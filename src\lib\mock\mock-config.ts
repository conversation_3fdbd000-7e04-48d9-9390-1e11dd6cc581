/**
 * Mock配置管理
 * 
 * 功能：
 * 1. 环境变量控制Mock模式开关
 * 2. Mock行为配置（延迟、错误率、场景等）
 * 3. 开发/测试环境适配
 * 4. 运行时配置管理
 * 
 * 设计原则：
 * - 类型安全的配置管理
 * - 支持运行时动态调整
 * - 与现有架构无缝集成
 * - 生产环境安全保护
 */

import { ensureNotProduction } from '@/lib/utils/environment-security'

// ============================================================================
// Mock配置类型定义
// ============================================================================

/**
 * Mock模式配置
 */
export interface MockModeConfig {
  /** 是否启用Mock模式 */
  enabled: boolean
  /** 强制Mock模式（忽略环境变量） */
  forceEnabled?: boolean
  /** 默认场景ID */
  defaultScenario: string
  /** 是否启用场景切换 */
  allowScenarioSwitching: boolean
  /** 是否启用错误注入 */
  allowErrorInjection: boolean
  /** 是否启用开发者工具 */
  showDevTools: boolean
}

/**
 * Mock行为配置
 */
export interface MockBehaviorConfig {
  /** 基础响应延迟（毫秒） */
  baseDelay: number
  /** 延迟变化范围（毫秒） */
  delayVariance: number
  /** 错误注入概率（0-1） */
  errorRate: number
  /** 断线重连概率（0-1） */
  reconnectRate: number
  /** 自动回复是否启用 */
  autoReply: boolean
  /** 流式响应块大小 */
  streamingChunkSize: number
  /** 流式响应间隔（毫秒） */
  streamingInterval: number
}

/**
 * Mock数据配置
 */
export interface MockDataConfig {
  /** 是否持久化Mock数据 */
  persistData: boolean
  /** 最大会话数量 */
  maxSessions: number
  /** 最大消息数量（每个会话） */
  maxMessagesPerSession: number
  /** 数据清理间隔（毫秒） */
  cleanupInterval: number
  /** 预加载场景列表 */
  preloadScenarios: string[]
}

/**
 * Mock日志配置
 */
export interface MockLoggingConfig {
  /** 日志级别 */
  level: 'debug' | 'info' | 'warn' | 'error'
  /** 是否启用详细日志 */
  verbose: boolean
  /** 是否记录消息内容 */
  logMessageContent: boolean
  /** 最大日志条数 */
  maxLogEntries: number
}

/**
 * 完整Mock配置
 */
export interface MockConfig {
  mode: MockModeConfig
  behavior: MockBehaviorConfig
  data: MockDataConfig
  logging: MockLoggingConfig
}

// ============================================================================
// 默认配置
// ============================================================================

const DEFAULT_MOCK_CONFIG: MockConfig = {
  mode: {
    enabled: false,
    forceEnabled: false,
    defaultScenario: 'streaming-fast',
    allowScenarioSwitching: true,
    allowErrorInjection: true,
    showDevTools: process.env.NODE_ENV === 'development',
  },

  behavior: {
    baseDelay: 500,
    delayVariance: 200,
    errorRate: 0.05,
    reconnectRate: 0.9,
    autoReply: true,
    streamingChunkSize: 3,
    streamingInterval: 100,
  },

  data: {
    persistData: true,
    maxSessions: 50,
    maxMessagesPerSession: 1000,
    cleanupInterval: 60000, // 1分钟
    preloadScenarios: ['streaming-fast', 'streaming-slow', 'form-simple', 'report-short'],
  },

  logging: {
    level: process.env.NODE_ENV === 'development' ? 'debug' : 'warn',
    verbose: process.env.NODE_ENV === 'development',
    logMessageContent: process.env.NODE_ENV === 'development',
    maxLogEntries: 1000,
  },
}

// ============================================================================
// 环境变量解析
// ============================================================================

/**
 * 从环境变量解析Mock配置
 */
function parseEnvConfig(): Partial<MockConfig> {
  const config: Partial<MockConfig> = {}

  // Mock模式配置
  if (process.env.NEXT_PUBLIC_MOCK_MODE === 'true') {
    config.mode = {
      ...DEFAULT_MOCK_CONFIG.mode,
      enabled: true,
    }
  }

  if (process.env.NEXT_PUBLIC_MOCK_FORCE_ENABLED === 'true') {
    config.mode = {
      ...config.mode,
      ...DEFAULT_MOCK_CONFIG.mode,
      forceEnabled: true,
    }
  }

  if (process.env.NEXT_PUBLIC_MOCK_DEFAULT_SCENARIO) {
    config.mode = {
      ...config.mode,
      ...DEFAULT_MOCK_CONFIG.mode,
      defaultScenario: process.env.NEXT_PUBLIC_MOCK_DEFAULT_SCENARIO,
    }
  }

  // Mock行为配置
  if (process.env.NEXT_PUBLIC_MOCK_BASE_DELAY) {
    const baseDelay = parseInt(process.env.NEXT_PUBLIC_MOCK_BASE_DELAY, 10)
    if (!isNaN(baseDelay)) {
      config.behavior = {
        ...config.behavior,
        ...DEFAULT_MOCK_CONFIG.behavior,
        baseDelay,
      }
    }
  }

  if (process.env.NEXT_PUBLIC_MOCK_ERROR_RATE) {
    const errorRate = parseFloat(process.env.NEXT_PUBLIC_MOCK_ERROR_RATE)
    if (!isNaN(errorRate)) {
      config.behavior = {
        ...config.behavior,
        ...DEFAULT_MOCK_CONFIG.behavior,
        errorRate: Math.max(0, Math.min(1, errorRate)),
      }
    }
  }

  // Mock日志配置
  if (process.env.NEXT_PUBLIC_MOCK_LOG_LEVEL) {
    const level = process.env.NEXT_PUBLIC_MOCK_LOG_LEVEL as MockLoggingConfig['level']
    if (['debug', 'info', 'warn', 'error'].includes(level)) {
      config.logging = {
        ...config.logging,
        ...DEFAULT_MOCK_CONFIG.logging,
        level,
      }
    }
  }

  return config
}

// ============================================================================
// Mock配置管理器
// ============================================================================

export class MockConfigManager {
  private static instance: MockConfigManager
  private config: MockConfig
  private listeners: Set<(config: MockConfig) => void> = new Set()

  private constructor() {
    // 合并默认配置和环境变量配置
    const envConfig = parseEnvConfig()
    this.config = this.mergeConfigs(DEFAULT_MOCK_CONFIG, envConfig)

    if (process.env.NODE_ENV === 'development') {
      console.log('🔧 Mock配置初始化完成:', {
        mockEnabled: this.config.mode.enabled,
        defaultScenario: this.config.mode.defaultScenario,
        baseDelay: this.config.behavior.baseDelay,
        errorRate: this.config.behavior.errorRate,
      })
    }
  }

  /**
   * 获取单例实例
   */
  static getInstance(): MockConfigManager {
    if (!MockConfigManager.instance) {
      MockConfigManager.instance = new MockConfigManager()
    }
    return MockConfigManager.instance
  }

  /**
   * 获取当前配置
   */
  getConfig(): Readonly<MockConfig> {
    return { ...this.config }
  }

  /**
   * 更新配置
   */
  updateConfig(updates: Partial<MockConfig>): void {
    ensureNotProduction('updateMockConfig', { updates })

    const newConfig = this.mergeConfigs(this.config, updates)
    this.config = newConfig

    // 通知所有监听器
    this.listeners.forEach(listener => {
      try {
        listener(newConfig)
      } catch (error) {
        console.error('Mock配置监听器执行失败:', error)
      }
    })

    console.log('🔧 Mock配置已更新:', updates)
  }

  /**
   * 监听配置变化
   */
  onConfigChange(listener: (config: MockConfig) => void): () => void {
    this.listeners.add(listener)
    
    // 返回取消监听的函数
    return () => {
      this.listeners.delete(listener)
    }
  }

  /**
   * 重置为默认配置
   */
  resetToDefault(): void {
    ensureNotProduction('resetMockConfig')
    
    this.updateConfig(DEFAULT_MOCK_CONFIG)
    console.log('🔧 Mock配置已重置为默认值')
  }

  /**
   * 检查是否应该启用Mock模式
   */
  shouldEnableMockMode(): boolean {
    const { enabled, forceEnabled } = this.config.mode
    
    // 强制启用
    if (forceEnabled) {
      return true
    }

    // 生产环境下不启用
    if (process.env.NODE_ENV === 'production') {
      return false
    }

    // 检查配置
    return enabled
  }

  /**
   * 获取随机延迟
   */
  getRandomDelay(): number {
    const { baseDelay, delayVariance } = this.config.behavior
    const variance = Math.random() * delayVariance - delayVariance / 2
    return Math.max(0, baseDelay + variance)
  }

  /**
   * 检查是否应该注入错误
   */
  shouldInjectError(): boolean {
    return Math.random() < this.config.behavior.errorRate
  }

  /**
   * 检查是否应该重连
   */
  shouldReconnect(): boolean {
    return Math.random() < this.config.behavior.reconnectRate
  }

  /**
   * 合并配置对象
   */
  private mergeConfigs(base: MockConfig, updates: Partial<MockConfig>): MockConfig {
    return {
      mode: { ...base.mode, ...updates.mode },
      behavior: { ...base.behavior, ...updates.behavior },
      data: { ...base.data, ...updates.data },
      logging: { ...base.logging, ...updates.logging },
    }
  }

  /**
   * 导出配置
   */
  exportConfig(): string {
    ensureNotProduction('exportMockConfig')
    return JSON.stringify(this.config, null, 2)
  }

  /**
   * 导入配置
   */
  importConfig(configJson: string): void {
    ensureNotProduction('importMockConfig')
    
    try {
      const importedConfig = JSON.parse(configJson) as Partial<MockConfig>
      this.updateConfig(importedConfig)
      console.log('🔧 Mock配置导入成功')
    } catch (error) {
      console.error('Mock配置导入失败:', error)
      throw new Error('配置格式无效')
    }
  }
}

// ============================================================================
// 便捷工具函数
// ============================================================================

/**
 * 获取Mock配置管理器实例
 */
export const mockConfig = MockConfigManager.getInstance()

/**
 * 检查是否启用Mock模式
 */
export function isMockModeEnabled(): boolean {
  return mockConfig.shouldEnableMockMode()
}

/**
 * 获取Mock配置
 */
export function getMockConfig(): Readonly<MockConfig> {
  return mockConfig.getConfig()
}

/**
 * 更新Mock配置
 */
export function updateMockConfig(updates: Partial<MockConfig>): void {
  mockConfig.updateConfig(updates)
}

/**
 * 监听Mock配置变化
 */
export function onMockConfigChange(listener: (config: MockConfig) => void): () => void {
  return mockConfig.onConfigChange(listener)
}

// ============================================================================
// React Hook
// ============================================================================

import { useState, useEffect } from 'react'

/**
 * 使用Mock配置的React Hook
 */
export function useMockConfig() {
  const [config, setConfig] = useState<MockConfig>(mockConfig.getConfig())

  useEffect(() => {
    const unsubscribe = mockConfig.onConfigChange(setConfig)
    return unsubscribe
  }, [])

  const updateConfig = (updates: Partial<MockConfig>) => {
    mockConfig.updateConfig(updates)
  }

  const resetConfig = () => {
    mockConfig.resetToDefault()
  }

  const shouldEnableMock = mockConfig.shouldEnableMockMode()

  return {
    config,
    updateConfig,
    resetConfig,
    shouldEnableMock,
    exportConfig: () => mockConfig.exportConfig(),
    importConfig: (json: string) => mockConfig.importConfig(json),
  }
}