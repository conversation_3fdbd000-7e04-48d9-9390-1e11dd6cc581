{"name": "vibe-coding-refactory", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev  --turbo", "dev:debug": "set NEXT_TELEMETRY_DISABLED=1 && set NODE_OPTIONS=--max-old-space-size=8192 --no-deprecation && next dev", "dev:development": "cross-env NEXT_PUBLIC_APP_ENV=development next dev --turbo", "dev:production": "cross-env NEXT_PUBLIC_APP_ENV=production next dev --turbo", "build": "cross-env NEXT_TELEMETRY_DISABLED=1 NODE_OPTIONS=\"--max-old-space-size=8192 --no-deprecation\" next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit --incremental", "type-check:watch": "tsc --noEmit --incremental --watch", "format": "prettier --write .", "format:check": "prettier --check .", "clean": "rimraf .next && rimraf dist && rimraf build", "clean:cache": "rimraf .next/cache && rimraf node_modules/.cache", "prepare": "husky", "pre-commit": "lint-staged", "commit-msg": "commitlint --edit", "validate": "npm run type-check && npm run lint && npm run format:check", "dev:inspect": "cross-env NODE_OPTIONS=\"--inspect\" next dev", "dev:inspect:turbo": "cross-env NODE_OPTIONS=\"--inspect\" next dev --turbo", "dev:api-debug": "cross-env DEBUG=\"api:*\" NODE_OPTIONS=\"--inspect\" next dev", "dev:middleware-debug": "cross-env DEBUG=\"middleware:*\" NODE_OPTIONS=\"--inspect\" next dev"}, "dependencies": {"@daveyplate/better-auth-ui": "^2.0.12", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@sentry/nextjs": "^9.35.0", "@tanstack/react-query": "^5.81.5", "@tanstack/react-query-devtools": "^5.81.5", "@types/highlight.js": "^9.12.4", "@types/markdown-it": "^14.1.2", "@types/pg": "^8.15.4", "@types/react-window": "^1.8.8", "axios": "^1.10.0", "better-auth": "^1.2.12", "better-sqlite3": "^12.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^11.18.2", "highlight.js": "^11.11.1", "immer": "^10.1.1", "import-in-the-middle": "^1.14.2", "lodash-es": "^4.17.21", "lucide-react": "^0.525.0", "markdown-it": "14.1.0", "markdown-it-abbr": "^2.0.0", "markdown-it-anchor": "^9.2.0", "markdown-it-container": "^4.0.0", "markdown-it-deflist": "^3.0.0", "markdown-it-emoji": "^3.0.0", "markdown-it-footnote": "^4.0.0", "markdown-it-ins": "^4.0.0", "markdown-it-mark": "^4.0.0", "markdown-it-sub": "^2.0.0", "markdown-it-sup": "^2.0.0", "markdown-it-task-lists": "^2.1.1", "motion": "^12.23.5", "nanoid": "^5.1.5", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "pg": "^8.16.3", "react": "^18.3.1", "react-day-picker": "^9.8.0", "react-dom": "^18.3.1", "react-fast-compare": "^3.2.2", "react-hook-form": "^7.60.0", "react-intersection-observer": "^9.16.0", "react-markdown": "^10.1.0", "react-pdf": "^10.0.1", "react-use": "^17.6.0", "react-virtualized-auto-sizer": "^1.0.26", "react-window": "^1.8.11", "require-in-the-middle": "^7.5.2", "socket.io-client": "^4.8.1", "sonner": "^2.0.5", "tailwind-merge": "^2.6.0", "use-deep-compare-effect": "^1.8.1", "vaul": "^1.1.2", "zod": "^4.0.5", "zustand": "^5.0.5"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@tailwindcss/postcss": "^4.0.0", "@tailwindcss/typography": "^0.5.16", "@testing-library/react": "^16.3.0", "@types/better-sqlite3": "^7.6.13", "@types/jest": "^30.0.0", "@types/lodash-es": "^4.17.12", "@types/markdown-it-container": "^2.0.10", "@types/node": "^22.10.2", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "cross-env": "^7.0.3", "eslint": "^8.57.1", "eslint-config-next": "15.3.3", "eslint-config-prettier": "^9.1.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "next": "15.3.4", "postcss": "^8.5.1", "prettier": "^3.4.2", "tailwindcss": "^4.0.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.4", "typescript": "^5.7.2"}}