/**
 * 测试无限循环修复
 * 
 * 这个文件用于测试我们修复的无限循环问题
 */

import React from 'react'
import { useSessionMessages, usePendingMessages, useMessage } from './src/stores/unified-chat-store'

// 测试组件 - 使用修复后的hooks
export const TestInfiniteLoopFix: React.FC = () => {
  console.log('🔄 TestInfiniteLoopFix渲染开始')

  // 测试selector hooks缓存
  const sessionMessages1 = useSessionMessages('test-session-1')
  const sessionMessages2 = useSessionMessages('test-session-1') // 相同参数，应该使用缓存
  const sessionMessages3 = useSessionMessages('test-session-2') // 不同参数，创建新selector
  
  const pendingMessages1 = usePendingMessages('test-session-1')
  const pendingMessages2 = usePendingMessages('test-session-1') // 相同参数，应该使用缓存
  
  const message1 = useMessage('test-message-1')
  const message2 = useMessage('test-message-1') // 相同参数，应该使用缓存

  console.log('📊 Hooks调用完成', {
    sessionMessages1: sessionMessages1.length,
    sessionMessages2: sessionMessages2.length,
    sessionMessages3: sessionMessages3.length,
    pendingMessages1: pendingMessages1.length,
    pendingMessages2: pendingMessages2.length,
    message1: message1?.id,
    message2: message2?.id,
  })

  // 验证相同参数的hooks返回相同引用（缓存生效）
  React.useEffect(() => {
    console.log('🔍 验证selector缓存:', {
      'sessionMessages缓存生效': sessionMessages1 === sessionMessages2,
      'sessionMessages不同参数': sessionMessages1 !== sessionMessages3,
      'pendingMessages缓存生效': pendingMessages1 === pendingMessages2,
      'message缓存生效': message1 === message2,
    })
  })

  console.log('✅ TestInfiniteLoopFix渲染完成')

  return (
    <div className="p-4 border rounded-lg">
      <h3 className="text-lg font-semibold mb-2">无限循环修复测试</h3>
      <div className="space-y-2 text-sm">
        <p>Session 1 消息数: {sessionMessages1.length}</p>
        <p>Session 2 消息数: {sessionMessages3.length}</p>
        <p>待发送消息数: {pendingMessages1.length}</p>
        <p>测试消息ID: {message1?.id || 'null'}</p>
      </div>
      <div className="mt-4 text-xs text-gray-500">
        检查控制台日志以验证selector缓存是否生效
      </div>
    </div>
  )
}

export default TestInfiniteLoopFix