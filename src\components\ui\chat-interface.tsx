'use client'

import { useRef, useState, useCallback, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'

// 组件导入
import { UnifiedMessage } from './unified-message'
import { StreamingMessageBubble } from './streaming-message-bubble'
import { adaptWebSocketMessage, isStreamingMessage } from './message-adapter'
import { AIChatInput } from './ai-chat-input'
import { TypingIndicator } from './typing-indicator'
import { ShortcutsHelpDialog } from './shortcuts-help-dialog'
import { useSession } from '@/components/providers/session-provider'
import { useSimpleScroll } from '@/hooks/use-simple-scroll'
import { useKeyboardShortcuts } from '@/hooks/use-keyboard-shortcuts'
import { useMobileOptimization } from '@/hooks/use-mobile-optimization'

// Shadcn UI 组件
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
// import { Card, CardContent } from '@/components/ui/card' // Unused imports
import { ScrollArea } from '@/components/ui/scroll-area'
import { Wifi, WifiOff, ArrowDown, Settings, Loader2, AlertCircle, Send } from 'lucide-react'

// Store和类型导入
import { useChatStore, useChatConnection, useChatMessages, useChatInput } from '@/stores/chat-store'
import { useSSRChat, HydratedOnly } from '@/components/chat/ssr-chat-provider'
import { ConnectionStatus } from '@/lib/connection/types'
import { isStreamingPayload, type ClientMessage } from '@/types/websocket-event-type'

export interface ChatInterfaceProps {
  /** 初始消息（SSR支持） */
  initialMessages?: ClientMessage[]
  /** 会话统计（SSR支持） */
  sessionStats?: {
    totalMessages: number
    userMessages: number
    assistantMessages: number
    lastMessageTime: number
  }
  /** 显示模式 */
  mode?: 'embed' | 'standalone'
  /** 主题 */
  theme?: 'light' | 'dark' | 'auto'
  /** 语言 */
  locale?: string
  /** 是否显示头像 */
  showAvatar?: boolean
  /** 是否显示时间戳 */
  showTimestamp?: boolean
  /** 消息渲染模式 */
  renderMode?: 'bubble' | 'card' | 'system'
  /** 用户头像URL */
  userAvatarUrl?: string
  /** 助手头像URL */
  assistantAvatarUrl?: string
  /** 是否启用文件上传 */
  enableFileUpload?: boolean
  /** 是否启用语音输入 */
  enableVoiceInput?: boolean
  /** 是否启用思考模式 */
  enableThinkMode?: boolean
  /** 是否启用深度搜索 */
  enableDeepSearch?: boolean
  /** 自定义样式 */
  className?: string
  /** 输入占位符 */
  placeholder?: string
  /** 最大输入行数 */
  maxInputRows?: number
}

export const ChatInterface = ({
  initialMessages = [],
  sessionStats,
  mode = 'standalone',
  theme = 'auto',
  locale = 'zh',
  showAvatar = true,
  showTimestamp = true, // 🎯 默认显示时间戳，匹配Figma设计
  renderMode = 'card',
  userAvatarUrl,
  assistantAvatarUrl,
  enableFileUpload = false,
  enableVoiceInput = false,
  enableThinkMode = false,
  enableDeepSearch = false,
  className,
  placeholder = '输入消息...',
  maxInputRows = 5,
}: ChatInterfaceProps) => {
  // SSR安全的chat context
  const ssrChat = useSSRChat()
  
  // Store状态 - 优先使用SSR安全的状态
  const { connectionStatus } = useChatConnection()
  const { sendMessage, deleteMessage } = useChatMessages()
  const { userInput, updateUserInput } = useChatInput()
  const { user: _user } = useSession() // 获取当前用户信息

  // 🎯 SSR兼容的消息获取 - 在hydration之前使用初始消息
  const currentSession = useChatStore(state => state.currentSession)
  const currentGroupId = useChatStore(state => state.currentGroupId)
  
  // 合并初始消息和实时消息
  const displayMessages = useMemo(() => {
    if (ssrChat.isHydrated) {
      return ssrChat.messages
    }
    return initialMessages
  }, [ssrChat.isHydrated, ssrChat.messages, initialMessages])

  const retryMessage = async (messageId: string) => {
    // TODO: 实现重试逻辑
    // eslint-disable-next-line no-console
    console.log('Retry message:', messageId)
  }
  const isConnected = connectionStatus === ConnectionStatus.CONNECTED

  // 本地状态
  const [thinkMode, setThinkMode] = useState(false)
  const [deepSearch, setDeepSearch] = useState(false)
  const [isTyping, setIsTyping] = useState(false)
  const [showShortcutsHelp, setShowShortcutsHelp] = useState(false)

  // Refs
  const scrollAreaRef = useRef<HTMLDivElement>(null)
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLTextAreaElement>(null)

  // 🎯 SSR友好的消息获取逻辑 - 防止hydration不匹配
  const sessionMessages = useMemo(() => {
    // 🔍 简洁的状态检查 (仅在开发环境)
    if (process.env.NODE_ENV === 'development') {
      // eslint-disable-next-line no-console
      console.log('📋 CHAT_INTERFACE_SSR_UPDATE:', {
        groupId: currentGroupId,
        sessionId: currentSession?.sessionId,
        hasSession: !!currentSession,
        messageCount: displayMessages.length,
        isHydrated: ssrChat.isHydrated
      })
    }

    return displayMessages
  }, [displayMessages, currentSession, currentGroupId, ssrChat.isHydrated])

  // 🎯 检测是否有streaming消息
  const hasStreamingMessage = useMemo(() => {
    const lastMessage = sessionMessages[sessionMessages.length - 1]
    return (
      lastMessage &&
      isStreamingPayload(lastMessage.payload) &&
      lastMessage.metadata?.streamingState &&
      !lastMessage.metadata.streamingState.isComplete
    )
  }, [sessionMessages])

  // 🎯 移动端优化Hook
  const { isMobile, screenSize, isKeyboardVisible, viewportHeight } = useMobileOptimization({
    enableViewportLock: true,
    enableKeyboardAware: true,
    enableTouchFeedback: true,
    debug: process.env.NODE_ENV === 'development',
  })

  // 🎯 使用简化滚动Hook - 统一管理所有滚动行为
  const { isAtBottom, unreadCount, scrollToBottom, handleScroll } = useSimpleScroll({
    containerRef: scrollAreaRef,
    endRef: messagesEndRef,
    messageCount: sessionMessages.length,
    hasStreamingMessage: hasStreamingMessage || false,
    threshold: 100,
    debug: process.env.NODE_ENV === 'development',
  })

  // 🎯 快捷键支持的辅助函数
  const focusInput = useCallback(() => {
    inputRef.current?.focus()
  }, [])

  const clearInput = useCallback(() => {
    updateUserInput('')
    focusInput()
  }, [updateUserInput, focusInput])

  const clearChat = useCallback(() => {
    if (currentSession && sessionMessages.length > 0) {
      // 确认清除对话
      if (window.confirm('确定要清除当前对话记录吗？此操作无法撤销。')) {
        // 清空当前会话的消息
        useChatStore.setState(state => {
          if (state.currentSession) {
            return {
              ...state,
              currentSession: {
                ...state.currentSession,
                messages: [],
              },
            }
          }
          return state
        })

        // 聚焦到输入框
        focusInput()
      }
    }
  }, [currentSession, sessionMessages.length, focusInput])

  // 注释掉原有的WebSocket连接逻辑，改为使用Mock适配器
  // useEffect(() => {
  //   if (!isConnected && sessionId) {
  //     connect({
  //       url: '/', // 连接到同域API路由
  //       auth: {
  //         userId: 'current-user', // 从认证状态获取
  //         organizationId: 'current-org' // 从认证状态获取
  //       }
  //     })
  //   }

  //   // 清理函数
  //   return () => {
  //     // 可选择性断开连接
  //   }
  // }, [sessionId, isConnected, connect])

  // 🎯 智能typing状态管理
  const managingTypingState = useCallback(() => {
    // 当用户发送消息后显示typing indicator
    setIsTyping(true)

    // 设置超时，如果10秒内没有收到回复则隐藏
    const typingTimeout = setTimeout(() => {
      setIsTyping(false)
    }, 10000)

    return () => clearTimeout(typingTimeout)
  }, [])

  // 监听新消息，隐藏typing indicator
  const lastMessageRef = useRef<string>('')
  const currentLastMessageId = sessionMessages[sessionMessages.length - 1]?.id || ''

  if (currentLastMessageId !== lastMessageRef.current && currentLastMessageId) {
    lastMessageRef.current = currentLastMessageId
    // 收到新消息时隐藏typing indicator
    setIsTyping(false)
  }

  // 处理输入提交 - SSR兼容版本
  const handleSubmit = useCallback(async () => {
    if (!userInput.content.trim() || userInput.isSubmitting) return
    
    // 如果未完成hydration，使用SSR兼容的发送方法
    if (!ssrChat.isHydrated) {
      try {
        await ssrChat.sendMessage(userInput.content.trim())
        updateUserInput('')
      } catch (error) {
        console.error('SSR模式下发送消息失败:', error)
      }
      return
    }

    // Hydration完成后使用完整功能
    if (!currentSession) return

    const messageContent = userInput.content.trim()

    try {
      // 🎯 发送消息前启动typing状态
      const clearTyping = managingTypingState()

      await sendMessage({
        content: messageContent,
        sessionId: currentSession.sessionId,
        metadata: {
          thinkMode,
          deepSearch,
        },
      })

      // 清空输入
      updateUserInput('')

      // 滚动到底部
      setTimeout(() => scrollToBottom(), 100)

      // 清理typing状态管理
      clearTyping()
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('发送消息失败:', error)
      // 发送失败时也要隐藏typing indicator
      setIsTyping(false)
    }
  }, [
    userInput,
    sendMessage,
    currentSession,
    thinkMode,
    deepSearch,
    updateUserInput,
    scrollToBottom,
    managingTypingState,
    ssrChat
  ])

  // 🎯 集成键盘快捷键处理
  const { handleInputKeyDown, shortcuts } = useKeyboardShortcuts({
    enabled: true,
    onSend: handleSubmit,
    onClearInput: clearInput,
    onFocusInput: focusInput,
    onClearChat: clearChat,
    onShowHelp: () => setShowShortcutsHelp(true),
    debug: process.env.NODE_ENV === 'development',
  })

  // 处理键盘事件（保留兼容性，但优先使用快捷键处理）
  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
      // 先让快捷键处理器处理
      handleInputKeyDown(e)
    },
    [handleInputKeyDown]
  )

  // 连接状态指示器
  const getConnectionStatus = () => {
    switch (connectionStatus) {
      case ConnectionStatus.CONNECTED:
        return {
          icon: Wifi,
          text: '已连接',
          color: 'text-green-500',
          bgColor: 'bg-green-50',
        }
      case ConnectionStatus.CONNECTING:
        return {
          icon: Loader2,
          text: '连接中...',
          color: 'text-yellow-500',
          bgColor: 'bg-yellow-50',
        }
      case ConnectionStatus.DISCONNECTED:
        return {
          icon: WifiOff,
          text: '已断开',
          color: 'text-red-500',
          bgColor: 'bg-red-50',
        }
      case ConnectionStatus.ERROR:
        return {
          icon: AlertCircle,
          text: '连接错误',
          color: 'text-red-500',
          bgColor: 'bg-red-50',
        }
      default:
        return {
          icon: WifiOff,
          text: '未连接',
          color: 'text-gray-500',
          bgColor: 'bg-gray-50',
        }
    }
  }

  const statusInfo = getConnectionStatus()
  const StatusIcon = statusInfo.icon

  return (
    <div
      className={cn('flex flex-col h-full bg-background', className)}
      style={{
        // 📱 移动端键盘感知高度调整
        height: isMobile && isKeyboardVisible ? `${viewportHeight}px` : undefined,
      }}
    >
      {/* 开发环境调试面板 - 只在开发环境显示 */}
      {process.env.NODE_ENV === 'development' && (
        <div className="bg-yellow-50 border-b border-yellow-200 p-2 text-xs">
          <div className="flex gap-4 flex-wrap">
            <span>🔗 连接: {isConnected ? '✅' : '❌'}</span>
            <span>📡 状态: {connectionStatus}</span>
            <span>📝 提交中: {userInput.isSubmitting ? '✅' : '❌'}</span>
            <span>🔒 输入框禁用: {!isConnected || userInput.isSubmitting ? '✅' : '❌'}</span>
            <span>💬 内容长度: {userInput.content.length}</span>
            <span>📱 移动端: {isMobile ? '✅' : '❌'}</span>
            <span>📐 尺寸: {screenSize}</span>
            <span>⌨️ 键盘: {isKeyboardVisible ? '✅' : '❌'}</span>
          </div>
        </div>
      )}

      {/* 📱 移动端优化的顶部状态栏 */}
      <div className="flex items-center justify-between p-3 sm:p-4 border-b bg-background/80 backdrop-blur-sm">
        <div className="flex items-center space-x-2 sm:space-x-3">
          {/* 只在连接有问题时显示状态 */}
          {connectionStatus !== ConnectionStatus.CONNECTED && (
            <Badge
              variant="outline"
              className={cn('flex items-center space-x-1', statusInfo.bgColor)}
            >
              <StatusIcon className={cn('w-3 h-3', statusInfo.color)} />
              <span className={statusInfo.color}>{statusInfo.text}</span>
            </Badge>
          )}

          {currentSession && sessionMessages.length > 0 && (
            <span className="text-sm text-muted-foreground">对话进行中</span>
          )}
        </div>

        <div className="flex items-center space-x-1 sm:space-x-2">
          {/* 只在开发环境显示技术控制按钮 */}
          {process.env.NODE_ENV === 'development' && (
            <>
              {connectionStatus === ConnectionStatus.DISCONNECTED && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    // eslint-disable-next-line no-console
                    console.warn('重新连接按钮点击 - 使用Mock适配器处理连接')
                  }}
                >
                  重新连接
                </Button>
              )}
              <Button size="sm" variant="ghost">
                <Settings className="w-4 h-4" />
              </Button>
            </>
          )}
        </div>
      </div>

      {/* 消息列表区域 */}
      <div className="flex-1 relative overflow-hidden min-h-0">
        <ScrollArea className="h-full w-full" ref={scrollAreaRef} onScrollCapture={handleScroll}>
          <div className="p-3 sm:p-4 space-y-3 sm:space-y-4">
            {sessionMessages.map(message => {
              console.log('🚀 渲染消息:', message.id, message.payload?.type)
              // 🎯 优先使用新的StreamingMessageBubble组件处理streaming消息
              const isStreaming = isStreamingMessage(message)
              console.log('🔍 消息类型判断:', {
                messageId: message.id,
                isStreaming,
                payloadType: message.payload?.type,
                userId: message.userId,
              })

              if (isStreaming) {
                const bubbleProps = adaptWebSocketMessage(message, {
                  currentUserId: _user?.id,
                  showAvatar,
                  showTimestamp,
                  userAvatarUrl,
                  assistantAvatarUrl,
                  enableCopy: true,
                  onCopy: content => {
                    // 复制功能的回调 - 可以在这里添加toast提示等
                    // eslint-disable-next-line no-console
                    console.log('复制内容:', content)
                  },
                })

                if (bubbleProps) {
                  console.log('✅ 使用StreamingMessageBubble渲染消息:', message.id)
                  return <StreamingMessageBubble key={message.id} {...bubbleProps} />
                }
              }

              // 🔄 fallback到原有的UnifiedMessage组件处理其他类型消息
              return (
                <UnifiedMessage
                  key={message.id}
                  message={message}
                  showAvatar={showAvatar}
                  showTimestamp={showTimestamp}
                  showStatus={true}
                  renderMode={renderMode}
                  userAvatarUrl={userAvatarUrl}
                  assistantAvatarUrl={assistantAvatarUrl}
                  enableCopy={false}
                  enableMenu={false}
                  onRetry={messageId => retryMessage(messageId)}
                  onDelete={messageId => deleteMessage(messageId)}
                  onErrorRetry={(_errorId: string) => retryMessage(message.id)}
                  onErrorDismiss={(_errorId: string) => deleteMessage(message.id)}
                />
              )
            })}

            {/* 优化的打字指示器 */}
            <AnimatePresence>
              <TypingIndicator
                visible={isTyping}
                name="AI助手"
                avatarUrl={assistantAvatarUrl || undefined}
                mode={renderMode === 'bubble' ? 'bubble' : 'bubble'}
              />
            </AnimatePresence>
          </div>

          {/* 滚动锚点 */}
          <div ref={messagesEndRef} />
        </ScrollArea>

        {/* 📱 移动端优化的未读消息提示 */}
        <AnimatePresence>
          {!isAtBottom && unreadCount > 0 && (
            <motion.div
              className="absolute bottom-16 sm:bottom-4 left-1/2 transform -translate-x-1/2 z-10"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
            >
              <Button
                size="sm"
                className="rounded-full shadow-lg text-xs sm:text-sm px-3 sm:px-4 h-8 sm:h-9"
                onClick={() => scrollToBottom()}
              >
                <ArrowDown className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                <span className="hidden xs:inline">{unreadCount} 条</span>
                <span className="xs:hidden">{unreadCount}</span>
                <span className="hidden sm:inline">新消息</span>
              </Button>
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      {/* 📱 移动端优化的输入区域 */}
      <div className="border-t bg-background/80 backdrop-blur-sm p-3 sm:p-4 pb-safe">
        <div className="max-w-4xl mx-auto">
          <div className="flex items-end space-x-2 sm:space-x-3">
            <div className="flex-1">
              <HydratedOnly
                fallback={
                  <div className="flex gap-2">
                    <div className="flex-1 h-12 bg-gray-100 rounded-lg animate-pulse" />
                    <div className="w-12 h-12 bg-gray-100 rounded-lg animate-pulse" />
                  </div>
                }
              >
                <AIChatInput
                  ref={inputRef}
                  value={userInput.content}
                  onChange={e => updateUserInput(e.target.value)}
                  onSubmit={handleSubmit}
                  onKeyDown={handleKeyDown}
                  onAttachClick={enableFileUpload ? () => {} : undefined}
                  onMicClick={enableVoiceInput ? () => {} : undefined}
                  onThinkChange={enableThinkMode ? setThinkMode : undefined}
                  onDeepSearchChange={enableDeepSearch ? setDeepSearch : undefined}
                  placeholder={placeholder}
                  disabled={(!isConnected || userInput.isSubmitting) && ssrChat.isHydrated}
                  thinkActive={thinkMode}
                  deepSearchActive={deepSearch}
                  showControls={
                    enableFileUpload || enableVoiceInput || enableThinkMode || enableDeepSearch
                  }
                  maxRows={maxInputRows}
                  autoFocus={true}
                />
              </HydratedOnly>
            </div>

            {/* <Button
              size="default"
              onClick={handleSubmit}
              disabled={!userInput.content.trim() || !isConnected || userInput.isSubmitting}
              className="h-12 w-12 rounded-full flex-shrink-0"
            >
              {userInput.isSubmitting ? (
                <Loader2 className="w-5 h-5 animate-spin" />
              ) : (
                <Send className="w-5 h-5" />
              )}
            </Button> */}
          </div>
        </div>
      </div>

      {/* 🎯 快捷键帮助对话框 */}
      <ShortcutsHelpDialog
        open={showShortcutsHelp}
        onOpenChange={setShowShortcutsHelp}
        shortcuts={shortcuts}
      />
    </div>
  )
}

// 使用 ChatStore 的便捷 Hook
