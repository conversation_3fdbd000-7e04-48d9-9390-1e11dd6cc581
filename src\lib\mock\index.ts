/**
 * Mock系统统一导出文件
 * 
 * 这个文件提供了完整Mock系统的统一访问入口，包括：
 * 1. 智能Mock数据生成
 * 2. Mock WebSocket Provider
 * 3. 开发环境集成
 * 4. Mock数据管理
 * 5. 开发者工具
 * 
 * 使用方式：
 * ```typescript
 * import { 
 *   MockWebSocketProvider, 
 *   useMockWebSocket,
 *   MockControlPanel,
 *   mockConfig,
 *   mockDataManager 
 * } from '@/lib/mock'
 * ```
 */

// ============================================================================
// 核心Mock系统
// ============================================================================

// Mock配置管理
export {
  getMockConfig, isMockModeEnabled, mockConfig, MockConfigManager, onMockConfigChange, updateMockConfig, useMockConfig
} from './mock-config'

// Mock数据管理
export {
  mockData, MockDataManager, mockDataManager
} from './mock-data-manager'

// 增强Mock连接
export {
  EnhancedMockConnection
} from './enhanced-mock-connection'

// Mock WebSocket Provider
export {
  MockWebSocketProvider, useIsMockMode, useMockDataManager,
  useMockDevTools, useMockScenarios, useMockWebSocket
} from './mock-websocket-provider'

// ============================================================================
// Mock场景系统
// ============================================================================

// 基础场景
export {
  MockScenarios,
  ScenarioManager
} from './mock-scenarios'

// 增强场景
export {
  EnhancedMockScenarios, EnhancedScenarioManager, MockScenariosEnhanced, ScenarioManagerEnhanced
} from './mock-scenarios-enhanced'

// Mock数据工厂
export {
  MOCK_AI_ASSISTANT_ID, MockDataFactory
} from './mock-data-factory'

// ============================================================================
// 开发者工具
// ============================================================================

// 注意：开发者工具组件由于JSX配置问题，需要在需要时单独导入
// 可以通过以下方式导入：
// import { MockControlPanel } from '@/components/dev/mock-control-panel'
// import { MockBestPracticesGuide } from '@/lib/mock/mock-integration-guide'

// ============================================================================
// 便捷工具函数
// ============================================================================

/**
 * 快速启用Mock模式
 * 适用于开发环境的快速设置
 */
export async function enableMockMode(scenario: string = 'streaming-fast') {
  const { mockConfig } = await import('./mock-config')
  
  mockConfig.updateConfig({
    mode: {
      enabled: true,
      defaultScenario: scenario,
      allowScenarioSwitching: true,
      allowErrorInjection: false,
      showDevTools: true,
    }
  })
  
  console.log('Mock模式已启用，默认场景:', scenario)
}

/**
 * 快速生成演示数据
 * 用于快速创建演示或测试数据
 */
export async function generateDemoData() {
  const { mockDataManager } = await import('./mock-data-manager')
  
  // 生成用户数据
  mockDataManager.createUser({
    id: 'demo-user-1',
    name: '演示用户',
    role: 'user',
    organizationId: 'demo-org',
  })

  // 生成会话和对话数据
  const sessionId = `demo-session-${Date.now()}`
  mockDataManager.createSession({
    sessionId,
    organizationId: 'demo-org',
  })

  const messages = mockDataManager.generateConversationData(10, sessionId)
  console.log('🎭 演示数据生成完成:', {
    sessionId,
    messageCount: messages.length,
  })

  return { sessionId, messages }
}

/**
 * 快速配置开发环境
 * 为开发者提供最佳的开发体验配置
 */
export function setupDevelopmentConfig() {
  const { mockConfig } = require('./mock-config')
  
  mockConfig.updateConfig({
    mode: {
      enabled: true,
      defaultScenario: 'streaming-fast',
      allowScenarioSwitching: true,
      allowErrorInjection: true,
      showDevTools: true,
    },
    behavior: {
      baseDelay: 300,
      delayVariance: 100,
      errorRate: 0.02,
      autoReply: true,
    },
    logging: {
      level: 'debug',
      verbose: true,
      logMessageContent: true,
    },
  })
  
  console.log('🎭 开发环境配置已应用')
}

/**
 * 快速配置测试环境
 * 为自动化测试提供最佳配置
 */
export function setupTestingConfig() {
  const { mockConfig } = require('./mock-config')
  
  mockConfig.updateConfig({
    mode: {
      enabled: true,
      defaultScenario: 'streaming-fast',
      allowScenarioSwitching: true,
      allowErrorInjection: false,
      showDevTools: false,
    },
    behavior: {
      baseDelay: 50,
      delayVariance: 10,
      errorRate: 0,
      autoReply: true,
    },
    logging: {
      level: 'warn',
      verbose: false,
      logMessageContent: false,
    },
  })
  
  console.log('🎭 测试环境配置已应用')
}

/**
 * 获取Mock系统状态
 * 用于调试和监控
 */
export function getMockSystemStatus() {
  try {
    const { mockConfig } = require('./mock-config')
    const { mockDataManager } = require('./mock-data-manager')
    
    const config = mockConfig.getConfig()
    const dataStats = mockDataManager.getStats()
    
    return {
      isEnabled: config.mode.enabled,
      currentScenario: config.mode.defaultScenario,
      connectionStatus: 'unknown', // 需要从连接中获取
      dataStats,
      config,
      timestamp: new Date().toISOString(),
    }
  } catch (error) {
    console.error('获取Mock系统状态失败:', error)
    return {
      isEnabled: false,
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date().toISOString(),
    }
  }
}

/**
 * 清理Mock系统
 * 用于测试清理或重置
 */
export function cleanupMockSystem() {
  try {
    const { mockDataManager } = require('./mock-data-manager')
    
    // 清理所有Mock数据
    mockDataManager.clearAll()
    
    console.log('🧹 Mock系统清理完成')
    return true
  } catch (error) {
    console.error('Mock系统清理失败:', error)
    return false
  }
}

// ============================================================================
// 类型导出
// ============================================================================

// 导出所有重要的类型定义
export type {
  // 核心配置类型
  MockBehaviorConfig,
  MockConfig,
  MockDataConfig,
  MockLoggingConfig,
  MockModeConfig
} from './mock-config'

export type {
  // 数据管理类型
  MockDatabase,
  MockDataStats,
  MockMessageItem,
  MockSession,
  MockUser
} from './mock-data-manager'

export type {
  // 连接类型
  ConnectionEvent,
  EnhancedMockConnectionConfig,
  MockConnectionStats
} from './enhanced-mock-connection'

export type {
  // WebSocket Provider类型
  MockWebSocketContextValue,
  MockWebSocketProviderProps
} from './mock-websocket-provider'

export type {
  // 场景类型
  MockScenario
} from './mock-scenarios'

export type {
  // 增强场景类型
  EnhancedMockScenario,
  ScenarioContext,
  ScenarioStep
} from './mock-scenarios-enhanced'

// ============================================================================
// 版本信息
// ============================================================================

export const MOCK_SYSTEM_VERSION = '1.0.0'
export const MOCK_SYSTEM_BUILD_DATE = new Date().toISOString()

/**
 * 获取Mock系统版本信息
 */
export function getMockSystemInfo() {
  return {
    version: MOCK_SYSTEM_VERSION,
    buildDate: MOCK_SYSTEM_BUILD_DATE,
    features: [
      '智能Mock数据生成',
      'Mock WebSocket Provider',
      '开发环境集成',
      'Mock数据管理',
      '开发者工具',
      '场景管理系统',
      '实时监控和调试',
      '环境安全保护',
    ],
    compatibility: {
      'unified-chat-store': '✅ 完全兼容',
      'ClientMessage': '✅ 完全兼容',
      'connection-store': '✅ 完全兼容',
      'React': '✅ 18+ 支持',
      'Next.js': '✅ 14+ 支持',
    },
  }
}

// ============================================================================
// 调试工具（仅开发环境）
// ============================================================================

if (process.env.NODE_ENV === 'development') {
  // 全局调试工具
  if (typeof window !== 'undefined') {
    ;(window as any).__MOCK_SYSTEM__ = {
      version: MOCK_SYSTEM_VERSION,
      enableMockMode,
      generateDemoData,
      setupDevelopmentConfig,
      setupTestingConfig,
      getMockSystemStatus,
      cleanupMockSystem,
      getMockSystemInfo,
    }
    
    console.log('🎭 Mock系统调试工具已加载到 window.__MOCK_SYSTEM__')
  }
}

// ============================================================================
// 默认导出
// ============================================================================

export default {
  // 工具函数
  enableMockMode,
  generateDemoData,
  setupDevelopmentConfig,
  setupTestingConfig,
  getMockSystemStatus,
  cleanupMockSystem,
  getMockSystemInfo,
  
  // 版本信息
  version: MOCK_SYSTEM_VERSION,
}