/**
 * Chat Provider - 统一的聊天系统上下文提供者
 * 
 * 🎯 设计目标：
 * 1. 作为组件的唯一依赖入口，简化开发体验
 * 2. 统一管理所有chat相关的状态和操作
 * 3. 提供类型安全的API接口
 * 4. 支持插件化扩展和消息类型注册
 * 
 * ⚡ 核心优势：
 * - 单一依赖：组件只需useChat()一个Hook
 * - 类型安全：完整的TypeScript支持
 * - 高性能：智能的状态优化和缓存
 * - 易扩展：注册机制支持快速功能开发
 */

'use client'

import * as React from 'react'
import { createContext, useContext, useMemo, useCallback, useEffect } from 'react'
import { shallow } from 'zustand/shallow'
import type { BaseWebSocketMessage as WebSocketMessage } from '@/types/websocket-event-type'
import type { ChatSession } from '@/stores/session-store'

// Store imports - 使用统一的Chat Store
import { 
  useMessageDataStore, 
  useMessageFlowStore, 
  useMessageUIStore,
  useSessionMessages, 
  usePendingMessages 
} from '@/stores/unified-chat-store'
import { useConnectionStore } from '@/stores/connection-store'
import { useSessionStore } from '@/stores/session-store'

// Registry import
import { chatRegistry, type ChatRegistry } from '@/lib/chat/chat-registry'

// ============================================================================
// 类型定义
// ============================================================================

/**
 * Chat API 统一接口
 */
export interface ChatAPI {
  // ============================================================================
  // 消息操作
  // ============================================================================
  
  /** 发送用户消息 */
  sendMessage: (content: string, options?: SendMessageOptions) => Promise<void>
  
  /** 重试失败的消息 */
  retryMessage: (messageId: string) => Promise<void>
  
  /** 更新消息内容 */
  updateMessage: (messageId: string, updates: Partial<WebSocketMessage>) => boolean
  
  /** 删除消息 */
  deleteMessage: (messageId: string) => boolean
  
  // ============================================================================
  // 消息查询
  // ============================================================================
  
  /** 获取当前会话的所有消息 */
  messages: WebSocketMessage[]
  
  /** 获取发送中的消息 */
  pendingMessages: PendingSentMessage[]
  
  /** 获取指定消息 */
  getMessage: (messageId: string) => WebSocketMessage | undefined
  
  /** 搜索消息 */
  searchMessages: (query: string) => WebSocketMessage[]
  
  // ============================================================================
  // 会话管理
  // ============================================================================
  
  /** 当前会话信息 */
  currentSession: ChatSession | null
  
  /** 创建新会话 */
  createSession: () => Promise<ChatSession>
  
  /** 切换会话 */
  switchSession: (sessionId: string) => Promise<void>
  
  /** 删除会话 */
  deleteSession: (sessionId: string) => void
  
  // ============================================================================
  // 连接状态
  // ============================================================================
  
  /** 连接状态 */
  connectionStatus: ConnectionStatus
  
  /** 是否已连接 */
  isConnected: boolean
  
  /** 连接错误信息 */
  connectionError: string | undefined
  
  /** 手动重连 */
  reconnect: () => Promise<void>
  
  // ============================================================================
  // UI状态和交互
  // ============================================================================
  
  /** 输入框状态 */
  input: {
    content: string
    isTyping: boolean
    isFocused: boolean
    updateContent: (content: string) => void
    clearContent: () => void
    setFocus: (focused: boolean) => void
  }
  
  /** 消息交互 */
  interaction: {
    selectedMessages: string[]
    editingMessage: string | null
    selectMessage: (messageId: string, multiple?: boolean) => void
    clearSelection: () => void
    startEdit: (messageId: string) => void
    endEdit: () => void
  }
  
  /** 界面设置 */
  interface: {
    theme: 'light' | 'dark' | 'auto'
    density: 'compact' | 'comfortable' | 'spacious'
    sidebarExpanded: boolean
    isMobile: boolean
    setTheme: (theme: 'light' | 'dark' | 'auto') => void
    setDensity: (density: 'compact' | 'comfortable' | 'spacious') => void
    toggleSidebar: () => void
  }
  
  // ============================================================================
  // 扩展系统
  // ============================================================================
  
  /** 消息注册表 */
  registry: ChatRegistry
  
  /** 注册新的消息类型 */
  registerMessageType: <T = any>(type: string, config: MessageTypeConfig<T>) => void
  
  /** 获取支持的消息类型 */
  getSupportedTypes: () => string[]
  
  // ============================================================================
  // 工具方法
  // ============================================================================
  
  /** 通知系统 */
  notify: {
    success: (message: string) => void
    error: (message: string) => void
    info: (message: string) => void
    warning: (message: string) => void
  }
  
  /** 获取诊断信息 */
  getDiagnostics: () => ChatDiagnostics
}

/**
 * 发送消息选项
 */
export interface SendMessageOptions {
  messageType?: string
  metadata?: Record<string, any>
  priority?: 'low' | 'normal' | 'high'
}

/**
 * 消息类型配置
 */
export interface MessageTypeConfig<T = any> {
  processor: {
    process: (message: WebSocketMessage) => T | Promise<T>
    validate?: (payload: any) => boolean
  }
  renderer: React.ComponentType<{
    message: WebSocketMessage
    onUpdate?: (messageId: string, updates: Partial<WebSocketMessage>) => void
  }>
  description?: string
}

/**
 * 发送中的消息（导入类型）
 */
export interface PendingSentMessage {
  tempId: string
  content: string
  timestamp: number
  sessionId: string
  userId: string
  status: 'pending' | 'sending' | 'sent' | 'failed'
}

/**
 * 连接状态（导入类型）
 */
export enum ConnectionStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  RECONNECTING = 'reconnecting',
  ERROR = 'error'
}

/**
 * 诊断信息
 */
export interface ChatDiagnostics {
  messages: {
    total: number
    pending: number
    sessions: number
  }
  connection: {
    status: ConnectionStatus
    type: string
    uptime: number
  }
  registry: {
    processors: number
    renderers: number
    types: string[]
  }
  performance: {
    memoryUsage: string
    renderTime: number
  }
}

// ============================================================================
// Context 创建
// ============================================================================

const ChatContext = createContext<ChatAPI | null>(null)

// ============================================================================
// Provider 组件
// ============================================================================

/**
 * 聊天系统配置选项
 */
export interface ChatSystemConfig {
  autoConnect?: boolean
  enableNotifications?: boolean
  maxRetries?: number
}

export interface ChatProviderProps {
  children: React.ReactNode
  /** 初始会话ID */
  initialSessionId?: string
  /** 启用调试模式 */
  enableDebug?: boolean
  /** 自定义配置 */
  config?: ChatSystemConfig
}

export const ChatProvider: React.FC<ChatProviderProps> = ({
  children,
  initialSessionId,
  enableDebug = process.env.NODE_ENV === 'development',
  config = {
    autoConnect: true,
    enableNotifications: true,
    maxRetries: 3
  }
}) => {
  // ============================================================================
  // Store状态订阅
  // ============================================================================
  
  // 静态selector函数 - 避免每次渲染时创建新函数
  const selectCurrentSession = useMemo(() => (state: any) => state.currentSession, [])
  const selectConnectionStatus = useMemo(() => (state: any) => state.connectionStatus, [])
  const selectIsConnected = useMemo(() => (state: any) => state.isConnected, [])
  const selectConnectionError = useMemo(() => (state: any) => state.lastError, [])
  const selectInputState = useMemo(() => (state: any) => state.input, [])
  const selectInteractionState = useMemo(() => (state: any) => state.interaction, [])
  const selectInterfaceState = useMemo(() => (state: any) => state.interface, [])
  
  // 消息数据
  const currentSession = useSessionStore(selectCurrentSession)
  const sessionId = useMemo(() => 
    currentSession?.sessionId ?? 'empty-session', 
    [currentSession?.sessionId]
  )
  const messages = useSessionMessages(sessionId)
  const pendingMessages = usePendingMessages(sessionId)
  
  // 连接状态
  const connectionStatus = useConnectionStore(selectConnectionStatus)
  const isConnected = useConnectionStore(selectIsConnected)
  const connectionError = useConnectionStore(selectConnectionError)
  
  // UI状态
  const inputState = useMessageUIStore(selectInputState)
  const interactionState = useMessageUIStore(selectInteractionState)
  const interfaceState = useMessageUIStore(selectInterfaceState)
  
  // ============================================================================
  // Store操作方法 - 使用稳定的selector缓存
  // ============================================================================
  
  // 创建稳定的selector函数 - 避免每次渲染创建新对象
  const selectMessageDataActions = useMemo(() => (state: any) => ({
    getMessage: state.getMessage,
    updateMessage: state.updateMessage,
    deleteMessage: state.deleteMessage,
    findMessages: state.findMessages
  }), [])
  
  const selectMessageFlowActions = useMemo(() => (state: any) => ({
    sendUserMessage: state.sendUserMessage,
    retryMessage: state.retryMessage
  }), [])
  
  const selectSessionActions = useMemo(() => (state: any) => ({
    createSession: state.createSession,
    setCurrentSession: state.setCurrentSession,
    clearSession: state.clearSession
  }), [])
  
  const selectConnectionActions = useMemo(() => (state: any) => ({
    connect: state.connect,
    disconnect: state.disconnect
  }), [])
  
  const selectUIActions = useMemo(() => (state: any) => ({
    updateInputContent: state.updateInputContent,
    clearInput: state.clearInput,
    setInputFocus: state.setInputFocus,
    selectMessage: state.selectMessage,
    clearSelection: state.clearSelection,
    startEditMessage: state.startEditMessage,
    endEditMessage: state.endEditMessage,
    setTheme: state.setTheme,
    setMessagesDensity: state.setMessagesDensity,
    toggleSidebar: state.toggleSidebar,
    addNotification: state.addNotification
  }), [])
  
  // 使用稳定的selector获取actions，shallow比较已在store内部处理
  const messageDataActions = useMessageDataStore(selectMessageDataActions)
  const messageFlowActions = useMessageFlowStore(selectMessageFlowActions)
  const sessionActions = useSessionStore(selectSessionActions)
  const connectionActions = useConnectionStore(selectConnectionActions)
  const uiActions = useMessageUIStore(selectUIActions)
  
  // ============================================================================
  // 统一API实现
  // ============================================================================
  
  const api = useMemo<ChatAPI>(() => ({
    // 消息操作
    sendMessage: async (content: string, options?: SendMessageOptions) => {
      await messageFlowActions.sendUserMessage(content, currentSession?.sessionId, options)
    },
    
    retryMessage: async (messageId: string) => {
      await messageFlowActions.retryMessage(messageId)
    },
    
    updateMessage: messageDataActions.updateMessage,
    deleteMessage: messageDataActions.deleteMessage,
    
    // 消息查询
    messages: messages as WebSocketMessage[],
    pendingMessages,
    getMessage: messageDataActions.getMessage,
    searchMessages: (query: string) => {
      return messageDataActions.findMessages((msg: any) => {
        // 安全地访问content属性
        const content = 'content' in msg.payload ? msg.payload.content : ''
        return content.toLowerCase().includes(query.toLowerCase())
      })
    },
    
    // 会话管理
    currentSession: currentSession || null,
    createSession: async () => {
      const sessionId = sessionActions.createSession('default-group', 'default-user', 'default-org')
      const sessionStore = useSessionStore.getState()
      const session = sessionStore.sessions[sessionId]
      return session!
    },
    switchSession: async (sessionId: string) => {
      sessionActions.setCurrentSession(sessionId)
    },
    deleteSession: sessionActions.clearSession,
    
    // 连接状态
    connectionStatus,
    isConnected,
    connectionError,
    reconnect: async () => {
      await connectionActions.connect()
    },
    
    // UI状态和交互
    input: {
      content: inputState.content,
      isTyping: inputState.isTyping,
      isFocused: inputState.isFocused,
      updateContent: uiActions.updateInputContent,
      clearContent: uiActions.clearInput,
      setFocus: uiActions.setInputFocus
    },
    
    interaction: {
      selectedMessages: interactionState.selectedMessageIds,
      editingMessage: interactionState.editingMessageId,
      selectMessage: uiActions.selectMessage,
      clearSelection: uiActions.clearSelection,
      startEdit: (messageId: string) => {
        const message = messageDataActions.getMessage(messageId)
        if (message) {
          // 安全地获取content
          const content = 'content' in message.payload ? message.payload.content : ''
          uiActions.startEditMessage(messageId, content)
        }
      },
      endEdit: uiActions.endEditMessage
    },
    
    interface: {
      theme: interfaceState.theme,
      density: interfaceState.messagesDensity,
      sidebarExpanded: interfaceState.sidebarExpanded,
      isMobile: interfaceState.isMobile,
      setTheme: uiActions.setTheme,
      setDensity: uiActions.setMessagesDensity,
      toggleSidebar: uiActions.toggleSidebar
    },
    
    // 扩展系统
    registry: chatRegistry,
    
    registerMessageType: <T = any>(type: string, config: MessageTypeConfig<T>) => {
      chatRegistry.registerProcessor(type, config.processor)
      chatRegistry.registerRenderer(type, {
        component: config.renderer as any, // 临时类型断言，后续修复MessageRenderer接口
        description: config.description || ''
      })
    },
    
    getSupportedTypes: () => {
      return chatRegistry.getRegisteredTypes().processors
    },
    
    // 工具方法
    notify: {
      success: (message: string) => uiActions.addNotification({ type: 'success', title: message }),
      error: (message: string) => uiActions.addNotification({ type: 'error', title: message }),
      info: (message: string) => uiActions.addNotification({ type: 'info', title: message }),
      warning: (message: string) => uiActions.addNotification({ type: 'warning', title: message })
    },
    
    getDiagnostics: () => {
      const messageStats = useMessageDataStore.getState().getDiagnostics()
      const flowStats = useMessageFlowStore.getState().getFlowDiagnostics()
      const registryStats = chatRegistry.getDiagnostics()
      
      return {
        messages: {
          total: messageStats.totalMessages,
          pending: flowStats.pendingCount,
          sessions: messageStats.totalSessions
        },
        connection: {
          status: connectionStatus,
          type: useConnectionStore.getState().getConnectionType() || 'unknown',
          uptime: 0 // TODO: 实现连接时间统计
        },
        registry: {
          processors: registryStats.totalProcessors,
          renderers: registryStats.totalRenderers,
          types: registryStats.registeredTypes.processors
        },
        performance: {
          memoryUsage: messageStats.memoryUsage,
          renderTime: 0 // TODO: 实现渲染时间统计
        }
      }
    }
  }), [
    // 消息数据 - 影响数据展示的状态
    messages,
    pendingMessages,
    currentSession,
    
    // 连接状态 - 影响功能可用性的状态
    connectionStatus,
    isConnected,
    connectionError,
    
    // UI状态 - 影响界面展示的状态
    inputState.content,
    inputState.isTyping,
    inputState.isFocused,
    interactionState.selectedMessageIds,
    interactionState.editingMessageId,
    interfaceState.theme,
    interfaceState.messagesDensity,
    interfaceState.sidebarExpanded,
    
    // Actions - 这些是稳定的引用，不会经常变化
    messageDataActions,
    messageFlowActions,
    sessionActions,
    connectionActions,
    uiActions
  ])
  
  // ============================================================================
  // 初始化和自动连接
  // ============================================================================
  
  // 创建稳定的初始化函数引用
  const initializeChat = useCallback(async () => {
    try {
      // 自动连接
      if (config?.autoConnect !== false) {
        await connectionActions.connect()
      }
      
      // 初始化会话
      if (initialSessionId) {
        sessionActions.setCurrentSession(initialSessionId)
      } else if (!currentSession) {
        sessionActions.createSession('default-group', 'default-user', 'default-org')
      }
      
      if (enableDebug) {
        console.log('✅ Chat系统初始化完成', api.getDiagnostics())
      }
      
    } catch (error) {
      console.error('❌ Chat系统初始化失败:', error)
      if (config?.enableNotifications !== false) {
        uiActions.addNotification({
          type: 'error',
          title: '聊天系统初始化失败',
          message: error instanceof Error ? error.message : '请刷新页面重试'
        })
      }
    }
  }, [
    config?.autoConnect,
    config?.enableNotifications,
    initialSessionId,
    currentSession,
    enableDebug,
    connectionActions.connect,
    sessionActions.setCurrentSession,
    sessionActions.createSession,
    uiActions.addNotification,
    api
  ])

  useEffect(() => {
    initializeChat()
  }, [initializeChat])
  
  // ============================================================================
  // 调试信息
  // ============================================================================
  
  useEffect(() => {
    if (enableDebug) {
      // 全局暴露chat API，便于调试
      ;(window as any).__chatAPI = api
      console.log('🔧 Chat API已暴露到 window.__chatAPI')
    }

    // 组件卸载时的清理函数
    return () => {
      try {
        // 清理UnifiedChatStore中的定时器
        const { useUnifiedChatStore } = require('@/stores/unified-chat-store')
        const store = useUnifiedChatStore.getState()
        if (store.cleanup) {
          store.cleanup()
        }

        // 清理全局debug引用
        if ((window as any).__chatAPI) {
          delete (window as any).__chatAPI
        }

        if (enableDebug) {
          console.log('🧹 ChatProvider 清理完成')
        }
      } catch (error) {
        if (enableDebug) {
          console.warn('⚠️ ChatProvider 清理时发生错误:', error)
        }
      }
    }
  }, [api, enableDebug])
  
  return (
    <ChatContext.Provider value={api}>
      {children}
    </ChatContext.Provider>
  )
}

// ============================================================================
// Hook导出
// ============================================================================

/**
 * 使用Chat API的主要Hook
 * 
 * @example
 * ```tsx
 * function ChatComponent() {
 *   const { messages, sendMessage, input } = useChat()
 *   
 *   return (
 *     <div>
 *       {messages.map(msg => <div key={msg.id}>{msg.payload?.content}</div>)}
 *       <input 
 *         value={input.content} 
 *         onChange={e => input.updateContent(e.target.value)}
 *         onKeyDown={e => e.key === 'Enter' && sendMessage(input.content)}
 *       />
 *     </div>
 *   )
 * }
 * ```
 */
export function useChat(): ChatAPI {
  const context = useContext(ChatContext)
  
  if (!context) {
    throw new Error('useChat must be used within a ChatProvider')
  }
  
  return context
}

/**
 * 轻量级Hook - 仅获取消息相关功能
 */
export function useChatMessages() {
  const { messages, sendMessage, pendingMessages } = useChat()
  return { messages, sendMessage, pendingMessages }
}

/**
 * 轻量级Hook - 仅获取UI相关功能
 */
export function useChatUI() {
  const { input, interaction, interface: interfaceState } = useChat()
  return { input, interaction, interface: interfaceState }
}

/**
 * 轻量级Hook - 仅获取连接相关功能
 */
export function useChatConnection() {
  const { connectionStatus, isConnected, connectionError, reconnect } = useChat()
  return { connectionStatus, isConnected, connectionError, reconnect }
}

// ============================================================================
// 高阶组件
// ============================================================================

/**
 * withChat HOC - 为组件注入chat功能
 */
export function withChat<P extends object>(
  Component: React.ComponentType<P & { chat: ChatAPI }>
) {
  return function WrappedComponent(props: P) {
    const chat = useChat()
    return <Component {...props} chat={chat} />
  }
}