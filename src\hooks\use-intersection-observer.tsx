/**
 * Intersection Observer Hook - 视口交叉检测
 * 
 * 🎯 设计目标：
 * 1. 检测元素是否在视口中
 * 2. 支持性能优化的懒加载
 * 3. 提供灵活的配置选项
 * 4. 兼容不支持的浏览器
 */

import { useEffect, useRef, useCallback, useState } from 'react'
import * as React from 'react'

// ============================================================================
// 类型定义
// ============================================================================

export interface UseIntersectionObserverOptions {
  /** 根元素，默认为视口 */
  root?: Element | null
  /** 根边距 */
  rootMargin?: string
  /** 交叉比例阈值 */
  threshold?: number | number[]
  /** 是否启用观察器 */
  enabled?: boolean
  /** 是否只触发一次 */
  triggerOnce?: boolean
  /** 初始可见状态 */
  initialInView?: boolean
}

export interface IntersectionObserverEntry {
  /** 是否相交 */
  isIntersecting: boolean
  /** 相交比例 */
  intersectionRatio: number
  /** 相交区域 */
  intersectionRect: DOMRectReadOnly
  /** 根元素边界 */
  rootBounds: DOMRectReadOnly | null
  /** 目标元素边界 */
  boundingClientRect: DOMRectReadOnly
  /** 时间戳 */
  time: number
  /** 目标元素 */
  target: Element
}

// ============================================================================
// Hook 实现
// ============================================================================

/**
 * 使用 Intersection Observer 检测元素可见性
 */
export function useIntersectionObserver<T extends Element = HTMLDivElement>(
  ref: React.RefObject<T>,
  callback: (entry: IntersectionObserverEntry) => void,
  options: UseIntersectionObserverOptions = {}
) {
  const {
    root = null,
    rootMargin = '0px',
    threshold = 0,
    enabled = true,
    triggerOnce = false,
    initialInView = false
  } = options

  const [isInView, setIsInView] = useState(initialInView)
  const [entry, setEntry] = useState<IntersectionObserverEntry | null>(null)
  const observerRef = useRef<IntersectionObserver>()
  const callbackRef = useRef(callback)
  const hasTriggeredRef = useRef(false)

  // 更新回调引用
  callbackRef.current = callback

  // 创建观察器回调
  const observerCallback = useCallback((entries: IntersectionObserverEntry[]) => {
    const [observerEntry] = entries
    
    if (!observerEntry) return

    const isCurrentlyInView = observerEntry.isIntersecting
    setIsInView(isCurrentlyInView)
    setEntry(observerEntry)

    // 调用用户提供的回调
    callbackRef.current(observerEntry)

    // 如果只触发一次且已经触发过，则断开观察
    if (triggerOnce && isCurrentlyInView && !hasTriggeredRef.current) {
      hasTriggeredRef.current = true
      if (observerRef.current && ref.current) {
        observerRef.current.unobserve(ref.current)
      }
    }
  }, [triggerOnce])

  // 设置和清理观察器
  useEffect(() => {
    const element = ref.current
    
    if (!enabled || !element) {
      return
    }

    // 检查浏览器支持
    if (!('IntersectionObserver' in window)) {
      console.warn('IntersectionObserver is not supported in this browser')
      // 对于不支持的浏览器，假设元素总是可见的
      setIsInView(true)
      setEntry({
        isIntersecting: true,
        intersectionRatio: 1,
        intersectionRect: element.getBoundingClientRect(),
        rootBounds: null,
        boundingClientRect: element.getBoundingClientRect(),
        time: Date.now(),
        target: element
      } as IntersectionObserverEntry)
      return
    }

    // 如果已触发且只触发一次，则不再观察
    if (triggerOnce && hasTriggeredRef.current) {
      return
    }

    try {
      // 创建观察器
      observerRef.current = new IntersectionObserver(observerCallback, {
        root,
        rootMargin,
        threshold
      })

      // 开始观察
      observerRef.current.observe(element)

      return () => {
        if (observerRef.current) {
          observerRef.current.disconnect()
        }
      }
    } catch (error) {
      console.error('Failed to create IntersectionObserver:', error)
    }
  }, [
    ref,
    root,
    rootMargin,
    threshold,
    enabled,
    triggerOnce,
    observerCallback
  ])

  // 手动触发检测
  const checkIntersection = useCallback(() => {
    if (!ref.current || !observerRef.current) return

    // 强制触发一次检测
    observerRef.current.unobserve(ref.current)
    observerRef.current.observe(ref.current)
  }, [ref])

  return {
    isInView,
    entry,
    checkIntersection
  }
}

// ============================================================================
// 便捷 Hooks
// ============================================================================

/**
 * 简化的可见性检测 Hook
 */
export function useInView<T extends Element = HTMLDivElement>(
  options?: UseIntersectionObserverOptions
) {
  const ref = useRef<T>(null)
  const [isInView, setIsInView] = useState(options?.initialInView ?? false)

  useIntersectionObserver(
    ref,
    (entry) => {
      setIsInView(entry.isIntersecting)
    },
    options
  )

  return [ref, isInView] as const
}

/**
 * 懒加载 Hook
 */
export function useLazyLoad<T extends Element = HTMLDivElement>(
  options?: Omit<UseIntersectionObserverOptions, 'triggerOnce'>
) {
  const ref = useRef<T>(null)
  const [hasLoaded, setHasLoaded] = useState(false)

  useIntersectionObserver(
    ref,
    (entry) => {
      if (entry.isIntersecting && !hasLoaded) {
        setHasLoaded(true)
      }
    },
    {
      ...options,
      triggerOnce: true
    }
  )

  return [ref, hasLoaded] as const
}

/**
 * 视口进入/离开检测 Hook
 */
export function useViewportEntry<T extends Element = HTMLDivElement>(
  onEnter?: () => void,
  onLeave?: () => void,
  options?: UseIntersectionObserverOptions
) {
  const ref = useRef<T>(null)
  const [isInView, setIsInView] = useState(options?.initialInView ?? false)
  const prevInViewRef = useRef(isInView)

  useIntersectionObserver(
    ref,
    (entry) => {
      const currentInView = entry.isIntersecting
      setIsInView(currentInView)

      // 检测状态变化
      if (currentInView && !prevInViewRef.current) {
        // 进入视口
        onEnter?.()
      } else if (!currentInView && prevInViewRef.current) {
        // 离开视口
        onLeave?.()
      }

      prevInViewRef.current = currentInView
    },
    options
  )

  return [ref, isInView] as const
}

// ============================================================================
// 高阶组件
// ============================================================================

export interface WithIntersectionObserverProps {
  isInView?: boolean
  intersectionEntry?: IntersectionObserverEntry | null
}

/**
 * 视口检测高阶组件
 */
export function withIntersectionObserver<P extends object>(
  Component: React.ComponentType<P & WithIntersectionObserverProps>,
  options?: UseIntersectionObserverOptions
) {
  return React.forwardRef<HTMLDivElement, P>((props, forwardedRef) => {
    const ref = useRef<HTMLDivElement>(null)
    const [isInView, setIsInView] = useState(options?.initialInView ?? false)
    const [entry, setEntry] = useState<IntersectionObserverEntry | null>(null)

    useIntersectionObserver(
      ref,
      (observerEntry) => {
        setIsInView(observerEntry.isIntersecting)
        setEntry(observerEntry)
      },
      options
    )

    // 合并 refs
    React.useImperativeHandle(forwardedRef, () => ref.current!)

    return (
      <div ref={ref}>
        <Component
          {...(props as P)}
          isInView={isInView}
          intersectionEntry={entry}
        />
      </div>
    )
  })
}

// ============================================================================
// 工具函数
// ============================================================================

/**
 * 检查浏览器是否支持 IntersectionObserver
 */
export function isIntersectionObserverSupported(): boolean {
  return (
    typeof window !== 'undefined' &&
    'IntersectionObserver' in window &&
    'IntersectionObserverEntry' in window &&
    'intersectionRatio' in window.IntersectionObserverEntry.prototype
  )
}

/**
 * 创建多阈值数组
 */
export function createThresholdArray(steps: number = 20): number[] {
  const thresholds: number[] = []
  for (let i = 0; i <= steps; i++) {
    thresholds.push(i / steps)
  }
  return thresholds
}

/**
 * 获取元素的可见比例
 */
export function getVisibilityRatio(element: Element): number {
  if (!isIntersectionObserverSupported()) {
    return 1 // 假设完全可见
  }

  const rect = element.getBoundingClientRect()
  const windowHeight = window.innerHeight || document.documentElement.clientHeight
  const windowWidth = window.innerWidth || document.documentElement.clientWidth

  // 计算可见区域
  const visibleHeight = Math.min(rect.bottom, windowHeight) - Math.max(rect.top, 0)
  const visibleWidth = Math.min(rect.right, windowWidth) - Math.max(rect.left, 0)

  if (visibleHeight <= 0 || visibleWidth <= 0) {
    return 0
  }

  const visibleArea = visibleHeight * visibleWidth
  const totalArea = rect.height * rect.width

  return totalArea > 0 ? visibleArea / totalArea : 0
}

export default useIntersectionObserver