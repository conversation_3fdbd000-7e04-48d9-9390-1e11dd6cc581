/**
 * Mock系统集成指南和使用示例
 * 
 * 功能：
 * 1. 提供完整的Mock系统使用指南
 * 2. 展示与现有系统的集成方式
 * 3. 包含实际使用案例和最佳实践
 * 4. 开发者快速上手指南
 * 
 * 使用场景：
 * - 新开发者快速了解Mock系统
 * - 项目集成参考
 * - 功能演示和测试
 */

'use client'

import React from 'react'
import { MockWebSocketProvider, useMockWebSocket } from './mock-websocket-provider'
import { MockControlPanel } from '@/components/dev/mock-control-panel'
import { useUnifiedChatStore } from '@/stores/unified-chat-store'

// ============================================================================
// 集成指南组件
// ============================================================================

/**
 * Mock系统集成示例
 * 演示如何在应用中集成完整的Mock系统
 */
export function MockSystemIntegrationExample() {
  return (
    <div className="space-y-6 p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold">Mock系统集成指南</h1>
      
      {/* 基础用法 */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">1. 基础集成</h2>
        <div className="bg-gray-100 p-4 rounded-lg">
          <h3 className="font-medium mb-2">步骤 1: 包装应用</h3>
          <pre className="text-sm bg-white p-3 rounded border overflow-x-auto">
{`// app/layout.tsx 或主应用入口
import { MockWebSocketProvider } from '@/lib/mock/mock-websocket-provider'
import { MockControlPanel } from '@/components/dev/mock-control-panel'

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <MockWebSocketProvider
          autoEnableInDev={true}
          showDevTools={process.env.NODE_ENV === 'development'}
        >
          {children}
          <MockControlPanel />
        </MockWebSocketProvider>
      </body>
    </html>
  )
}`}
          </pre>
        </div>

        <div className="bg-gray-100 p-4 rounded-lg">
          <h3 className="font-medium mb-2">步骤 2: 环境变量配置</h3>
          <pre className="text-sm bg-white p-3 rounded border overflow-x-auto">
{`# .env.local (开发环境)
NEXT_PUBLIC_MOCK_MODE=true
NEXT_PUBLIC_MOCK_DEFAULT_SCENARIO=streaming-fast
NEXT_PUBLIC_MOCK_BASE_DELAY=500
NEXT_PUBLIC_MOCK_ERROR_RATE=0.05
NEXT_PUBLIC_MOCK_LOG_LEVEL=debug

# .env.production (生产环境 - 禁用Mock)
NEXT_PUBLIC_MOCK_MODE=false
NEXT_PUBLIC_WEBSOCKET_URL=wss://your-production-websocket.com`}
          </pre>
        </div>
      </section>

      {/* 组件集成 */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">2. 组件中使用Mock功能</h2>
        <ComponentIntegrationExample />
      </section>

      {/* 场景管理 */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">3. 场景管理示例</h2>
        <ScenarioManagementExample />
      </section>

      {/* 数据生成 */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">4. Mock数据生成</h2>
        <DataGenerationExample />
      </section>

      {/* 测试集成 */}
      <section className="space-y-4">
        <h2 className="text-xl font-semibold">5. 测试环境集成</h2>
        <TestingIntegrationExample />
      </section>
    </div>
  )
}

// ============================================================================
// 组件集成示例
// ============================================================================

function ComponentIntegrationExample() {
  return (
    <div className="bg-gray-100 p-4 rounded-lg">
      <h3 className="font-medium mb-2">在聊天组件中使用Mock功能</h3>
      <pre className="text-sm bg-white p-3 rounded border overflow-x-auto">
{`// components/chat/chat-interface.tsx
import { useMockWebSocket, useIsMockMode } from '@/lib/mock/mock-websocket-provider'
import { useUnifiedChatStore } from '@/stores/unified-chat-store'

export function ChatInterface() {
  const mockWebSocket = useMockWebSocket()
  const isMockMode = useIsMockMode()
  const { sendUserMessage } = useUnifiedChatStore()

  const handleSendMessage = async (content: string) => {
    try {
      await sendUserMessage(content)
      console.log('消息发送成功')
    } catch (error) {
      console.error('消息发送失败:', error)
    }
  }

  const handleSwitchToDemo = async () => {
    if (isMockMode) {
      await mockWebSocket.switchScenario('product-demo-interactive')
    }
  }

  return (
    <div className="chat-interface">
      {isMockMode && (
        <div className="mock-controls">
          <button onClick={handleSwitchToDemo}>
            切换到产品演示场景
          </button>
          <button onClick={() => mockWebSocket.generateConversationData(5)}>
            生成示例对话
          </button>
        </div>
      )}
      
      {/* 聊天界面内容 */}
      <ChatMessageList />
      <ChatInput onSend={handleSendMessage} />
    </div>
  )
}`}
      </pre>
    </div>
  )
}

// ============================================================================
// 场景管理示例
// ============================================================================

function ScenarioManagementExample() {
  return (
    <div className="bg-gray-100 p-4 rounded-lg">
      <h3 className="font-medium mb-2">自定义场景管理</h3>
      <pre className="text-sm bg-white p-3 rounded border overflow-x-auto">
{`// hooks/use-demo-scenarios.ts
import { useMockScenarios } from '@/lib/mock/mock-websocket-provider'
import { EnhancedScenarioManager } from '@/lib/mock/mock-scenarios-enhanced'

export function useDemoScenarios() {
  const { switchScenario, currentScenario } = useMockScenarios()

  const runDemoFlow = async () => {
    // 数据分析演示流程
    const scenarios = [
      'data-analysis-workflow',
      'customer-service-workflow', 
      'product-demo-interactive'
    ]

    for (const scenarioId of scenarios) {
      await switchScenario(scenarioId)
      
      // 等待场景完成
      const scenario = EnhancedScenarioManager.getScenario(scenarioId)
      if (scenario) {
        await new Promise(resolve => 
          setTimeout(resolve, scenario.estimatedDuration)
        )
      }
    }
  }

  const runStressTest = async () => {
    await switchScenario('system-stress-test')
    console.log('压力测试开始')
  }

  return {
    currentScenario,
    runDemoFlow,
    runStressTest,
    switchScenario
  }
}`}
      </pre>
    </div>
  )
}

// ============================================================================
// 数据生成示例
// ============================================================================

function DataGenerationExample() {
  return (
    <div className="bg-gray-100 p-4 rounded-lg">
      <h3 className="font-medium mb-2">Mock数据生成和管理</h3>
      <pre className="text-sm bg-white p-3 rounded border overflow-x-auto">
{`// utils/mock-data-utils.ts
import { mockDataManager, mockData } from '@/lib/mock/mock-data-manager'
import { MessageFactory } from '@/types/websocket-event-type'

export class DemoDataGenerator {
  // 生成完整的用户旅程数据
  static async generateUserJourney(userId: string = 'demo-user') {
    // 1. 创建用户
    const user = mockData.createUser({
      id: userId,
      name: '演示用户',
      role: 'user',
      organizationId: 'demo-org'
    })

    // 2. 创建会话
    const session = mockData.createSession({
      sessionId: \`session_\${userId}_\${Date.now()}\`,
      organizationId: 'demo-org',
      participants: [userId, 'mock-assistant-bot']
    })

    // 3. 生成对话数据
    const messages = await mockData.generateConversationData(15, session.sessionId)

    // 4. 添加特殊场景消息
    const scenarioMessages = await mockData.generateScenarioData(
      'data-analysis-workflow', 
      session.sessionId
    )

    return {
      user,
      session,
      messageCount: messages.length + scenarioMessages.length,
      sessionId: session.sessionId
    }
  }

  // 批量生成测试数据
  static async generateBulkTestData(userCount: number = 5) {
    const results = []
    
    for (let i = 0; i < userCount; i++) {
      const journey = await this.generateUserJourney(\`test-user-\${i}\`)
      results.push(journey)
      
      // 避免同时生成过多数据
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    return results
  }

  // 清理测试数据
  static cleanupTestData() {
    mockData.clearAll()
    console.log('测试数据已清理')
  }
}`}
      </pre>
    </div>
  )
}

// ============================================================================
// 测试集成示例
// ============================================================================

function TestingIntegrationExample() {
  return (
    <div className="bg-gray-100 p-4 rounded-lg">
      <h3 className="font-medium mb-2">测试环境集成</h3>
      <pre className="text-sm bg-white p-3 rounded border overflow-x-auto">
{`// tests/setup/mock-setup.ts
import { MockWebSocketProvider } from '@/lib/mock/mock-websocket-provider'
import { mockConfig } from '@/lib/mock/mock-config'

export function setupMockForTesting() {
  // 配置测试环境Mock
  mockConfig.updateConfig({
    mode: {
      enabled: true,
      defaultScenario: 'streaming-fast',
      allowScenarioSwitching: true,
      showDevTools: false
    },
    behavior: {
      baseDelay: 50, // 测试时使用更快的响应
      errorRate: 0, // 测试时不注入错误
      autoReply: true
    },
    logging: {
      level: 'warn', // 减少测试日志
      verbose: false
    }
  })
}

// tests/chat.test.tsx
import { render, screen, waitFor } from '@testing-library/react'
import { ChatInterface } from '@/components/chat/chat-interface'
import { setupMockForTesting } from './setup/mock-setup'

describe('Chat Interface with Mock', () => {
  beforeEach(() => {
    setupMockForTesting()
  })

  test('should handle mock responses', async () => {
    render(
      <MockWebSocketProvider>
        <ChatInterface />
      </MockWebSocketProvider>
    )

    // 发送消息
    const input = screen.getByRole('textbox')
    fireEvent.change(input, { target: { value: '测试消息' } })
    fireEvent.submit(input.closest('form'))

    // 等待Mock响应
    await waitFor(() => {
      expect(screen.getByText(/我理解了您的问题/)).toBeInTheDocument()
    }, { timeout: 3000 })
  })

  test('should switch scenarios', async () => {
    const { mockWebSocket } = render(
      <MockWebSocketProvider>
        <ChatInterface />
      </MockWebSocketProvider>
    )

    // 切换到表单场景
    await act(async () => {
      await mockWebSocket.switchScenario('form-simple')
    })

    // 验证表单出现
    await waitFor(() => {
      expect(screen.getByText(/请填写以下信息/)).toBeInTheDocument()
    })
  })
})`}
      </pre>
    </div>
  )
}

// ============================================================================
// 配置示例组件
// ============================================================================

/**
 * Mock配置示例展示
 */
export function MockConfigurationExample() {
  return (
    <div className="space-y-4 p-6 max-w-4xl mx-auto">
      <h2 className="text-xl font-semibold">Mock系统配置示例</h2>
      
      {/* 环境配置 */}
      <div className="bg-gray-100 p-4 rounded-lg">
        <h3 className="font-medium mb-2">环境变量配置</h3>
        <pre className="text-sm bg-white p-3 rounded border overflow-x-auto">
{`# 开发环境 (.env.local)
NEXT_PUBLIC_MOCK_MODE=true
NEXT_PUBLIC_MOCK_DEFAULT_SCENARIO=streaming-fast
NEXT_PUBLIC_MOCK_BASE_DELAY=500
NEXT_PUBLIC_MOCK_ERROR_RATE=0.05

# 测试环境 (.env.test)
NEXT_PUBLIC_MOCK_MODE=true
NEXT_PUBLIC_MOCK_BASE_DELAY=50
NEXT_PUBLIC_MOCK_ERROR_RATE=0

# 生产环境 (.env.production)
NEXT_PUBLIC_MOCK_MODE=false
NEXT_PUBLIC_WEBSOCKET_URL=wss://api.example.com/ws`}
        </pre>
      </div>

      {/* 代码配置 */}
      <div className="bg-gray-100 p-4 rounded-lg">
        <h3 className="font-medium mb-2">运行时配置</h3>
        <pre className="text-sm bg-white p-3 rounded border overflow-x-auto">
{`// 动态配置Mock行为
import { updateMockConfig } from '@/lib/mock/mock-config'

// 配置高性能模式
updateMockConfig({
  behavior: {
    baseDelay: 100,
    delayVariance: 50,
    errorRate: 0,
    autoReply: true
  }
})

// 配置调试模式
updateMockConfig({
  behavior: {
    baseDelay: 1000,
    delayVariance: 500,
    errorRate: 0.1
  },
  logging: {
    level: 'debug',
    verbose: true,
    logMessageContent: true
  }
})`}
        </pre>
      </div>
    </div>
  )
}

// ============================================================================
// 最佳实践指南
// ============================================================================

/**
 * Mock系统最佳实践指南
 */
export function MockBestPracticesGuide() {
  return (
    <div className="space-y-6 p-6 max-w-4xl mx-auto">
      <h2 className="text-xl font-semibold">Mock系统最佳实践</h2>
      
      {/* 开发实践 */}
      <section className="space-y-3">
        <h3 className="text-lg font-medium">1. 开发阶段实践</h3>
        <div className="space-y-2 text-sm">
          <div className="flex items-start gap-2">
            <span className="text-green-600">✅</span>
            <span>在开发环境自动启用Mock模式，提高开发效率</span>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-green-600">✅</span>
            <span>使用场景切换功能快速测试不同业务流程</span>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-green-600">✅</span>
            <span>利用开发工具面板实时监控和调试</span>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-green-600">✅</span>
            <span>定期清理Mock数据，避免内存泄漏</span>
          </div>
        </div>
      </section>

      {/* 测试实践 */}
      <section className="space-y-3">
        <h3 className="text-lg font-medium">2. 测试阶段实践</h3>
        <div className="space-y-2 text-sm">
          <div className="flex items-start gap-2">
            <span className="text-green-600">✅</span>
            <span>在测试中使用确定性的Mock配置，避免随机性</span>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-green-600">✅</span>
            <span>为不同测试场景创建专门的Mock数据集</span>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-green-600">✅</span>
            <span>测试错误场景和边界条件</span>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-green-600">✅</span>
            <span>验证Mock和真实环境的行为一致性</span>
          </div>
        </div>
      </section>

      {/* 安全实践 */}
      <section className="space-y-3">
        <h3 className="text-lg font-medium">3. 安全和生产实践</h3>
        <div className="space-y-2 text-sm">
          <div className="flex items-start gap-2">
            <span className="text-red-600">❌</span>
            <span>绝对不要在生产环境启用Mock模式</span>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-green-600">✅</span>
            <span>使用环境变量严格控制Mock功能</span>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-green-600">✅</span>
            <span>定期审查Mock配置和权限</span>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-green-600">✅</span>
            <span>监控Mock功能的使用和性能影响</span>
          </div>
        </div>
      </section>

      {/* 性能实践 */}
      <section className="space-y-3">
        <h3 className="text-lg font-medium">4. 性能优化实践</h3>
        <div className="space-y-2 text-sm">
          <div className="flex items-start gap-2">
            <span className="text-green-600">✅</span>
            <span>合理设置Mock延迟，模拟真实网络条件</span>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-green-600">✅</span>
            <span>限制Mock数据的大小和数量</span>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-green-600">✅</span>
            <span>使用数据清理机制防止内存泄漏</span>
          </div>
          <div className="flex items-start gap-2">
            <span className="text-green-600">✅</span>
            <span>监控Mock系统的资源使用情况</span>
          </div>
        </div>
      </section>
    </div>
  )
}

// ============================================================================
// 故障排除指南
// ============================================================================

/**
 * Mock系统故障排除指南
 */
export function MockTroubleshootingGuide() {
  return (
    <div className="space-y-6 p-6 max-w-4xl mx-auto">
      <h2 className="text-xl font-semibold">Mock系统故障排除</h2>
      
      {/* 常见问题 */}
      <section className="space-y-4">
        <h3 className="text-lg font-medium">常见问题和解决方案</h3>
        
        <div className="space-y-3">
          <div className="border-l-4 border-yellow-400 pl-4 py-2 bg-yellow-50">
            <h4 className="font-medium text-yellow-800">问题：Mock模式无法启用</h4>
            <div className="text-sm text-yellow-700 mt-1">
              <p><strong>可能原因：</strong></p>
              <ul className="list-disc list-inside mt-1 space-y-1">
                <li>环境变量 NEXT_PUBLIC_MOCK_MODE 未设置为 true</li>
                <li>在生产环境下尝试启用Mock模式</li>
                <li>MockWebSocketProvider 未正确配置</li>
              </ul>
              <p className="mt-2"><strong>解决方案：</strong></p>
              <ul className="list-disc list-inside mt-1 space-y-1">
                <li>检查 .env.local 文件中的配置</li>
                <li>确认当前环境为 development</li>
                <li>检查 Provider 的包装是否正确</li>
              </ul>
            </div>
          </div>

          <div className="border-l-4 border-red-400 pl-4 py-2 bg-red-50">
            <h4 className="font-medium text-red-800">问题：Mock消息不显示</h4>
            <div className="text-sm text-red-700 mt-1">
              <p><strong>可能原因：</strong></p>
              <ul className="list-disc list-inside mt-1 space-y-1">
                <li>unified-chat-store 未正确集成</li>
                <li>消息处理器配置错误</li>
                <li>WebSocket事件监听器未设置</li>
              </ul>
              <p className="mt-2"><strong>解决方案：</strong></p>
              <ul className="list-disc list-inside mt-1 space-y-1">
                <li>检查 handleIncomingMessage 调用</li>
                <li>验证消息格式是否符合 ClientMessage 类型</li>
                <li>查看浏览器控制台的错误信息</li>
              </ul>
            </div>
          </div>

          <div className="border-l-4 border-blue-400 pl-4 py-2 bg-blue-50">
            <h4 className="font-medium text-blue-800">问题：场景切换失败</h4>
            <div className="text-sm text-blue-700 mt-1">
              <p><strong>可能原因：</strong></p>
              <ul className="list-disc list-inside mt-1 space-y-1">
                <li>场景ID不存在或拼写错误</li>
                <li>Mock连接未建立</li>
                <li>场景配置有误</li>
              </ul>
              <p className="mt-2"><strong>解决方案：</strong></p>
              <ul className="list-disc list-inside mt-1 space-y-1">
                <li>使用 EnhancedScenarioManager.getAllEnhancedScenarios() 查看可用场景</li>
                <li>确认Mock连接状态为 CONNECTED</li>
                <li>检查场景定义是否正确</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* 调试工具 */}
      <section className="space-y-3">
        <h3 className="text-lg font-medium">调试工具使用</h3>
        <div className="bg-gray-100 p-4 rounded-lg">
          <pre className="text-sm">
{`// 在浏览器控制台中使用调试命令

// 查看Mock配置
console.log(window.__MOCK_CONFIG__)

// 查看Mock数据统计
console.log(window.__MOCK_DATA_STATS__)

// 导出诊断数据
const diagnostics = window.__MOCK_EXPORT_DIAGNOSTICS__()
console.log(diagnostics)

// 清理Mock数据
window.__MOCK_CLEAR_DATA__()

// 切换场景
window.__MOCK_SWITCH_SCENARIO__('streaming-fast')`}
          </pre>
        </div>
      </section>
    </div>
  )
}

// ============================================================================
// 导出所有组件
// ============================================================================

export {
  MockSystemIntegrationExample,
  MockConfigurationExample,
  MockBestPracticesGuide,
  MockTroubleshootingGuide
}