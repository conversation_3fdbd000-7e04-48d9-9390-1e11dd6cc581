/**
 * Chat Skeletons - 聊天系统骨架屏组件集合
 * 
 * 🎯 设计目标：
 * 1. 提供一致的加载状态视觉反馈
 * 2. 支持不同粒度的骨架屏
 * 3. 适配不同的布局和主题
 * 4. 性能优化的动画效果
 * 
 * ⚡ 核心特性：
 * - 多种骨架屏组件
 * - 自适应布局
 * - 主题适配
 * - 平滑动画
 */

'use client'

import React from 'react'
import type { ComponentProps } from 'react'

// ============================================================================
// 基础骨架屏组件
// ============================================================================

interface SkeletonProps extends ComponentProps<'div'> {
  /** 宽度 */
  width?: string | number
  /** 高度 */
  height?: string | number
  /** 是否为圆形 */
  circle?: boolean
  /** 动画类型 */
  animation?: 'pulse' | 'shimmer' | 'wave' | 'none'
  /** 主题 */
  theme?: 'light' | 'dark' | 'auto'
}

export function Skeleton({
  width,
  height,
  circle = false,
  animation = 'pulse',
  theme = 'auto',
  className = '',
  style,
  ...props
}: SkeletonProps) {
  const animationClasses = {
    pulse: 'animate-pulse',
    shimmer: 'animate-shimmer',
    wave: 'animate-wave',
    none: ''
  }

  const themeClasses = {
    light: 'bg-gray-200',
    dark: 'bg-gray-700',
    auto: 'bg-gray-200 dark:bg-gray-700'
  }

  const baseClasses = [
    'block',
    themeClasses[theme],
    animationClasses[animation],
    circle ? 'rounded-full' : 'rounded',
    className
  ].filter(Boolean).join(' ')

  const skeletonStyle = {
    width: typeof width === 'number' ? `${width}px` : width,
    height: typeof height === 'number' ? `${height}px` : height,
    ...style
  }

  return (
    <div 
      className={baseClasses}
      style={skeletonStyle}
      {...props}
    />
  )
}

// ============================================================================
// 消息相关骨架屏
// ============================================================================

/**
 * 单条消息骨架屏
 */
export interface MessageSkeletonProps {
  /** 消息类型 */
  type?: 'user' | 'assistant' | 'system'
  /** 是否显示头像 */
  showAvatar?: boolean
  /** 是否显示时间戳 */
  showTimestamp?: boolean
  /** 内容行数 */
  contentLines?: number
  /** 动画延迟（毫秒） */
  animationDelay?: number
}

export function MessageSkeleton({
  type = 'assistant',
  showAvatar = true,
  showTimestamp = true,
  contentLines = 2,
  animationDelay = 0
}: MessageSkeletonProps) {
  const isUserMessage = type === 'user'
  const isSystemMessage = type === 'system'
  
  return (
    <div 
      className={`flex ${isUserMessage ? 'justify-end' : 'justify-start'} mb-4`}
      style={{ animationDelay: `${animationDelay}ms` }}
    >
      <div className={`flex ${isUserMessage ? 'flex-row-reverse' : 'flex-row'} max-w-[80%] space-x-3`}>
        {/* 头像 */}
        {showAvatar && !isSystemMessage && (
          <Skeleton
            width={32}
            height={32}
            circle
            className="flex-shrink-0"
          />
        )}
        
        {/* 消息内容 */}
        <div className="flex-1 space-y-2">
          {/* 发送者信息 */}
          {showTimestamp && !isSystemMessage && (
            <div className={`flex items-center space-x-2 ${isUserMessage ? 'justify-end' : 'justify-start'}`}>
              <Skeleton width={60} height={12} />
              <Skeleton width={80} height={12} />
            </div>
          )}
          
          {/* 消息气泡 */}
          <div className={`rounded-lg p-3 ${
            isUserMessage 
              ? 'bg-blue-50 dark:bg-blue-900/20' 
              : isSystemMessage
              ? 'bg-yellow-50 dark:bg-yellow-900/20'
              : 'bg-gray-50 dark:bg-gray-800'
          }`}>
            <div className="space-y-2">
              {Array.from({ length: contentLines }, (_, i) => (
                <Skeleton
                  key={i}
                  height={16}
                  width={i === contentLines - 1 ? '60%' : '100%'}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

/**
 * 消息列表骨架屏
 */
export interface MessageListSkeletonProps {
  /** 消息数量 */
  messageCount?: number
  /** 是否显示加载更多 */
  showLoadMore?: boolean
  /** 消息类型分布 */
  messageTypes?: Array<'user' | 'assistant' | 'system'>
}

export function MessageListSkeleton({
  messageCount = 5,
  showLoadMore = false,
  messageTypes = ['assistant', 'user', 'assistant', 'user', 'assistant']
}: MessageListSkeletonProps) {
  const messages = Array.from({ length: messageCount }, (_, i) => ({
    type: messageTypes[i % messageTypes.length] || 'assistant',
    lines: Math.floor(Math.random() * 3) + 1
  }))

  return (
    <div className="message-list-skeleton">
      {/* 历史消息加载提示 */}
      {showLoadMore && (
        <div className="flex justify-center py-4">
          <Skeleton width={120} height={32} className="rounded-full" />
        </div>
      )}

      {/* 消息列表 */}
      <div className="space-y-1">
        {messages.map((message, index) => (
          <MessageSkeleton
            key={index}
            type={message.type}
            contentLines={message.lines}
            animationDelay={index * 100}
          />
        ))}
      </div>
    </div>
  )
}

// ============================================================================
// 输入区域骨架屏
// ============================================================================

/**
 * 聊天输入框骨架屏
 */
export interface ChatInputSkeletonProps {
  /** 是否显示附件按钮 */
  showAttachButton?: boolean
  /** 是否显示表情按钮 */
  showEmojiButton?: boolean
  /** 是否显示发送按钮 */
  showSendButton?: boolean
}

export function ChatInputSkeleton({
  showAttachButton = true,
  showEmojiButton = true,
  showSendButton = true
}: ChatInputSkeletonProps) {
  return (
    <div className="chat-input-skeleton border-t bg-white dark:bg-gray-900 p-4">
      <div className="flex items-end space-x-2">
        {/* 附件按钮 */}
        {showAttachButton && (
          <Skeleton width={32} height={32} className="flex-shrink-0" />
        )}
        
        {/* 输入框 */}
        <div className="flex-1">
          <Skeleton height={40} className="rounded-lg" />
        </div>
        
        {/* 表情按钮 */}
        {showEmojiButton && (
          <Skeleton width={32} height={32} className="flex-shrink-0" />
        )}
        
        {/* 发送按钮 */}
        {showSendButton && (
          <Skeleton width={32} height={32} className="flex-shrink-0" />
        )}
      </div>
      
      {/* 输入提示 */}
      <div className="mt-2 flex justify-between items-center">
        <Skeleton width={150} height={12} />
        <Skeleton width={80} height={12} />
      </div>
    </div>
  )
}

// ============================================================================
// 侧边栏骨架屏
// ============================================================================

/**
 * 会话列表项骨架屏
 */
export function SessionItemSkeleton() {
  return (
    <div className="session-item-skeleton p-3 border-b">
      <div className="flex items-center space-x-3">
        <Skeleton width={40} height={40} circle />
        <div className="flex-1 space-y-2">
          <div className="flex justify-between items-center">
            <Skeleton width={120} height={14} />
            <Skeleton width={60} height={12} />
          </div>
          <Skeleton width={180} height={12} />
        </div>
      </div>
    </div>
  )
}

/**
 * 侧边栏骨架屏
 */
export interface SidebarSkeletonProps {
  /** 会话数量 */
  sessionCount?: number
  /** 是否显示搜索框 */
  showSearch?: boolean
  /** 是否显示新建按钮 */
  showNewButton?: boolean
}

export function SidebarSkeleton({
  sessionCount = 8,
  showSearch = true,
  showNewButton = true
}: SidebarSkeletonProps) {
  return (
    <div className="sidebar-skeleton w-64 bg-gray-50 dark:bg-gray-800 border-r">
      {/* 头部 */}
      <div className="p-4 border-b">
        {showNewButton && (
          <Skeleton height={40} className="mb-3 rounded-lg" />
        )}
        {showSearch && (
          <Skeleton height={36} className="rounded-lg" />
        )}
      </div>
      
      {/* 会话列表 */}
      <div className="flex-1 overflow-hidden">
        {Array.from({ length: sessionCount }, (_, i) => (
          <SessionItemSkeleton key={i} />
        ))}
      </div>
      
      {/* 底部 */}
      <div className="p-4 border-t">
        <div className="flex items-center space-x-2">
          <Skeleton width={24} height={24} circle />
          <Skeleton width={100} height={14} />
        </div>
      </div>
    </div>
  )
}

// ============================================================================
// 完整聊天界面骨架屏
// ============================================================================

/**
 * 完整聊天界面骨架屏
 */
export interface ChatInterfaceSkeletonProps {
  /** 是否显示侧边栏 */
  showSidebar?: boolean
  /** 消息数量 */
  messageCount?: number
  /** 是否显示头部 */
  showHeader?: boolean
  /** 布局模式 */
  layout?: 'desktop' | 'mobile' | 'embed'
}

export function ChatInterfaceSkeleton({
  showSidebar = true,
  messageCount = 6,
  showHeader = true,
  layout = 'desktop'
}: ChatInterfaceSkeletonProps) {
  const isEmbed = layout === 'embed'
  const isMobile = layout === 'mobile'
  
  return (
    <div className={`chat-interface-skeleton flex h-full ${
      isEmbed ? 'rounded-lg border' : ''
    }`}>
      {/* 侧边栏 */}
      {showSidebar && !isMobile && !isEmbed && (
        <SidebarSkeleton />
      )}
      
      {/* 主聊天区域 */}
      <div className="flex-1 flex flex-col">
        {/* 头部 */}
        {showHeader && !isEmbed && (
          <div className="chat-header-skeleton p-4 border-b bg-white dark:bg-gray-900">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {isMobile && (
                  <Skeleton width={24} height={24} />
                )}
                <Skeleton width={150} height={20} />
              </div>
              <div className="flex items-center space-x-2">
                <Skeleton width={24} height={24} circle />
                <Skeleton width={24} height={24} circle />
                <Skeleton width={24} height={24} circle />
              </div>
            </div>
          </div>
        )}
        
        {/* 消息区域 */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full p-4">
            <MessageListSkeleton 
              messageCount={messageCount}
              showLoadMore={!isEmbed}
            />
          </div>
        </div>
        
        {/* 输入区域 */}
        <ChatInputSkeleton />
      </div>
    </div>
  )
}

// ============================================================================
// 特殊用途骨架屏
// ============================================================================

/**
 * 流式消息骨架屏
 */
export function StreamingMessageSkeleton() {
  return (
    <div className="streaming-message-skeleton">
      <MessageSkeleton
        type="assistant"
        contentLines={1}
      />
      <div className="ml-11 -mt-2">
        <div className="flex items-center space-x-1">
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                style={{ animationDelay: `${i * 0.1}s` }}
              />
            ))}
          </div>
          <span className="text-xs text-gray-500 ml-2">正在输入...</span>
        </div>
      </div>
    </div>
  )
}

/**
 * 加载状态骨架屏
 */
export interface LoadingSkeletonProps {
  /** 加载状态文本 */
  text?: string | undefined;
  /** 是否显示进度条 */
  showProgress?: boolean;
  /** 进度百分比 */
  progress?: number;
}

export function LoadingSkeleton({
  text = '正在加载...',
  showProgress = false,
  progress = 0,
}: LoadingSkeletonProps) {
  return (
    <div className="loading-skeleton flex flex-col items-center justify-center p-8 space-y-4">
      {/* 加载动画 */}
      <div className="relative">
        <div className="w-12 h-12 border-4 border-gray-200 border-t-blue-500 rounded-full animate-spin"></div>
      </div>
      
      {/* 加载文本 */}
      <p className="text-sm text-gray-600 dark:text-gray-400">
        {text}
      </p>
      
      {/* 进度条 */}
      {showProgress && (
        <div className="w-48 h-2 bg-gray-200 rounded-full overflow-hidden">
          <div 
            className="h-full bg-blue-500 transition-all duration-300 ease-out"
            style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
          />
        </div>
      )}
    </div>
  );
}

/**
 * 错误重试骨架屏
 */
export interface ErrorRetrySkeleton {
  /** 错误信息 */
  error?: string | undefined;
  /** 重试回调 */
  onRetry?: () => void;
  /** 是否正在重试 */
  isRetrying?: boolean;
}

export function ErrorRetrySkeleton({
  error = '加载失败',
  onRetry,
  isRetrying = false,
}: ErrorRetrySkeleton) {
  return (
    <div className="error-retry-skeleton flex flex-col items-center justify-center p-8 space-y-4">
      {/* 错误图标 */}
      <div className="w-16 h-16 text-red-500">
        <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={1.5} 
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.732 15.5c-.77.833.192 2.5 1.732 2.5z" 
          />
        </svg>
      </div>
      
      {/* 错误信息 */}
      <p className="text-sm text-gray-600 dark:text-gray-400 text-center">
        {error}
      </p>
      
      {/* 重试按钮 */}
      {onRetry && (
        <button
          onClick={onRetry}
          disabled={isRetrying}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          {isRetrying ? '重试中...' : '重试'}
        </button>
      )}
    </div>
  );
}

// ============================================================================
// 组合骨架屏
// ============================================================================

/**
 * 自适应骨架屏组合
 */
export interface AdaptiveSkeletonProps {
  /** 当前状态 */
  state: 'loading' | 'error' | 'empty' | 'streaming';
  /** 错误信息 */
  error?: string;
  /** 重试回调 */
  onRetry?: () => void;
  /** 加载文本 */
  loadingText?: string;
  /** 空状态文本 */
  emptyText?: string;
}

export function AdaptiveSkeleton({
  state,
  error = '加载失败',
  onRetry,
  loadingText = '正在加载...',
  emptyText = '暂无消息',
}: AdaptiveSkeletonProps) {
  switch (state) {
    case 'loading':
      return <LoadingSkeleton text={loadingText} />;
      
    case 'error':
      return (
        <ErrorRetrySkeleton 
          error={error} 
          onRetry={onRetry}
        />
      );
      
    case 'empty':
      return (
        <div className="empty-skeleton flex flex-col items-center justify-center p-8 space-y-4">
          <div className="w-16 h-16 text-gray-400">
            <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={1.5} 
                d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" 
              />
            </svg>
          </div>
          <p className="text-sm text-gray-500">{emptyText}</p>
        </div>
      );
      
    case 'streaming':
      return <StreamingMessageSkeleton />;
      
    default:
      return <LoadingSkeleton />;
  }
}

export default ChatInterfaceSkeleton