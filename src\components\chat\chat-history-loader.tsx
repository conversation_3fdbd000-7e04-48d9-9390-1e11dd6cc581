'use client'

/**
 * Chat History Loader - 服务端消息历史预加载组件
 * 
 * 🎯 设计目标：
 * 1. 在服务端预加载消息历史数据
 * 2. 支持流式渲染和Suspense
 * 3. 提供SEO友好的内容渲染
 * 4. 优化首屏加载性能
 * 
 * ⚡ 核心特性：
 * - Server Components集成
 * - 增量静态再生(ISR)支持
 * - 错误边界和降级策略
 * - 缓存优化
 */

import type { ChatSession } from '@/stores/session-store'
import type { BaseWebSocketMessage, ClientMessage } from '@/types/websocket-event-type'
import { Suspense } from 'react'

// ============================================================================
// 类型定义
// ============================================================================

/**
 * 聊天历史加载器配置
 */
export interface ChatHistoryLoaderConfig {
  /** 会话ID */
  sessionId: string
  /** 加载消息数量限制 */
  limit?: number
  /** 分页偏移量 */
  offset?: number
  /** 是否包含系统消息 */
  includeSystemMessages?: boolean
  /** 缓存时间（秒） */
  cacheTTL?: number
  /** 是否启用增量加载 */
  enableIncremental?: boolean
}

/**
 * 聊天历史数据
 */
export interface ChatHistoryData {
  /** 消息列表 */
  messages: ClientMessage[]
  /** 会话信息 */
  session: ChatSession | null
  /** 总消息数 */
  totalCount: number
  /** 是否有更多消息 */
  hasMore: boolean
  /** 最后加载时间 */
  lastLoadedAt: number
  /** 会话统计 */
  sessionStats: {
    totalMessages: number
    userMessages: number
    assistantMessages: number
    systemMessages: number
    lastMessageTime: number
  }
}

/**
 * 加载器属性
 */
export interface ChatHistoryLoaderProps {
  /** 加载配置 */
  config: ChatHistoryLoaderConfig
  /** 子组件 */
  children: (data: ChatHistoryData) => React.ReactNode
  /** 加载中占位符 */
  fallback?: React.ReactNode
  /** 错误处理 */
  onError?: (error: Error) => void
}

// ============================================================================
// 数据获取函数（服务端）
// ============================================================================

/**
 * 获取聊天历史数据（服务端函数）
 * 这个函数应该在服务端调用，可以直接访问数据库或API
 */
export async function getChatHistoryData(
  config: ChatHistoryLoaderConfig
): Promise<ChatHistoryData> {
  const {
    sessionId,
    limit = 50,
    offset = 0,
    includeSystemMessages = false,
    cacheTTL = 300 // 5分钟缓存
  } = config

  try {
    // 这里应该替换为实际的数据获取逻辑
    // 例如：从数据库、Redis缓存或FastAPI后端获取数据
    
    // 模拟数据获取延迟
    await new Promise(resolve => setTimeout(resolve, 100))

    // 模拟从后端API获取数据
    const response = await fetchChatHistory({
      sessionId,
      limit,
      offset,
      includeSystemMessages
    })

    const { messages, session, totalCount } = response

    // 计算统计信息
    const sessionStats = calculateSessionStats(messages)

    return {
      messages,
      session,
      totalCount,
      hasMore: totalCount > offset + messages.length,
      lastLoadedAt: Date.now(),
      sessionStats
    }

  } catch (error) {
    console.error('❌ 获取聊天历史失败:', error)
    
    // 返回空数据作为降级策略
    return {
      messages: [],
      session: null,
      totalCount: 0,
      hasMore: false,
      lastLoadedAt: Date.now(),
      sessionStats: {
        totalMessages: 0,
        userMessages: 0,
        assistantMessages: 0,
        systemMessages: 0,
        lastMessageTime: 0
      }
    }
  }
}

/**
 * 模拟从后端获取聊天历史
 * 在实际项目中，这里应该调用 unified-http 或直接的数据库查询
 */
async function fetchChatHistory({
  sessionId,
  limit,
  offset,
  includeSystemMessages
}: {
  sessionId: string
  limit: number
  offset: number
  includeSystemMessages: boolean
}) {
  // 这里是模拟实现，实际应该调用后端API
  // 例如: 
  // const { useHttp } = await import('@/lib/http/unified-http')
  // const http = useHttp()
  // return await http.get(`/api/chat/history/${sessionId}`, { limit, offset })

  // 模拟数据
  const mockMessages: ClientMessage[] = [
    {
      id: 'msg_1',
      groupChatId: 'default-group',
      sessionId,
      userId: 'user-1',
      organizationId: 'default-org',
      payload: {
        type: 'user_message',
        content: '你好，我想了解一下产品功能'
      },
      timestamp: Date.now() - 3600000, // 1小时前
      status: 'sent',
      metadata: {},
      createdAt: Date.now() - 3600000,
      updatedAt: Date.now() - 3600000
    },
    {
      id: 'msg_2',
      groupChatId: 'default-group',
      sessionId,
      userId: 'assistant',
      organizationId: 'default-org',
      payload: {
        type: 'assistant_message',
        content: '很高兴为您介绍我们的产品功能。我们的产品主要包括...'
      },
      timestamp: Date.now() - 3500000,
      status: 'sent',
      metadata: {},
      createdAt: Date.now() - 3500000,
      updatedAt: Date.now() - 3500000
    }
  ]

  const mockSession: ChatSession = {
    sessionId,
    userId: 'user-1',
    groupChatId: 'default-group',
    organizationId: 'default-org',
    isActive: true,
    messages: mockMessages as unknown as BaseWebSocketMessage[],
    createdAt: new Date(Date.now() - 3600000),
    updatedAt: new Date()
  }

  return {
    messages: mockMessages,
    session: mockSession,
    totalCount: mockMessages.length
  }
}

/**
 * 计算会话统计信息
 */
function calculateSessionStats(messages: ClientMessage[]) {
  const stats = {
    totalMessages: messages.length,
    userMessages: 0,
    assistantMessages: 0,
    systemMessages: 0,
    lastMessageTime: 0
  }

  messages.forEach(message => {
    if (message.timestamp > stats.lastMessageTime) {
      stats.lastMessageTime = message.timestamp
    }

    switch (message.payload.type) {
      case 'user_message':
        stats.userMessages++
        break
      case 'assistant_message':
        stats.assistantMessages++
        break
      case 'system':
        stats.systemMessages++
        break
    }
  })

  return stats
}

// ============================================================================
// Server Component - 聊天历史加载器
// ============================================================================

/**
 * 聊天历史加载器 - Server Component
 * 在服务端预加载聊天历史数据
 */
export async function ChatHistoryLoader({
  config,
  children,
  fallback = <ChatHistoryLoadingSkeleton />,
  onError
}: ChatHistoryLoaderProps) {
  try {
    // 在服务端获取数据
    const historyData = await getChatHistoryData(config)
    
    // 渲染子组件并传递数据
    return (
      <div className="chat-history-loader">
        {children(historyData)}
      </div>
    )
  } catch (error) {
    console.error('❌ ChatHistoryLoader错误:', error)
    
    // 调用错误处理回调
    if (onError) {
      onError(error instanceof Error ? error : new Error('未知错误'))
    }

    // 返回错误状态
    return (
      <ChatHistoryErrorBoundary 
        error={error instanceof Error ? error : new Error('加载失败')}
        config={config}
      />
    )
  }
}

// ============================================================================
// 客户端组件 - 增量加载器
// ============================================================================

import { useCallback, useState } from 'react'

/**
 * 增量聊天历史加载器属性
 */
export interface IncrementalChatLoaderProps {
  /** 初始数据 */
  initialData: ChatHistoryData
  /** 加载更多的配置 */
  loadMoreConfig?: Partial<ChatHistoryLoaderConfig>
  /** 加载更多时的回调 */
  onLoadMore?: (data: ChatHistoryData) => void
  /** 子组件 */
  children: (props: {
    data: ChatHistoryData
    loadMore: () => Promise<void>
    isLoading: boolean
    error: Error | null
  }) => React.ReactNode
}

/**
 * 增量聊天历史加载器 - 客户端组件
 * 支持加载更多历史消息
 */
export function IncrementalChatLoader({
  initialData,
  loadMoreConfig,
  onLoadMore,
  children
}: IncrementalChatLoaderProps) {
  const [data, setData] = useState<ChatHistoryData>(initialData)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  const loadMore = useCallback(async () => {
    if (isLoading || !data.hasMore) return

    setIsLoading(true)
    setError(null)

    try {
      const config: ChatHistoryLoaderConfig = {
        sessionId: data.session?.sessionId || '',
        limit: 20,
        offset: data.messages.length,
        ...loadMoreConfig
      }

      // 这里应该调用客户端API获取更多数据
      // 可以使用 unified-http 或直接调用API
      const newData = await getChatHistoryData(config)

      // 合并新数据
      const mergedData: ChatHistoryData = {
        ...newData,
        messages: [...data.messages, ...newData.messages],
        sessionStats: {
          ...data.sessionStats,
          totalMessages: data.sessionStats.totalMessages + newData.messages.length
        }
      }

      setData(mergedData)
      onLoadMore?.(mergedData)

    } catch (err) {
      const loadError = err instanceof Error ? err : new Error('加载更多失败')
      setError(loadError)
      console.error('❌ 加载更多聊天历史失败:', loadError)
    } finally {
      setIsLoading(false)
    }
  }, [data, isLoading, loadMoreConfig, onLoadMore])

  return (
    <>
      {children({
        data,
        loadMore,
        isLoading,
        error
      })}
    </>
  )
}

// ============================================================================
// 辅助组件
// ============================================================================

/**
 * 聊天历史加载骨架屏
 */
export function ChatHistoryLoadingSkeleton() {
  return (
    <div className="chat-history-skeleton animate-pulse p-4 space-y-4">
      <div className="flex items-center space-x-2 mb-4">
        <div className="w-4 h-4 bg-gray-200 rounded"></div>
        <div className="h-4 bg-gray-200 rounded w-32"></div>
      </div>
      
      {[1, 2, 3, 4, 5].map((i) => (
        <div key={i} className="flex space-x-3">
          <div className="w-8 h-8 bg-gray-200 rounded-full flex-shrink-0"></div>
          <div className="flex-1 space-y-2">
            <div className="flex items-center space-x-2">
              <div className="h-3 bg-gray-200 rounded w-16"></div>
              <div className="h-3 bg-gray-200 rounded w-20"></div>
            </div>
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
          </div>
        </div>
      ))}
      
      <div className="flex justify-center pt-4">
        <div className="h-8 bg-gray-200 rounded w-24"></div>
      </div>
    </div>
  )
}

/**
 * 聊天历史错误边界
 */
export function ChatHistoryErrorBoundary({ 
  error, 
  config 
}: { 
  error: Error
  config: ChatHistoryLoaderConfig 
}) {
  const handleRetry = () => {
    window.location.reload()
  }

  return (
    <div className="chat-history-error p-6 text-center bg-red-50 border border-red-200 rounded-lg">
      <div className="text-red-600 mb-2">
        <svg 
          className="w-8 h-8 mx-auto mb-2" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.732 15.5c-.77.833.192 2.5 1.732 2.5z" 
          />
        </svg>
      </div>
      
      <h3 className="text-lg font-semibold text-red-800 mb-2">
        聊天历史加载失败
      </h3>
      
      <p className="text-red-700 mb-4">
        {error.message}
      </p>
      
      <div className="flex justify-center space-x-3">
        <button
          onClick={handleRetry}
          className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors"
        >
          重试
        </button>
        
        <button
          onClick={() => window.history.back()}
          className="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
        >
          返回
        </button>
      </div>
      
      {process.env.NODE_ENV === 'development' && (
        <details className="mt-4 text-left">
          <summary className="cursor-pointer text-sm text-red-600">
            调试信息
          </summary>
          <pre className="mt-2 p-2 bg-red-100 text-xs text-red-800 rounded overflow-auto">
            {JSON.stringify({ error: error.stack, config }, null, 2)}
          </pre>
        </details>
      )}
    </div>
  )
}

// ============================================================================
// Suspense边界组件
// ============================================================================

/**
 * 聊天历史 Suspense 包装器
 */
export function ChatHistorySuspense({ 
  children, 
  fallback = <ChatHistoryLoadingSkeleton /> 
}: {
  children: React.ReactNode
  fallback?: React.ReactNode
}) {
  return (
    <Suspense fallback={fallback}>
      {children}
    </Suspense>
  )
}

// ============================================================================
// 预设配置
// ============================================================================

/**
 * 预设的加载器配置
 */
export const CHAT_LOADER_PRESETS = {
  /** 默认配置 */
  default: {
    limit: 50,
    offset: 0,
    includeSystemMessages: false,
    cacheTTL: 300,
    enableIncremental: true
  } as Partial<ChatHistoryLoaderConfig>,

  /** 快速加载（较少消息） */
  quick: {
    limit: 20,
    offset: 0,
    includeSystemMessages: false,
    cacheTTL: 60,
    enableIncremental: true
  } as Partial<ChatHistoryLoaderConfig>,

  /** 详细加载（包含系统消息） */
  detailed: {
    limit: 100,
    offset: 0,
    includeSystemMessages: true,
    cacheTTL: 600,
    enableIncremental: true
  } as Partial<ChatHistoryLoaderConfig>,

  /** 仅最近消息 */
  recent: {
    limit: 10,
    offset: 0,
    includeSystemMessages: false,
    cacheTTL: 30,
    enableIncremental: false
  } as Partial<ChatHistoryLoaderConfig>
} as const

export default ChatHistoryLoader