/**
 * WebSocket 消息类型定义
 * 设计原则：严格分离后端数据和前端UI状态，保持类型系统的纯洁性
 * 
 * 架构说明：
 * - BackendWebSocketMessage: 纯净的后端数据类型
 * - FrontendMessageState: 前端UI状态类型
 * - ClientMessage: 组合后端数据和前端状态的完整客户端消息类型
 */

// ============================================================================
// 后端纯净数据类型 (Backend Data Types)
// ============================================================================

/**
 * 后端 WebSocket 消息接口 - 仅包含后端提供的数据字段
 * 这个接口保持纯洁性，只包含从后端接收的数据
 */
export interface BackendWebSocketMessage {
  /** 群聊标识 */
  groupChatId: string
  /** 会话标识 */
  sessionId: string
  /** 用户唯一标识 */
  userId: string
  /** 公司/组织标识 */
  organizationId: string
  /** 消息负载数据 */
  payload: MessagePayload
  /** 错误信息 (可选, 当消息类型为 'error' 时出现) */
  error?: string
  /** 消息时间戳 */
  timestamp: number
}

// ============================================================================
// 前端扩展状态类型 (Frontend State Types)
// ============================================================================

/**
 * 前端消息状态接口 - 包含所有前端UI相关的状态
 */
export interface FrontendMessageState {
  /** 前端生成的消息唯一标识符 */
  id: string
  /** 消息显示状态 */
  status: 'sending' | 'sent' | 'delivered' | 'failed' | 'pending' | 'retry'
  /** UI相关元数据 */
  metadata: {
    /** 流式传输状态 */
    streamingState?: {
      isComplete: boolean
      accumulatedText?: string
      lastUpdateTime?: number
    }
    /** 用户交互状态 */
    interactionState?: {
      isExpanded?: boolean
      isHighlighted?: boolean
      hasUserInteracted?: boolean
    }
    /** 错误处理状态 */
    errorState?: {
      retryCount?: number
      lastErrorTime?: number
      isRecoverable?: boolean
    }
    /** 自定义扩展字段 */
    [key: string]: any
  }
  /** 前端创建时间 */
  createdAt: number
  /** 最后更新时间 */
  updatedAt: number
}

/**
 * 完整的客户端消息类型 - 组合后端数据和前端状态
 */
export type ClientMessage = BackendWebSocketMessage & FrontendMessageState

// ============================================================================
// 向后兼容性支持 (Backward Compatibility)
// ============================================================================

/**
 * @deprecated 请使用 ClientMessage 替代
 * 保留原有接口以确保向后兼容性，将在下个主版本中移除
 */
export interface BaseWebSocketMessage {
  /** 消息唯一标识 - 前端生成的UI ID (必填) */
  id: string
  /** 群聊标识 */
  groupChatId: string
  /** 会话标识 */
  sessionId: string
  /** 用户唯一标识 */
  userId: string
  /** 公司/组织标识 */
  organizationId: string
  /** 消息负载数据 */
  payload: MessagePayload
  /** 错误信息 (可选, 当消息类型为 'error' 时出现) */
  error?: string
  /** 消息时间戳 */
  timestamp: number
  /** UI相关元数据 - 前端管理 */
  metadata?: {
    streamingState?: {
      isComplete: boolean
      accumulatedText?: string
    }
    [key: string]: any
  }
  /** 消息状态 - UI显示状态 */
  status?: 'sending' | 'sent' | 'delivered' | 'failed' | 'pending' | 'retry'
}

// ============================================================================
// Payload 联合类型定义 (Message Payload Types)
// ============================================================================

/**
 * 消息负载联合类型 - 包含所有可能的消息负载类型
 */
export type MessagePayload = 
  | StreamingPayload 
  | CheckpointPayload 
  | ReportPayload 
  | ErrorPayload 
  | TextPayload 
  | SystemPayload 
  | UserPayload 
  | AssistantPayload 
  | PollPayload
  | FilePayload

// ============================================================================
// 1. 流式内容输出 Payload
// ============================================================================

/**
 * 流式内容输出负载 - 用于实时传输文本内容
 */
export interface StreamingPayload {
  /** 负载类型标识 */
  readonly type: 'streaming'
  /** 当前文本片段 - 增量内容 */
  delta: string
  /** 是否传输完成 */
  isComplete: boolean
  /** 序列号 - 用于确保片段顺序 (可选) */
  sequence?: number
  /** 总长度预估 (可选) */
  totalLength?: number
}

// ============================================================================
// 2. 用户表单交互 Payload
// ============================================================================

/**
 * 检查点负载 - 用于用户表单交互和数据收集
 */
export interface CheckpointPayload {
  /** 负载类型标识 */
  readonly type: 'checkpoint'
  /** 表单字段数组 - 后端只提供字段定义数据 */
  fields: FormField[]
  /** 检查点标题 (可选) */
  title?: string
  /** 检查点描述 (可选) */
  description?: string
  /** 是否必须完成此检查点 */
  isRequired?: boolean
}

/**
 * 表单字段定义 - 描述单个表单字段的结构和约束
 */
export interface FormField {
  /** 字段唯一标识符 */
  id: string
  /** 字段显示标签 */
  label: string
  /** 字段类型 */
  type: FormFieldType
  /** 是否为必填字段 */
  required: boolean
  /** 默认值 (可选) */
  defaultValue?: string | number | boolean | string[]
  /** 选项数据 - 仅用于选择类型字段 (可选) */
  options?: FormFieldOption[]
  /** 字段验证规则 (可选) */
  validation?: FormFieldValidation
  /** 字段提示信息 (可选) */
  placeholder?: string
  /** 字段帮助文本 (可选) */
  helpText?: string
}

/**
 * 支持的表单字段类型
 */
export type FormFieldType =
  | 'text'
  | 'textarea'
  | 'select'
  | 'multiselect'
  | 'radio'
  | 'checkbox'
  | 'number'
  | 'date'
  | 'datetime'
  | 'email'
  | 'url'
  | 'password'
  | 'file'

/**
 * 表单字段选项定义
 */
export interface FormFieldOption {
  /** 选项显示文本 */
  label: string
  /** 选项值 */
  value: string | number
  /** 选项是否禁用 (可选) */
  disabled?: boolean
  /** 选项描述 (可选) */
  description?: string
}

/**
 * 表单字段验证规则
 */
export interface FormFieldValidation {
  /** 最小长度 (适用于文本类型) */
  minLength?: number
  /** 最大长度 (适用于文本类型) */
  maxLength?: number
  /** 最小值 (适用于数字类型) */
  min?: number
  /** 最大值 (适用于数字类型) */
  max?: number
  /** 正则表达式模式 */
  pattern?: string
  /** 自定义验证错误消息 */
  errorMessage?: string
}

// ============================================================================
// 3. 最终报告展示 Payload
// ============================================================================

/**
 * 报告负载 - 用于展示最终分析报告或总结内容
 */
export interface ReportPayload {
  /** 负载类型标识 */
  readonly type: 'report'
  /** 报告内容 - 支持 Markdown 格式 */
  content: string
  /** 报告标题 (可选) */
  title?: string
  /** 报告摘要 (可选) */
  summary?: string
  /** 报告生成时间 (可选) */
  generatedAt?: number
  /** 报告版本 (可选) */
  version?: string
}

// ============================================================================
// 4. 错误信息 Payload
// ============================================================================

/**
 * 错误负载 - 用于传输错误信息和异常状态
 */
export interface ErrorPayload {
  /** 负载类型标识 */
  readonly type: 'error'
  /** 错误代码 - 用于程序化处理 */
  code: string
  /** 错误描述消息 */
  message: string
  /** 错误标题 (可选) */
  title?: string
  /** 详细错误内容 (可选) */
  content?: string
  /** 错误严重级别 (可选) */
  severity?: 'low' | 'medium' | 'high' | 'critical'
  /** 是否可恢复 (可选) */
  recoverable?: boolean
  /** 建议的修复操作 (可选) */
  suggestedActions?: string[]
}

// ============================================================================
// 5. 文本消息 Payload
// ============================================================================

/**
 * 文本负载 - 用于传输普通文本内容
 */
export interface TextPayload {
  /** 负载类型标识 */
  readonly type: 'text'
  /** 文本内容 */
  content: string
  /** 文本格式 (可选) */
  format?: 'plain' | 'markdown' | 'html'
  /** 文本语言 (可选) */
  language?: string
}

// ============================================================================
// 6. 系统消息 Payload
// ============================================================================

/**
 * 系统负载 - 用于传输系统级通知和状态信息
 */
export interface SystemPayload {
  /** 负载类型标识 */
  readonly type: 'system'
  /** 系统消息内容 */
  content: string
  /** 系统消息类型 (可选) */
  subType?: 'notification' | 'warning' | 'info' | 'status'
  /** 系统消息优先级 (可选) */
  priority?: 'low' | 'normal' | 'high' | 'urgent'
}

// ============================================================================
// 7. 用户消息 Payload
// ============================================================================

/**
 * 用户负载 - 用于传输用户发送的消息内容
 */
export interface UserPayload {
  /** 负载类型标识 */
  readonly type: 'user_message'
  /** 用户消息内容 */
  content: string
  /** 消息附件 (可选) */
  attachments?: MessageAttachment[]
  /** 引用的消息ID (可选) */
  replyToMessageId?: string
}

// ============================================================================
// 8. 助手消息 Payload
// ============================================================================

/**
 * 助手负载 - 用于传输AI助手的回复内容
 */
export interface AssistantPayload {
  /** 负载类型标识 */
  readonly type: 'assistant_message'
  /** 助手消息内容 */
  content: string
  /** 助手类型标识 (可选) */
  assistantType?: string
  /** 置信度分数 (可选) */
  confidence?: number
  /** 相关的工具调用 (可选) */
  toolCalls?: ToolCall[]
}

// ============================================================================
// 9. 投票消息 Payload
// ============================================================================

/**
 * 投票负载 - 用于创建和管理投票交互
 */
export interface PollPayload {
  /** 负载类型标识 */
  readonly type: 'poll'
  /** 投票问题的简要描述 */
  content: string
  /** 投票选项数据 */
  data: PollData
  /** 投票截止时间 (可选) */
  expiresAt?: number
  /** 是否允许多选 (可选) */
  allowMultiple?: boolean
  /** 是否允许匿名投票 (可选) */
  anonymous?: boolean
}

/**
 * 投票数据结构
 */
export interface PollData {
  /** 投票问题 */
  question: string
  /** 投票选项列表 */
  options: PollOption[]
  /** 总投票数 (可选) */
  totalVotes?: number
}

/**
 * 投票选项定义
 */
export interface PollOption {
  /** 选项唯一标识 */
  id: string
  /** 选项文本 */
  text: string
  /** 选项得票数 */
  votes: number
  /** 选项描述 (可选) */
  description?: string
}

// ============================================================================
// 10. 文件上传 Payload
// ============================================================================

/**
 * 文件负载 - 用于传输文件信息和上传状态
 */
export interface FilePayload {
  /** 负载类型标识 */
  readonly type: 'file'
  /** 文件信息列表 */
  files: FileInfo[]
  /** 文件上下文描述 (可选) */
  context?: string
  /** 上传会话ID (可选) */
  uploadSessionId?: string
  /** 总文件数量 (可选) */
  totalCount?: number
}

/**
 * 文件信息定义
 */
export interface FileInfo {
  /** 文件唯一标识 */
  id: string
  /** 文件名 */
  name: string
  /** 文件大小（字节） */
  size: number
  /** MIME 类型 */
  type: string
  /** 文件 URL (可选) */
  url?: string
  /** 预览内容 (可选) */
  preview?: string
  /** 上传状态 (可选) */
  uploadStatus?: 'pending' | 'uploading' | 'completed' | 'failed'
  /** 上传进度百分比 (可选) */
  uploadProgress?: number
  /** 文件哈希值 (可选) */
  hash?: string
  /** 额外元数据 (可选) */
  metadata?: Record<string, unknown>
}

// ============================================================================
// 辅助类型定义 (Helper Types)
// ============================================================================

/**
 * 消息附件定义
 */
export interface MessageAttachment {
  /** 附件类型 */
  type: 'image' | 'document' | 'audio' | 'video' | 'link' | 'other'
  /** 附件 URL */
  url: string
  /** 附件名称 (可选) */
  name?: string
  /** 附件大小 (可选) */
  size?: number
  /** 附件描述 (可选) */
  description?: string
}

/**
 * 工具调用定义
 */
export interface ToolCall {
  /** 工具调用ID */
  id: string
  /** 工具名称 */
  name: string
  /** 工具参数 */
  arguments: Record<string, unknown>
  /** 调用结果 (可选) */
  result?: unknown
  /** 调用状态 (可选) */
  status?: 'pending' | 'completed' | 'failed'
}

// ============================================================================
// 工具类和转换器 (Factory and Converters)
// ============================================================================

/**
 * 消息工厂类 - 用于类型转换和消息创建
 */
export class MessageFactory {
  /**
   * 从后端消息创建完整的客户端消息
   * @param backendMessage 后端消息数据
   * @param frontendState 前端状态数据 (可选)
   * @returns 完整的客户端消息
   */
  static createClientMessage(
    backendMessage: BackendWebSocketMessage,
    frontendState?: Partial<FrontendMessageState>
  ): ClientMessage {
    const now = Date.now()
    const defaultFrontendState: FrontendMessageState = {
      id: frontendState?.id ?? MessageFactory.generateMessageId(),
      status: frontendState?.status ?? 'sent',
      metadata: {
        ...frontendState?.metadata,
      },
      createdAt: frontendState?.createdAt ?? now,
      updatedAt: frontendState?.updatedAt ?? now,
    }

    return {
      ...backendMessage,
      ...defaultFrontendState,
    }
  }

  /**
   * 从客户端消息提取后端数据
   * @param clientMessage 完整的客户端消息
   * @returns 纯净的后端消息数据
   */
  static extractBackendMessage(clientMessage: ClientMessage): BackendWebSocketMessage {
    const {
      id: _id,
      status: _status,
      metadata: _metadata,
      createdAt: _createdAt,
      updatedAt: _updatedAt,
      ...backendMessage
    } = clientMessage

    return backendMessage as BackendWebSocketMessage
  }

  /**
   * 从客户端消息提取前端状态
   * @param clientMessage 完整的客户端消息
   * @returns 前端状态数据
   */
  static extractFrontendState(clientMessage: ClientMessage): FrontendMessageState {
    return {
      id: clientMessage.id,
      status: clientMessage.status,
      metadata: clientMessage.metadata,
      createdAt: clientMessage.createdAt,
      updatedAt: clientMessage.updatedAt,
    }
  }

  /**
   * 更新客户端消息的前端状态
   * @param clientMessage 原始客户端消息
   * @param updates 状态更新
   * @returns 更新后的客户端消息
   */
  static updateFrontendState(
    clientMessage: ClientMessage,
    updates: Partial<FrontendMessageState>
  ): ClientMessage {
    return {
      ...clientMessage,
      ...updates,
      updatedAt: Date.now(),
      metadata: {
        ...clientMessage.metadata,
        ...updates.metadata,
      },
    }
  }

  /**
   * 生成唯一的消息ID
   * @returns 唯一标识符
   */
  private static generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 创建新的流式消息
   * @param delta 文本片段
   * @param isComplete 是否完成
   * @param messageContext 消息上下文
   * @returns 流式消息
   */
  static createStreamingMessage(
    delta: string,
    isComplete: boolean,
    messageContext: Pick<BackendWebSocketMessage, 'groupChatId' | 'sessionId' | 'userId' | 'organizationId'>
  ): ClientMessage {
    const backendMessage: BackendWebSocketMessage = {
      ...messageContext,
      payload: {
        type: 'streaming',
        delta,
        isComplete,
      },
      timestamp: Date.now(),
    }

    return this.createClientMessage(backendMessage, {
      status: isComplete ? 'sent' : 'sending',
      metadata: {
        streamingState: {
          isComplete,
          accumulatedText: delta,
          lastUpdateTime: Date.now(),
        },
      },
    })
  }
}

// ============================================================================
// 工作流步骤枚举 (Workflow Steps)
// ============================================================================

/**
 * 工作流步骤枚举 - 定义处理流程的各个阶段
 */
export enum WorkflowStep {
  QUERY_PARSING_START = 'query_parsing_start',
  COUNTRY_CLASSIFICATION = 'country_classification',
  OPPORTUNITY_DIMENSION_SELECTION = 'opportunity_dimension_selection',
  DATA_COLLECTION = 'data_collection',
  ANALYSIS_PROCESSING = 'analysis_processing',
  REPORT_GENERATION = 'report_generation',
  WORKFLOW_COMPLETED = 'workflow_completed',
}

// ============================================================================
// 消息类型枚举 (Message Type Enums)
// ============================================================================

/**
 * WebSocket 消息类型枚举
 * @deprecated 请直接使用 Payload 的 type 字段
 */
export enum WebSocketMessageType {
  STREAMING = 'streaming',
  CHECKPOINT = 'checkpoint',
  REPORT = 'report',
  ERROR = 'error',
  TEXT = 'text',
  SYSTEM = 'system',
  USER_MESSAGE = 'user_message',
  ASSISTANT_MESSAGE = 'assistant_message',
  POLL = 'poll',
  FILE = 'file',
}

// ============================================================================
// 类型保护函数 (Type Guards)
// ============================================================================

/**
 * 检查是否为流式负载
 */
export function isStreamingPayload(payload: MessagePayload): payload is StreamingPayload {
  return payload.type === 'streaming'
}

/**
 * 检查是否为检查点负载
 */
export function isCheckpointPayload(payload: MessagePayload): payload is CheckpointPayload {
  return payload.type === 'checkpoint'
}

/**
 * 检查是否为报告负载
 */
export function isReportPayload(payload: MessagePayload): payload is ReportPayload {
  return payload.type === 'report'
}

/**
 * 检查是否为错误负载
 */
export function isErrorPayload(payload: MessagePayload): payload is ErrorPayload {
  return payload.type === 'error'
}

/**
 * 检查是否为文本负载
 */
export function isTextPayload(payload: MessagePayload): payload is TextPayload {
  return payload.type === 'text'
}

/**
 * 检查是否为系统负载
 */
export function isSystemPayload(payload: MessagePayload): payload is SystemPayload {
  return payload.type === 'system'
}

/**
 * 检查是否为用户负载
 */
export function isUserPayload(payload: MessagePayload): payload is UserPayload {
  return payload.type === 'user_message'
}

/**
 * 检查是否为助手负载
 */
export function isAssistantPayload(payload: MessagePayload): payload is AssistantPayload {
  return payload.type === 'assistant_message'
}

/**
 * 检查是否为投票负载
 */
export function isPollPayload(payload: MessagePayload): payload is PollPayload {
  return payload.type === 'poll'
}

/**
 * 检查是否为文件负载
 */
export function isFilePayload(payload: MessagePayload): payload is FilePayload {
  return payload.type === 'file'
}

/**
 * 检查是否为后端消息
 */
export function isBackendMessage(message: any): message is BackendWebSocketMessage {
  return (
    typeof message === 'object' &&
    message !== null &&
    typeof message.groupChatId === 'string' &&
    typeof message.sessionId === 'string' &&
    typeof message.userId === 'string' &&
    typeof message.organizationId === 'string' &&
    typeof message.payload === 'object' &&
    typeof message.timestamp === 'number'
  )
}

/**
 * 检查是否为客户端消息
 */
export function isClientMessage(message: any): message is ClientMessage {
  return (
    typeof message === 'object' &&
    message !== null &&
    typeof message.groupChatId === 'string' &&
    typeof message.sessionId === 'string' &&
    typeof message.userId === 'string' &&
    typeof message.organizationId === 'string' &&
    typeof message.payload === 'object' &&
    typeof message.timestamp === 'number' &&
    typeof message.id === 'string' &&
    typeof message.status === 'string' &&
    typeof message.metadata === 'object' &&
    typeof message.createdAt === 'number' &&
    typeof message.updatedAt === 'number'
  )
}

// ============================================================================
// 实用工具类型 (Utility Types)
// ============================================================================

/**
 * 专用消息类型定义 - 新版本使用 ClientMessage
 */
export type StreamingClientMessage = ClientMessage & { payload: StreamingPayload }
export type CheckpointClientMessage = ClientMessage & { payload: CheckpointPayload }
export type ReportClientMessage = ClientMessage & { payload: ReportPayload }
export type ErrorClientMessage = ClientMessage & { payload: ErrorPayload }
export type TextClientMessage = ClientMessage & { payload: TextPayload }
export type SystemClientMessage = ClientMessage & { payload: SystemPayload }
export type UserClientMessage = ClientMessage & { payload: UserPayload }
export type AssistantClientMessage = ClientMessage & { payload: AssistantPayload }
export type PollClientMessage = ClientMessage & { payload: PollPayload }
export type FileClientMessage = ClientMessage & { payload: FilePayload }

/**
 * @deprecated 请使用对应的 ClientMessage 类型
 */
export type StreamingMessage = BaseWebSocketMessage & { payload: StreamingPayload }
export type CheckpointMessage = BaseWebSocketMessage & { payload: CheckpointPayload }
export type ReportMessage = BaseWebSocketMessage & { payload: ReportPayload }
export type ErrorMessage = BaseWebSocketMessage & { payload: ErrorPayload }

// ============================================================================
// 类型迁移工具 (Migration Utilities)
// ============================================================================

/**
 * 类型迁移工具类 - 帮助从旧类型迁移到新类型
 */
export class TypeMigrationHelper {
  /**
   * 将旧版 BaseWebSocketMessage 转换为新的 ClientMessage
   * @param oldMessage 旧版消息
   * @returns 新版客户端消息
   */
  static migrateToClientMessage(oldMessage: BaseWebSocketMessage): ClientMessage {
    const backendMessage: BackendWebSocketMessage = {
      groupChatId: oldMessage.groupChatId,
      sessionId: oldMessage.sessionId,
      userId: oldMessage.userId,
      organizationId: oldMessage.organizationId,
      payload: oldMessage.payload,
      ...(oldMessage.error && { error: oldMessage.error }),
      timestamp: oldMessage.timestamp,
    }

    const frontendState: FrontendMessageState = {
      id: oldMessage.id,
      status: oldMessage.status ?? 'sent',
      metadata: oldMessage.metadata ?? {},
      createdAt: oldMessage.timestamp,
      updatedAt: Date.now(),
    }

    return {
      ...backendMessage,
      ...frontendState,
    }
  }

  /**
   * 将新版 ClientMessage 转换为旧版 BaseWebSocketMessage (用于向后兼容)
   * @param newMessage 新版消息
   * @returns 新版消息 (BaseWebSocketMessage 已弃用)
   */
  static migrateToBaseMessage(newMessage: ClientMessage): ClientMessage {
    // Return the same message since BaseWebSocketMessage is deprecated
    return newMessage
  }
}
