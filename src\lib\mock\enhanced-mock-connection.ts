/**
 * 增强的Mock连接管理器
 * 
 * 功能：
 * 1. 继承现有MockConnection，增加高级功能
 * 2. 支持场景切换、错误注入、自定义延迟
 * 3. 完整的生命周期管理
 * 4. 与unified-chat-store深度集成
 * 5. 智能Mock数据生成和管理
 * 
 * 设计原则：
 * - 向后兼容现有MockConnection
 * - 与MockDataManager集成
 * - 支持复杂场景和工作流
 * - 提供丰富的开发调试功能
 */

import { BaseConnection } from '@/lib/connection/base-connection'
import {
  ConnectionStatus,
  ConnectionType,
  type ConnectionConfig,
  type ConnectionResult,
} from '@/lib/connection/types'
import type { BackendWebSocketMessage, ClientMessage } from '@/types/websocket-event-type'
import { TypeMigrationHelper } from '@/types/websocket-event-type'
import { processMessage } from '../message-processor'
import { mockConfig } from './mock-config'
import { MOCK_AI_ASSISTANT_ID, MockDataFactory } from './mock-data-factory'
import { mockDataManager } from './mock-data-manager'
import { ScenarioManager, type MockScenario } from './mock-scenarios'

// ============================================================================
// 增强Mock连接配置
// ============================================================================

export interface EnhancedMockConnectionConfig extends ConnectionConfig {
  /** 默认场景ID */
  defaultScenario?: string
  /** 自动回复配置 */
  autoReply?: {
    enabled: boolean
    delay: number
    intelligent: boolean // 是否使用智能回复
  }
  /** 错误注入配置 */
  errorInjection?: {
    enabled: boolean
    rate: number // 错误概率 0-1
    types: string[] // 错误类型
  }
  /** 网络模拟配置 */
  networkSimulation?: {
    latency: number // 基础延迟
    jitter: number // 延迟抖动
    packetLoss: number // 丢包率
    reconnectRate: number // 重连成功率
  }
  /** 场景模拟配置 */
  scenarioConfig?: {
    enableAutoScenarios: boolean // 自动场景切换
    scenarioInterval: number // 场景切换间隔（毫秒）
    mixedScenarios: boolean // 支持混合场景
  }
}

// ============================================================================
// 连接状态和事件
// ============================================================================

export interface ConnectionEvent {
  type: 'connected' | 'disconnected' | 'error' | 'message' | 'scenario_changed'
  timestamp: number
  data: any
}

export interface MockConnectionStats {
  /** 连接时长 */
  connectionDuration: number
  /** 发送消息数 */
  messagesSent: number
  /** 接收消息数 */
  messagesReceived: number
  /** 错误次数 */
  errorCount: number
  /** 当前场景 */
  currentScenario: string | null
  /** 网络延迟统计 */
  latencyStats: {
    min: number
    max: number
    avg: number
  }
}

// ============================================================================
// 增强Mock连接管理器
// ============================================================================

export class EnhancedMockConnection extends BaseConnection {
  private config: EnhancedMockConnectionConfig
  private currentScenario: MockScenario | null = null
  private autoReplyTimer: NodeJS.Timeout | null = null
  private scenarioTimer: NodeJS.Timeout | null = null
  private reconnectTimer: NodeJS.Timeout | null = null
  
  // 统计数据
  private stats: MockConnectionStats = {
    connectionDuration: 0,
    messagesSent: 0,
    messagesReceived: 0,
    errorCount: 0,
    currentScenario: null,
    latencyStats: { min: 0, max: 0, avg: 0 }
  }
  
  // 事件历史
  private eventHistory: ConnectionEvent[] = []
  private readonly maxEventHistory = 1000
  
  // 延迟统计
  private latencyHistory: number[] = []
  private readonly maxLatencyHistory = 100

  constructor(config?: EnhancedMockConnectionConfig) {
    super()
    
    const mockConfigData = mockConfig.getConfig()
    this.config = {
      defaultScenario: mockConfigData.mode.defaultScenario,
      autoReply: {
        enabled: mockConfigData.behavior.autoReply,
        delay: mockConfigData.behavior.baseDelay,
        intelligent: true,
      },
      errorInjection: {
        enabled: mockConfigData.mode.allowErrorInjection,
        rate: mockConfigData.behavior.errorRate,
        types: ['network', 'timeout', 'validation'],
      },
      networkSimulation: {
        latency: mockConfigData.behavior.baseDelay,
        jitter: mockConfigData.behavior.delayVariance,
        packetLoss: 0.01,
        reconnectRate: mockConfigData.behavior.reconnectRate,
      },
      scenarioConfig: {
        enableAutoScenarios: false,
        scenarioInterval: 30000,
        mixedScenarios: true,
      },
      ...config,
    }

    this.initializeDefaultScenario()
    console.log('🎭 增强Mock连接初始化完成')
  }

  // ============================================================================
  // IConnectionManager 实现
  // ============================================================================

  async connect(config?: ConnectionConfig): Promise<ConnectionResult> {
    if (this.isDestroyed) {
      throw new Error('Enhanced mock connection has been destroyed')
    }

    if (this.status === ConnectionStatus.CONNECTED) {
      return {
        success: true,
        type: ConnectionType.MOCK,
        message: 'Already connected to Enhanced Mock service',
      }
    }

    try {
      this.setStatus(ConnectionStatus.CONNECTING)
      this.recordEvent('connected', { config })
      
      // 合并配置
      this.config = { ...this.config, ...config }
      
      // 模拟网络延迟
      const connectDelay = this.calculateNetworkDelay()
      await new Promise(resolve => setTimeout(resolve, connectDelay))
      
      // 检查连接失败概率
      if (this.shouldSimulateError('connection')) {
        throw new Error('模拟连接失败')
      }

      this.recordConnection()
      this.setStatus(ConnectionStatus.CONNECTED)
      
      // 启动自动场景切换
      this.startAutoScenarios()
      
      // 发送连接确认消息
      await this.sendWelcomeMessage()

      console.log('✅ 增强Mock连接建立成功')
      
      return {
        success: true,
        type: ConnectionType.MOCK,
        message: 'Connected to Enhanced Mock service',
      }
    } catch (error) {
      this.setStatus(ConnectionStatus.ERROR)
      this.stats.errorCount++
      this.recordEvent('error', { error: error instanceof Error ? error.message : error })
      
      const errorMessage = error instanceof Error ? error.message : 'Unknown connection error'
      return {
        success: false,
        type: ConnectionType.MOCK,
        error: errorMessage,
      }
    }
  }

  async disconnect(): Promise<void> {
    this.clearTimers()
    this.setStatus(ConnectionStatus.DISCONNECTED)
    this.recordEvent('disconnected', {})
    console.log('🔌 增强Mock连接已断开')
  }

  async sendMessage(message: BackendWebSocketMessage | ClientMessage): Promise<void> {
    if (!this.isConnected()) {
      throw new Error('Enhanced mock connection not established')
    }

    if (!this.validateMessage(message)) {
      throw new Error('Invalid message format')
    }

    const startTime = Date.now()
    
    // 转换为统一的ClientMessage格式
    let clientMessage: ClientMessage
    if ('createdAt' in message && 'updatedAt' in message) {
      clientMessage = message as ClientMessage
    } else {
      clientMessage = TypeMigrationHelper.migrateToClientMessage(message as BackendWebSocketMessage)
    }

    console.log('📨 Enhanced Mock收到消息:', {
      messageId: clientMessage.id,
      type: clientMessage.payload?.type,
      userId: clientMessage.userId,
    })

    // 统计数据
    this.stats.messagesSent++
    this.incrementMessagesSent()

    // 模拟网络延迟
    const networkDelay = this.calculateNetworkDelay()
    await new Promise(resolve => setTimeout(resolve, networkDelay))
    
    // 记录延迟
    const actualDelay = Date.now() - startTime
    this.recordLatency(actualDelay)

    // 检查是否应该注入错误
    if (this.shouldInjectError()) {
      const error = this.generateRandomError()
      this.stats.errorCount++
      this.recordEvent('error', { error, messageId: clientMessage.id })
      throw new Error(error)
    }

    // 检查丢包模拟
    if (this.shouldSimulatePacketLoss()) {
      console.log('📦 模拟丢包，消息被丢弃:', clientMessage.id)
      return
    }

    try {
      // 处理用户消息
      await this.processUserMessage(clientMessage)
      
      // 存储到Mock数据管理器
      mockDataManager.addMessage(clientMessage)
      
      // 生成AI回复（如果启用）
      if (this.config.autoReply?.enabled && clientMessage.userId !== MOCK_AI_ASSISTANT_ID) {
        await this.generateIntelligentReply(clientMessage)
      }

      this.recordEvent('message', {
        messageId: clientMessage.id,
        type: clientMessage.payload?.type,
        processed: true,
      })

    } catch (error) {
      this.stats.errorCount++
      console.error('❌ 消息处理失败:', error)
      throw error
    }
  }

  getType(): ConnectionType {
    return ConnectionType.MOCK
  }

  // ============================================================================
  // 场景管理功能
  // ============================================================================

  /**
   * 切换场景
   */
  async switchScenario(scenarioId: string): Promise<boolean> {
    try {
      const scenario = ScenarioManager.getScenario(scenarioId)
      if (!scenario) {
        console.error('未找到场景:', scenarioId)
        return false
      }

      this.currentScenario = scenario
      this.stats.currentScenario = scenarioId
      this.recordEvent('scenario_changed', { scenarioId, scenario })

      // 更新配置
      this.config.defaultScenario = scenarioId

      console.log('🎭 场景切换成功:', scenario.name)
      
      // 如果是混合场景，执行场景流程
      if (scenario.type === 'mixed') {
        await this.executeMixedScenario(scenario)
      }

      return true
    } catch (error) {
      console.error('场景切换失败:', error)
      return false
    }
  }

  /**
   * 获取可用场景列表
   */
  getAvailableScenarios(): MockScenario[] {
    return ScenarioManager.getAllScenarios()
  }

  /**
   * 获取当前场景
   */
  getCurrentScenario(): MockScenario | null {
    return this.currentScenario
  }

  // ============================================================================
  // 智能回复和消息处理
  // ============================================================================

  /**
   * 处理用户消息
   */
  private async processUserMessage(message: ClientMessage): Promise<void> {
    // 直接使用ClientMessage，无需转换
    const processedMessage = processMessage(message, {
      sessionId: message.sessionId || 'enhanced-mock-session',
      userId: message.userId || 'mock-user',
      onMessage: (msg) => {
        console.log('📤 处理用户消息:', {
          messageId: msg.id,
          userId: msg.userId,
          payloadType: msg.payload?.type,
        })
        this.emitMessage(msg)
        this.stats.messagesReceived++
      },
      onError: (error, msg) => {
        console.error('消息处理错误:', error)
        this.stats.errorCount++
      },
    })
  }

  /**
   * 生成智能回复
   */
  private async generateIntelligentReply(originalMessage: ClientMessage): Promise<void> {
    if (!this.config.autoReply?.enabled) return

    const delay = this.config.autoReply.delay + Math.random() * 1000
    
    this.autoReplyTimer = setTimeout(async () => {
      try {
        if (!this.isConnected() || this.isDestroyed) return

        let replyMessages: BackendWebSocketMessage[]

        if (this.config.autoReply?.intelligent && this.currentScenario) {
          // 基于当前场景生成回复
          replyMessages = await this.generateScenarioBasedReply(originalMessage)
        } else {
          // 生成通用回复
          replyMessages = await this.generateGenericReply(originalMessage)
        }

        // 发送回复消息序列
        for (let i = 0; i < replyMessages.length; i++) {
          const message = replyMessages[i]
          const sendDelay = i * 300 // 消息间隔

          setTimeout(() => {
            if (this.isConnected() && !this.isDestroyed) {
              this.emitMessage(message)
              this.stats.messagesReceived++
              
              // 存储到Mock数据管理器
              const clientMessage = TypeMigrationHelper.migrateToClientMessage(message)
              mockDataManager.addMessage(clientMessage)
              
              console.log('🤖 发送AI回复:', {
                messageId: message.id,
                type: message.payload?.type,
              })
            }
          }, sendDelay)
        }

      } catch (error) {
        console.error('❌ 智能回复生成失败:', error)
        this.stats.errorCount++
      }
    }, delay)
  }

  /**
   * 基于场景生成回复
   */
  private async generateScenarioBasedReply(originalMessage: ClientMessage): Promise<BackendWebSocketMessage[]> {
    if (!this.currentScenario) {
      return this.generateGenericReply(originalMessage)
    }

    const config = {
      userId: MOCK_AI_ASSISTANT_ID,
      sessionId: originalMessage.sessionId || 'enhanced-mock-session',
      groupChatId: originalMessage.groupChatId || 'enhanced-mock-group',
      organizationId: originalMessage.organizationId || 'mock-org',
    }

    // 根据场景类型生成不同类型的回复
    switch (this.currentScenario.type) {
      case 'streaming':
        return this.generateStreamingReply(originalMessage, config)
      
      case 'checkpoint':
        return this.generateFormReply(originalMessage, config)
      
      case 'report':
        return this.generateReportReply(originalMessage, config)
      
      case 'error':
        return this.generateErrorReply(originalMessage, config)
      
      default:
        return this.generateGenericReply(originalMessage)
    }
  }

  /**
   * 生成流式回复
   */
  private async generateStreamingReply(
    originalMessage: ClientMessage,
    config: any
  ): Promise<BackendWebSocketMessage[]> {
    const userContent = originalMessage.payload?.type === 'user_message' 
      ? (originalMessage.payload as any).content 
      : '用户消息'

    const responses = [
      `我理解您关于"${userContent}"的问题。`,
      '让我为您详细分析一下...',
      '根据我的分析，主要有以下几个要点：',
      '1. 数据显示当前趋势良好',
      '2. 建议采取积极的策略',
      '3. 持续监控相关指标',
      '希望这个分析对您有帮助！如果还有其他问题，请随时告诉我。',
    ]

    const fullText = responses.join('\n\n')
    return MockDataFactory.createStreamingSequence(fullText, 5, config)
  }

  /**
   * 生成表单回复
   */
  private async generateFormReply(
    originalMessage: ClientMessage,
    config: any
  ): Promise<BackendWebSocketMessage[]> {
    const preText = '为了更好地帮助您，请填写以下信息：'
    const streamingMessage = MockDataFactory.createStreamingMessage({
      delta: preText,
      isComplete: true,
      config,
      isAssistantMessage: true,
    })

    const formMessage = MockDataFactory.createSimpleForm(config)
    
    return [streamingMessage, formMessage]
  }

  /**
   * 生成报告回复
   */
  private async generateReportReply(
    originalMessage: ClientMessage,
    config: any
  ): Promise<BackendWebSocketMessage[]> {
    const preText = '正在生成详细分析报告，请稍候...'
    const streamingMessage = MockDataFactory.createStreamingMessage({
      delta: preText,
      isComplete: true,
      config,
      isAssistantMessage: true,
    })

    const reportMessage = MockDataFactory.createShortReport(config)
    
    return [streamingMessage, reportMessage]
  }

  /**
   * 生成错误回复
   */
  private async generateErrorReply(
    originalMessage: ClientMessage,
    config: any
  ): Promise<BackendWebSocketMessage[]> {
    return [MockDataFactory.createValidationError(config)]
  }

  /**
   * 生成通用回复
   */
  private async generateGenericReply(originalMessage: ClientMessage): Promise<BackendWebSocketMessage[]> {
    const config = {
      userId: MOCK_AI_ASSISTANT_ID,
      sessionId: originalMessage.sessionId || 'enhanced-mock-session',
      groupChatId: originalMessage.groupChatId || 'enhanced-mock-group',
      organizationId: originalMessage.organizationId || 'mock-org',
    }

    const genericResponses = [
      '感谢您的消息！',
      '我已经收到了您的请求，正在处理中...',
      '根据您提供的信息，我的建议是：这是一个智能化的回复。',
      '如果您还有其他问题，请随时告诉我！',
    ]

    const responseText = genericResponses.join(' ')
    return MockDataFactory.createStreamingSequence(responseText, 4, config)
  }

  // ============================================================================
  // 混合场景执行
  // ============================================================================

  /**
   * 执行混合场景
   */
  private async executeMixedScenario(scenario: MockScenario): Promise<void> {
    const steps = scenario.data?.steps || []
    if (steps.length === 0) return

    console.log('🎭 执行混合场景:', scenario.name, '步骤数:', steps.length)

    for (let i = 0; i < steps.length; i++) {
      const step = steps[i]
      const delay = step.delay || 0

      setTimeout(async () => {
        try {
          await this.executeScenarioStep(step, i, scenario)
        } catch (error) {
          console.error(`混合场景步骤 ${i + 1} 执行失败:`, error)
        }
      }, delay)
    }
  }

  /**
   * 执行场景步骤
   */
  private async executeScenarioStep(step: any, stepIndex: number, parentScenario: MockScenario): Promise<void> {
    const stepScenario = ScenarioManager.getScenario(step.scenario)
    if (!stepScenario) {
      console.error('未找到步骤场景:', step.scenario)
      return
    }

    console.log(`🎭 执行场景步骤 ${stepIndex + 1}:`, stepScenario.name)

    // 生成步骤数据
    const messages = ScenarioManager.generateScenarioData(step.scenario, {
      sessionId: 'mixed-scenario-session',
      groupChatId: 'mixed-scenario-group',
      organizationId: 'mock-org',
    })

    // 发送步骤消息
    for (let i = 0; i < messages.length; i++) {
      const message = messages[i]
      const messageDelay = i * (stepScenario.delayMs || 100)

      setTimeout(() => {
        if (this.isConnected() && !this.isDestroyed) {
          this.emitMessage(message)
          this.stats.messagesReceived++
          
          // 存储消息
          const clientMessage = TypeMigrationHelper.migrateToClientMessage(message)
          mockDataManager.addMessage(clientMessage)
        }
      }, messageDelay)
    }
  }

  // ============================================================================
  // 网络模拟和错误注入
  // ============================================================================

  /**
   * 计算网络延迟
   */
  private calculateNetworkDelay(): number {
    const { latency, jitter } = this.config.networkSimulation || { latency: 100, jitter: 50 }
    const randomJitter = (Math.random() - 0.5) * jitter
    return Math.max(0, latency + randomJitter)
  }

  /**
   * 记录延迟统计
   */
  private recordLatency(latency: number): void {
    this.latencyHistory.push(latency)
    if (this.latencyHistory.length > this.maxLatencyHistory) {
      this.latencyHistory.shift()
    }

    // 更新统计
    if (this.latencyHistory.length > 0) {
      this.stats.latencyStats.min = Math.min(...this.latencyHistory)
      this.stats.latencyStats.max = Math.max(...this.latencyHistory)
      this.stats.latencyStats.avg = this.latencyHistory.reduce((a, b) => a + b, 0) / this.latencyHistory.length
    }
  }

  /**
   * 检查是否应该注入错误
   */
  private shouldInjectError(): boolean {
    if (!this.config.errorInjection?.enabled) return false
    return Math.random() < (this.config.errorInjection.rate || 0)
  }

  /**
   * 检查是否应该模拟特定类型的错误
   */
  private shouldSimulateError(type: string): boolean {
    const errorTypes = this.config.errorInjection?.types || []
    return this.shouldInjectError() && errorTypes.includes(type)
  }

  /**
   * 检查是否应该模拟丢包
   */
  private shouldSimulatePacketLoss(): boolean {
    const packetLoss = this.config.networkSimulation?.packetLoss || 0
    return Math.random() < packetLoss
  }

  /**
   * 生成随机错误
   */
  private generateRandomError(): string {
    const errors = [
      '网络连接超时',
      '服务器处理异常',
      '数据格式验证失败',
      '权限验证失败',
      '资源访问受限',
      '请求频率过高',
    ]
    return errors[Math.floor(Math.random() * errors.length)]
  }

  // ============================================================================
  // 自动场景和生命周期管理
  // ============================================================================

  /**
   * 启动自动场景切换
   */
  private startAutoScenarios(): void {
    if (!this.config.scenarioConfig?.enableAutoScenarios) return

    const interval = this.config.scenarioConfig.scenarioInterval || 30000
    
    this.scenarioTimer = setInterval(() => {
      const scenarios = ScenarioManager.getAllScenarios()
      const randomScenario = scenarios[Math.floor(Math.random() * scenarios.length)]
      
      console.log('🎭 自动场景切换:', randomScenario.name)
      this.switchScenario(randomScenario.id)
    }, interval)
  }

  /**
   * 初始化默认场景
   */
  private initializeDefaultScenario(): void {
    if (this.config.defaultScenario) {
      const scenario = ScenarioManager.getScenario(this.config.defaultScenario)
      if (scenario) {
        this.currentScenario = scenario
        this.stats.currentScenario = scenario.id
        console.log('🎭 初始化默认场景:', scenario.name)
      }
    }
  }

  /**
   * 发送欢迎消息
   */
  private async sendWelcomeMessage(): Promise<void> {
    const scenarioInfo = this.currentScenario ? ` 当前场景：${this.currentScenario.name}` : ''
    const welcomeText = `✅ 增强Mock服务连接成功！${scenarioInfo} 准备开始智能对话。`

    const welcomeMessage = MockDataFactory.createStreamingMessage({
      delta: welcomeText,
      isComplete: true,
      config: {
        userId: MOCK_AI_ASSISTANT_ID,
        sessionId: 'enhanced-mock-session',
        groupChatId: 'enhanced-mock-group',
        organizationId: 'mock-org',
      },
      isAssistantMessage: true,
    })

    setTimeout(() => {
      this.emitMessage(welcomeMessage)
      this.stats.messagesReceived++
      
      // 存储消息
      const clientMessage = TypeMigrationHelper.migrateToClientMessage(welcomeMessage)
      mockDataManager.addMessage(clientMessage)
    }, 500)
  }

  // ============================================================================
  // 统计和监控
  // ============================================================================

  /**
   * 获取连接统计
   */
  getStats(): MockConnectionStats {
    return {
      ...this.stats,
      connectionDuration: this.isConnected() ? Date.now() - (this.connectionTime || 0) : 0,
    }
  }

  /**
   * 获取事件历史
   */
  getEventHistory(): ConnectionEvent[] {
    return [...this.eventHistory]
  }

  /**
   * 记录事件
   */
  private recordEvent(type: ConnectionEvent['type'], data: any): void {
    const event: ConnectionEvent = {
      type,
      timestamp: Date.now(),
      data,
    }

    this.eventHistory.push(event)
    if (this.eventHistory.length > this.maxEventHistory) {
      this.eventHistory.shift()
    }
  }

  /**
   * 导出诊断数据
   */
  exportDiagnostics(): string {
    return JSON.stringify({
      stats: this.getStats(),
      events: this.getEventHistory(),
      config: this.config,
      currentScenario: this.currentScenario,
      latencyHistory: this.latencyHistory,
    }, null, 2)
  }

  // ============================================================================
  // 清理和销毁
  // ============================================================================

  /**
   * 清理所有定时器
   */
  private clearTimers(): void {
    if (this.autoReplyTimer) {
      clearTimeout(this.autoReplyTimer)
      this.autoReplyTimer = null
    }
    
    if (this.scenarioTimer) {
      clearInterval(this.scenarioTimer)
      this.scenarioTimer = null
    }
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }

  /**
   * 销毁连接
   */
  destroy(): void {
    this.clearTimers()
    super.destroy()
    console.log('🗑️ 增强Mock连接已销毁')
  }
}