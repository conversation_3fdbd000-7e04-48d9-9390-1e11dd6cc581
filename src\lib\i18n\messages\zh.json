{"common": {"loading": "加载中...", "error": "错误", "success": "成功", "cancel": "取消", "confirm": "确认", "save": "保存", "delete": "删除", "edit": "编辑", "back": "返回", "next": "下一步", "previous": "上一步", "submit": "提交", "close": "关闭", "search": "搜索", "filter": "筛选", "refresh": "刷新", "retry": "重试"}, "auth": {"login": {"title": "欢迎回来", "subtitle": "登录到您的 Vibe Coding 账户", "username": "用户名或邮箱", "usernamePlaceholder": "请输入用户名或邮箱", "password": "密码", "passwordPlaceholder": "请输入密码", "forgotPassword": "忘记密码？", "loginButton": "登录", "loggingIn": "登录中...", "orLoginWith": "或者使用第三方登录", "loginWithApple": "使用 Apple 登录", "loginWithGoogle": "使用 Google 登录", "loginWithFacebook": "使用 Facebook 登录", "noAccount": "还没有账号？", "registerNow": "立即注册", "brandTitle": "Vibe Coding Refactory", "brandSubtitle": "企业级AI应用前端架构", "feature1": "✨ Vue3 到 Next.js 完整迁移", "feature2": "🚀 现代化组件系统", "feature3": "🔐 完整的认证与权限管理", "feature4": "🎨 企业级UI设计系统", "termsText": "点击继续即表示您同意我们的", "termsOfService": "服务条款", "and": "和", "privacyPolicy": "隐私政策", "period": "。"}, "register": {"title": "创建账户", "subtitle": "注册您的新账户", "name": "姓名", "namePlaceholder": "请输入您的姓名", "email": "邮箱", "emailPlaceholder": "请输入您的邮箱地址", "password": "密码", "passwordPlaceholder": "请输入密码（至少6位）", "companyName": "公司名称", "companyNamePlaceholder": "请输入公司名称", "inviteCode": "邀请码", "inviteCodePlaceholder": "请输入邀请码", "createAccountButton": "创建账户", "creatingAccount": "创建账户中...", "accountCreated": "账户创建成功！", "alreadyHaveAccount": "已有账户？", "signIn": "立即登录", "or": "或者", "registerSuccess": "注册成功！正在跳转到主页...", "registering": "注册中..."}, "validation": {"usernameRequired": "请输入用户名或邮箱", "passwordRequired": "请输入密码", "emailFormat": "请输入有效的邮箱地址", "passwordLength": "密码至少需要6个字符", "nameLength": "姓名至少需要2个字符", "companyNameLength": "公司名称至少需要2个字符", "inviteCodeLength": "邀请码格式不正确", "loginError": "登录过程中发生异常，请稍后重试", "allFieldsRequired": "请填写所有必填项", "passwordMismatch": "两次输入的密码不一致", "registerError": "注册失败，请检查网络连接或联系管理员"}, "messages": {"loginSuccess": "登录成功", "welcomeBack": "欢迎回来，{username}！", "loginFailed": "登录失败", "loginError": "登录过程中发生错误", "featureInDevelopment": "功能开发中", "forgotPasswordInDevelopment": "忘记密码功能正在开发中", "registerInDevelopment": "注册功能正在开发中"}, "legacy": {"login": "登录", "logout": "退出登录", "pleaseLogin": "请先登录", "invalidCredentials": "用户名或密码错误", "accountLocked": "账户已被锁定，请联系管理员", "userDeleted": "该用户已被删除，请联系管理员"}}, "navigation": {"chat": "Cha<PERSON>", "uiGallery": "UI Gallery", "home": "首页", "dashboard": "仪表板", "settings": "设置"}, "chat": {"metadata": {"title": "聊天助手 - 智能对话系统", "description": "与AI助手进行智能对话，获得专业的帮助和建议", "keywords": "聊天,AI,助手,对话,智能,帮助", "openGraph": {"title": "聊天助手 - 智能对话系统", "description": "与AI助手进行智能对话，获得专业的帮助和建议"}, "twitter": {"title": "聊天助手 - 智能对话系统", "description": "与AI助手进行智能对话，获得专业的帮助和建议"}, "session": {"title": "对话会话 {sessionId}", "description": "继续您的智能对话会话", "openGraph": {"title": "对话会话 - 聊天助手", "description": "继续您的智能对话会话"}, "twitter": {"title": "对话会话 - 聊天助手", "description": "继续您的智能对话会话"}}, "error": {"title": "页面错误 - 聊天助手", "description": "页面加载出现问题，请稍后重试"}}, "title": "聊天助手", "subtitle": "与AI助手开始智能对话", "session": {"title": "对话会话", "id": "会话ID", "messages": "条消息", "redirected_from": "来自"}, "footer": {"powered_by": "由 AI 技术驱动"}, "input": {"placeholder": "输入消息...", "send": "发送", "sending": "发送中...", "retry": "重试", "failed": "发送失败"}, "messages": {"user": "你", "assistant": "助手", "system": "系统", "loading": "正在思考...", "empty": {"title": "开始对话", "description": "你好！我是你的AI助手，很高兴为你服务。有什么我可以帮助你的吗？"}}, "status": {"connected": "已连接", "connecting": "连接中...", "disconnected": "已断开", "error": "连接错误", "reconnect": "重新连接"}, "errors": {"connection_failed": "连接失败，请检查网络连接", "send_failed": "消息发送失败，请重试", "load_history_failed": "历史消息加载失败", "session_not_found": "会话不存在", "hydration_failed": "页面初始化失败", "unknown": "未知错误"}, "actions": {"copy": "复制", "edit": "编辑", "delete": "删除", "retry": "重试", "clear": "清空", "scroll_to_bottom": "滚动到底部", "new_messages": "新消息"}, "welcome": "欢迎来到 SpecificAI！", "description": "您的AI助手已准备就绪。", "placeholder": "输入消息...", "send": "发送", "clear": "清空对话", "typing": "正在输入...", "security": {"error": {"noUser": "未找到用户信息，请重新登录", "noOrganization": "未找到组织信息，请联系管理员", "invalidGroupId": "无效的聊天组ID，请刷新页面重试", "unknown": "安全验证失败，未知错误"}}, "error": {"title": "发生错误", "reload": "重新加载", "initializationFailed": "聊天初始化失败：{error}", "unknown": "未知错误", "reconnect": "重新连接", "boundary": {"connection": {"title": "网络连接异常", "message": "聊天服务暂时无法连接，请检查网络后重试。"}, "security": {"title": "访问权限异常", "message": "您没有访问此聊天的权限，请重新登录。"}, "render": {"title": "界面显示异常", "message": "聊天界面出现显示问题，正在尝试恢复。"}, "unknown": {"title": "系统异常", "message": "聊天系统遇到未知错误，请稍后重试。"}, "technicalDetails": "技术详情", "retry": {"connection": "重新连接", "general": "重试"}, "reset": "重置", "refresh": "刷新页面", "maxRetriesReached": "已达到最大重试次数，请刷新页面或联系支持"}}, "interface": {"placeholder": {"group": "在群聊 {groupId} 中输入消息...", "default": "输入消息测试智能连接系统..."}}}, "ui": {"gallery": {"title": "UI 组件库", "description": "测试和调试所有UI组件的样式验证", "buttons": "按钮组件", "inputs": "输入组件", "cards": "卡片组件", "markdown": "Markdown 渲染器演示"}}, "uiGallery": {"title": "UI Component Gallery", "subtitle": "项目中可用的组件展示", "radixComponents": "Radix U<PERSON> 组件", "customComponents": "自定义组件", "componentAvailable": "组件可用"}, "errors": {"unauthorized": "未授权，请重新登录", "noPermission": "权限不足，无法访问", "userNotLogin": "用户未登录，请先登录", "userDeleted": "该用户已被删除，请联系管理员", "networkError": "网络连接失败，请检查网络设置", "serverError": "服务器内部错误", "notFound": "请求的资源不存在", "badRequest": "请求参数错误", "forbidden": "权限不足", "timeout": "请求超时", "unknown": "未知错误"}, "language": {"switch": "切换语言", "chinese": "中文", "english": "English", "japanese": "日本語", "current": "当前语言"}, "user": {"profile": "个人资料", "account": "账户", "preferences": "偏好设置", "avatar": "头像", "name": "姓名", "email": "邮箱", "role": "角色", "lastLogin": "最后登录", "memberSince": "注册时间"}, "poll": {"title": "我想更了解您", "subtitle": "请填写公司基本信息，以便我们更好地为您提供服务", "placeholder": "请描述您的公司情况或选择上方标签...", "start": "开始", "step": "第 {step} 步，共 {total} 步", "loading": "正在加载问卷选项...", "steps": {"country": {"title": "请选择您的国家/地区", "options": {"vietnam": "越南", "japan": "日本", "korea": "韩国", "eastAsia": "东亚国家", "southeastAsia": "东南亚地区"}}, "industry": {"title": "请选择您的行业类型", "options": {"technology": "科技/互联网", "manufacturing": "制造业", "finance": "金融服务", "retail": "零售/电商", "other": "其他行业"}}, "focus": {"title": "请选择您关注的领域", "options": {"aiAutomation": "AI自动化", "digitalTransformation": "数字化转型", "dataAnalytics": "数据分析", "processOptimization": "流程优化", "other": "其他领域"}}, "companyInfo": {"title": "请描述您的公司情况", "placeholder": "请详细描述您的公司规模、业务模式、主要痛点等信息..."}}}, "reportPage": {"defaultTitle": "报告页面", "reportTitle": "嵌入式报告", "reportDescription": "这是一个简化的报告页面，展示iframe嵌入功能。", "parametersTitle": "URL参数"}, "resultsPage": {"pageTitle": "结果页面", "resultsTitle": "分析结果", "description": "这里显示分析结果和相关数据。", "parametersTitle": "URL参数", "sampleReport1": "示例报告1", "sampleReport2": "示例报告2"}, "table": {"title": "标题", "status": {"completed": "已完成", "pending": "待处理", "running": "运行中", "failed": "失败"}, "date": "日期", "noData": "暂无数据"}, "dashboardPage": {"welcomeTitle": "欢迎", "welcomeMessage": "欢迎，{name}！", "dashboardTitle": "嵌入式仪表板", "dashboardDescription": "这是一个简化的仪表板页面，展示iframe嵌入功能。", "parametersTitle": "URL参数", "initializing": "正在初始化分析..."}, "ErrorPage": {"title": "出现了一些问题", "subtitle": "应用程序遇到了意外错误，我们正在努力解决", "errorDetails": "错误详情", "errorDetailsDescription": "以下信息可能有助于技术支持团队诊断问题", "errorMessage": "错误消息", "unknownError": "未知错误", "errorId": "错误ID", "occurrenceTime": "发生时间", "retry": "重试", "backToHome": "返回首页", "refreshPage": "刷新页面", "troubleshootingTitle": "故障排除建议", "troubleshooting1": "刷新页面或稍后重试", "troubleshooting2": "检查网络连接是否正常", "troubleshooting3": "清除浏览器缓存和Cookie", "troubleshooting4": "尝试使用其他浏览器访问", "stillHavingIssuesTitle": "问题仍然存在？", "stillHavingIssuesDescription": "如果错误持续出现，请联系我们的技术支持团队。请提供上述错误详情以便快速诊断。", "sendReport": "发送错误报告", "sending": "发送中...", "reportSent": "报告已发送", "resend": "重新发送", "sendingReport": "正在发送错误报告...", "sendReportFailed": "发送错误报告时出现异常，请稍后重试。", "sendFailed": "发送失败，请稍后重试", "footerCode": "错误代码: 500 | 服务器内部错误", "footerMessage": "我们已经自动记录此错误，技术团队将尽快处理。"}, "AnalysisReportsTable": {"taskName": "任务名称", "newTitle": "新标题", "status": "状态", "progress": "进度", "createdAt": "创建时间", "updatedAt": "更新时间", "actions": "操作", "completed": "已完成", "running": "进行中", "failed": "已失败", "pending": "等待中", "cancelled": "已取消"}}