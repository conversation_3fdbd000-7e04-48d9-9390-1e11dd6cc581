# Chat系统Mock模式完整实现

## 概述

为chat系统实现了完善的Mock模式支持，提供了智能Mock数据生成、Mock WebSocket Provider、开发环境集成、Mock数据管理和开发者工具等功能。该系统与现有的`unified-chat-store.ts`完全兼容，使用新的`ClientMessage`和`BackendWebSocketMessage`类型。

## 🎯 主要功能

### 1. 智能Mock数据生成
- **基于消息类型生成符合schema的mock数据**
- **支持各种消息类型**：text, streaming, checkpoint, report, error等
- **模拟真实的业务场景和数据变化**
- **智能流式文本生成**：支持分块传输和实时累积

### 2. Mock WebSocket Provider
- **完整替代真实WebSocket连接**
- **模拟连接状态变化**：connecting, connected, disconnected, error
- **支持自定义延迟和错误注入**
- **实现消息发送接收的完整模拟**
- **与现有ConnectionStore无缝集成**

### 3. 开发环境集成
- **环境变量控制Mock模式开关**
- **与ConnectionStore和unified-chat-store深度集成**
- **支持Mock模式和真实模式的无缝切换**
- **生产环境安全保护机制**

### 4. Mock数据管理
- **持久化Mock数据存储**（localStorage）
- **会话、用户、消息的完整管理**
- **支持预定义场景和动态数据生成**
- **数据导入/导出功能**
- **自动数据清理和内存优化**

### 5. 开发者工具
- **实时Mock控制面板**
- **场景切换和参数调整**
- **消息流量监控和日志记录**
- **错误注入控制**
- **性能统计和诊断信息**

## 📁 文件结构

```
src/lib/mock/
├── index.ts                           # 统一导出文件
├── mock-config.ts                     # Mock配置管理
├── mock-data-manager.ts              # Mock数据管理器
├── enhanced-mock-connection.ts       # 增强的Mock连接
├── mock-scenarios-enhanced.ts       # 增强的Mock场景定义
├── mock-websocket-provider.tsx      # Mock WebSocket Provider
├── mock-integration-guide.tsx       # 集成指南和使用示例
├── mock-data-factory.ts            # Mock数据工厂（现有）
└── mock-scenarios.ts               # 基础Mock场景（现有）

src/components/dev/
└── mock-control-panel.tsx          # Mock控制面板
```

## 🚀 快速开始

### 1. 基础集成

```typescript
// app/layout.tsx
import { MockWebSocketProvider } from '@/lib/mock'
import { MockControlPanel } from '@/lib/mock'

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        <MockWebSocketProvider
          autoEnableInDev={true}
          showDevTools={process.env.NODE_ENV === 'development'}
        >
          {children}
          <MockControlPanel />
        </MockWebSocketProvider>
      </body>
    </html>
  )
}
```

### 2. 环境变量配置

```bash
# .env.local (开发环境)
NEXT_PUBLIC_MOCK_MODE=true
NEXT_PUBLIC_MOCK_DEFAULT_SCENARIO=streaming-fast
NEXT_PUBLIC_MOCK_BASE_DELAY=500
NEXT_PUBLIC_MOCK_ERROR_RATE=0.05
NEXT_PUBLIC_MOCK_LOG_LEVEL=debug

# .env.production (生产环境)
NEXT_PUBLIC_MOCK_MODE=false
NEXT_PUBLIC_WEBSOCKET_URL=wss://your-production-websocket.com
```

### 3. 在组件中使用

```typescript
import { useMockWebSocket, useIsMockMode } from '@/lib/mock'
import { useUnifiedChatStore } from '@/stores/unified-chat-store'

export function ChatInterface() {
  const mockWebSocket = useMockWebSocket()
  const isMockMode = useIsMockMode()
  const { sendUserMessage } = useUnifiedChatStore()

  const handleSendMessage = async (content: string) => {
    await sendUserMessage(content)
  }

  const handleSwitchToDemo = async () => {
    if (isMockMode) {
      await mockWebSocket.switchScenario('product-demo-interactive')
    }
  }

  return (
    <div>
      {isMockMode && (
        <div className="mock-controls">
          <button onClick={handleSwitchToDemo}>
            切换到产品演示场景
          </button>
        </div>
      )}
      <ChatMessageList />
      <ChatInput onSend={handleSendMessage} />
    </div>
  )
}
```

## 🎭 Mock场景系统

### 预定义场景

1. **基础场景**
   - `streaming-fast` - 快速流式响应
   - `streaming-slow` - 缓慢流式响应
   - `form-simple` - 简单表单交互
   - `report-short` - 简短分析报告

2. **增强场景**
   - `data-analysis-workflow` - 完整数据分析工作流
   - `customer-service-workflow` - 智能客服对话流程
   - `product-demo-interactive` - 互动产品演示
   - `system-stress-test` - 系统压力测试

### 自定义场景

```typescript
import { EnhancedScenarioManager } from '@/lib/mock'

// 创建自定义场景
const customScenario = {
  id: 'custom-demo',
  name: '自定义演示场景',
  description: '展示自定义业务流程',
  type: 'mixed',
  category: 'business',
  steps: [
    {
      id: 'welcome',
      type: 'streaming',
      delay: 0,
      data: { text: '欢迎使用自定义演示！' }
    },
    // ... 更多步骤
  ]
}
```

## 🛠️ 开发者工具

### Mock控制面板功能

1. **场景管理**
   - 实时场景切换
   - 场景搜索和筛选
   - 随机场景测试

2. **配置调整**
   - 延迟和错误率配置
   - 行为模式调整
   - 快速预设应用

3. **数据管理**
   - 数据统计查看
   - 批量数据生成
   - 数据导入/导出

4. **实时监控**
   - 连接状态监控
   - 消息流量统计
   - 性能指标跟踪

### 调试工具

```typescript
// 浏览器控制台调试
window.__MOCK_SYSTEM__.getMockSystemStatus()
window.__MOCK_SYSTEM__.generateDemoData()
window.__MOCK_SYSTEM__.cleanupMockSystem()
```

## 🔧 配置选项

### Mock配置结构

```typescript
interface MockConfig {
  mode: {
    enabled: boolean
    defaultScenario: string
    allowScenarioSwitching: boolean
    allowErrorInjection: boolean
    showDevTools: boolean
  }
  behavior: {
    baseDelay: number
    delayVariance: number
    errorRate: number
    reconnectRate: number
    autoReply: boolean
  }
  data: {
    persistData: boolean
    maxSessions: number
    maxMessagesPerSession: number
    cleanupInterval: number
  }
  logging: {
    level: 'debug' | 'info' | 'warn' | 'error'
    verbose: boolean
    logMessageContent: boolean
  }
}
```

### 运行时配置更新

```typescript
import { updateMockConfig } from '@/lib/mock'

// 配置高性能模式
updateMockConfig({
  behavior: {
    baseDelay: 100,
    errorRate: 0,
    autoReply: true
  }
})

// 配置调试模式
updateMockConfig({
  behavior: {
    baseDelay: 1000,
    errorRate: 0.1
  },
  logging: {
    level: 'debug',
    verbose: true
  }
})
```

## 🧪 测试集成

### 测试环境设置

```typescript
// tests/setup/mock-setup.ts
import { setupTestingConfig, cleanupMockSystem } from '@/lib/mock'

beforeEach(() => {
  setupTestingConfig()
})

afterEach(() => {
  cleanupMockSystem()
})
```

### 测试示例

```typescript
import { render, screen, waitFor } from '@testing-library/react'
import { MockWebSocketProvider } from '@/lib/mock'
import { ChatInterface } from '@/components/chat/chat-interface'

test('should handle mock responses', async () => {
  render(
    <MockWebSocketProvider>
      <ChatInterface />
    </MockWebSocketProvider>
  )

  // 发送消息
  const input = screen.getByRole('textbox')
  fireEvent.change(input, { target: { value: '测试消息' } })
  fireEvent.submit(input.closest('form'))

  // 等待Mock响应
  await waitFor(() => {
    expect(screen.getByText(/我理解了您的问题/)).toBeInTheDocument()
  }, { timeout: 3000 })
})
```

## 🛡️ 安全特性

### 生产环境保护

- **环境检测**：严格禁止在生产环境启用Mock功能
- **安全审计**：记录所有Mock操作和访问尝试
- **配置验证**：多重验证确保环境配置正确性
- **错误处理**：生产环境下安全地处理Mock相关错误

### 配置安全

```typescript
// 自动环境检测和保护
import { ensureNotProduction } from '@/lib/utils/environment-security'

function enableMockFeature() {
  ensureNotProduction('enableMockFeature')
  // Mock功能代码
}
```

## 📊 性能特性

### 内存管理
- **数据限制**：自动限制Mock数据的大小和数量
- **定期清理**：自动清理过期和多余的Mock数据
- **内存监控**：实时监控内存使用情况

### 性能优化
- **批量操作**：支持批量消息处理和数据操作
- **索引优化**：优化消息索引和查询性能
- **延迟加载**：按需加载Mock场景和数据

## 🔄 兼容性

### 与现有系统集成
- ✅ **unified-chat-store.ts**：完全兼容，无需修改现有代码
- ✅ **ClientMessage类型**：完全支持新的消息类型系统
- ✅ **connection-store.ts**：无缝集成现有连接管理
- ✅ **React 18+**：支持最新的React特性和hooks

### 向后兼容性
- 保持与现有Mock系统的API兼容
- 支持旧版BaseWebSocketMessage类型
- 提供迁移工具和指南

## 📝 最佳实践

### 开发阶段
- ✅ 在开发环境自动启用Mock模式
- ✅ 使用场景切换功能测试不同业务流程
- ✅ 利用开发工具面板进行实时调试
- ✅ 定期清理Mock数据避免内存泄漏

### 测试阶段
- ✅ 使用确定性的Mock配置避免随机性
- ✅ 为不同测试场景创建专门的Mock数据集
- ✅ 测试错误场景和边界条件
- ✅ 验证Mock和真实环境的行为一致性

### 生产部署
- ❌ 绝对不要在生产环境启用Mock模式
- ✅ 使用环境变量严格控制Mock功能
- ✅ 定期审查Mock配置和权限
- ✅ 监控Mock功能的使用和性能影响

## 🐛 故障排除

### 常见问题

1. **Mock模式无法启用**
   - 检查环境变量配置
   - 确认当前环境为development
   - 验证Provider包装是否正确

2. **Mock消息不显示**
   - 检查unified-chat-store集成
   - 验证消息格式是否符合ClientMessage类型
   - 查看浏览器控制台错误信息

3. **场景切换失败**
   - 确认场景ID存在且拼写正确
   - 检查Mock连接状态
   - 验证场景配置是否正确

### 调试工具

```bash
# 浏览器控制台调试命令
window.__MOCK_SYSTEM__.getMockSystemStatus()
window.__MOCK_SYSTEM__.getMockSystemInfo()
```

## 🔮 未来扩展

### 计划功能
- [ ] 可视化场景编辑器
- [ ] 更多预定义业务场景
- [ ] 性能基准测试工具
- [ ] 云端Mock数据同步
- [ ] AI驱动的智能回复生成

### 扩展建议
- 集成更多第三方服务模拟
- 支持复杂的多用户场景
- 添加更多的错误类型和边界条件测试
- 提供Mock数据的可视化分析

## 📞 支持

如有问题或建议，请：
1. 查看本README和集成指南
2. 使用开发者工具进行调试
3. 查看浏览器控制台的详细日志
4. 参考故障排除部分

---

**Version**: 1.0.0  
**Build Date**: 2024-07-28  
**Compatibility**: React 18+, Next.js 14+, TypeScript 5+