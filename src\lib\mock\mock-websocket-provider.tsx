/**
 * Mock WebSocket Provider
 * 
 * 功能：
 * 1. 替代真实WebSocket连接的React Provider
 * 2. 与ConnectionStore集成
 * 3. 支持开发模式下的自动切换
 * 4. 提供Mock连接状态管理
 * 5. 集成开发者工具
 * 
 * 设计原则：
 * - 与现有ConnectionProvider无缝兼容
 * - 支持运行时Mock模式切换
 * - 提供丰富的开发调试功能
 * - 完全集成unified-chat-store
 */

'use client'

import React, { createContext, useContext, useEffect, useState, useCallback, useRef } from 'react'
import { EnhancedMockConnection, type EnhancedMockConnectionConfig } from './enhanced-mock-connection'
import { useConnectionStore } from '@/stores/connection-store'
import { useUnifiedChatStore } from '@/stores/unified-chat-store'
import { mockConfig, useMockConfig, type MockConfig } from './mock-config'
import { mockDataManager } from './mock-data-manager'
import { EnhancedScenarioManager, type EnhancedMockScenario } from './mock-scenarios-enhanced'
import { ConnectionType, ConnectionStatus } from '@/lib/connection/types'
import type { ClientMessage } from '@/types/websocket-event-type'

// ============================================================================
// Context 类型定义
// ============================================================================

export interface MockWebSocketContextValue {
  // Mock连接实例
  mockConnection: EnhancedMockConnection | null
  
  // 连接状态
  isConnected: boolean
  connectionStatus: ConnectionStatus
  
  // Mock模式控制
  isMockMode: boolean
  enableMockMode: () => Promise<void>
  disableMockMode: () => Promise<void>
  toggleMockMode: () => Promise<void>
  
  // 场景管理
  currentScenario: EnhancedMockScenario | null
  availableScenarios: EnhancedMockScenario[]
  switchScenario: (scenarioId: string) => Promise<boolean>
  
  // Mock配置
  mockConfig: MockConfig
  updateMockConfig: (updates: Partial<MockConfig>) => void
  
  // 数据管理
  generateScenarioData: (scenarioId: string) => Promise<ClientMessage[]>
  generateConversationData: (messageCount?: number) => Promise<ClientMessage[]>
  clearMockData: () => void
  
  // 开发工具
  showDevTools: boolean
  toggleDevTools: () => void
  exportDiagnostics: () => string
  
  // 统计信息
  connectionStats: any
  dataStats: any
}

// ============================================================================
// Context 创建
// ============================================================================

const MockWebSocketContext = createContext<MockWebSocketContextValue | null>(null)

// ============================================================================
// Provider 组件
// ============================================================================

export interface MockWebSocketProviderProps {
  children: React.ReactNode
  /** 是否在开发环境自动启用Mock模式 */
  autoEnableInDev?: boolean
  /** 默认Mock配置 */
  defaultConfig?: Partial<EnhancedMockConnectionConfig>
  /** 是否显示开发工具 */
  showDevTools?: boolean
}

export function MockWebSocketProvider({
  children,
  autoEnableInDev = true,
  defaultConfig,
  showDevTools: initialShowDevTools,
}: MockWebSocketProviderProps) {
  // ============================================================================
  // 状态管理
  // ============================================================================
  
  const [mockConnection, setMockConnection] = useState<EnhancedMockConnection | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus>(ConnectionStatus.DISCONNECTED)
  const [currentScenario, setCurrentScenario] = useState<EnhancedMockScenario | null>(null)
  const [showDevTools, setShowDevTools] = useState(initialShowDevTools ?? false)
  const [connectionStats, setConnectionStats] = useState<any>({})
  const [dataStats, setDataStats] = useState<any>({})
  
  // Hook引用
  const { config: mockConfigData, updateConfig: updateMockConfigData } = useMockConfig()
  const connectionStore = useConnectionStore()
  
  // 使用稳定的selector获取unified chat store方法
  const unifiedChatStoreActions = useUnifiedChatStore(state => ({
    handleIncomingMessage: state.handleIncomingMessage,
    getFullDiagnostics: state.getFullDiagnostics
  }))
  
  // 定时器引用
  const statsUpdateTimer = useRef<NodeJS.Timeout | null>(null)
  
  // ============================================================================
  // Mock模式检测和初始化
  // ============================================================================
  
  const isMockMode = connectionStore.isMockMode()
  
  useEffect(() => {
    // 开发环境自动启用Mock模式
    if (autoEnableInDev && process.env.NODE_ENV === 'development' && mockConfigData.mode.enabled) {
      const shouldAutoEnable = !connectionStore.connectionManager && mockConfigData.mode.enabled
      
      if (shouldAutoEnable) {
        console.log('🎭 开发环境自动启用Mock模式')
        setTimeout(() => {
          enableMockMode()
        }, 100)
      }
    }
  }, [autoEnableInDev, mockConfigData.mode.enabled])

  // ============================================================================
  // Mock连接管理
  // ============================================================================

  const createMockConnection = useCallback((): EnhancedMockConnection => {
    const config: EnhancedMockConnectionConfig = {
      defaultScenario: mockConfigData.mode.defaultScenario,
      autoReply: {
        enabled: mockConfigData.behavior.autoReply,
        delay: mockConfigData.behavior.baseDelay,
        intelligent: true,
      },
      errorInjection: {
        enabled: mockConfigData.mode.allowErrorInjection,
        rate: mockConfigData.behavior.errorRate,
        types: ['network', 'timeout', 'validation'],
      },
      networkSimulation: {
        latency: mockConfigData.behavior.baseDelay,
        jitter: mockConfigData.behavior.delayVariance,
        packetLoss: 0.01,
        reconnectRate: mockConfigData.behavior.reconnectRate,
      },
      scenarioConfig: {
        enableAutoScenarios: false,
        scenarioInterval: 30000,
        mixedScenarios: true,
      },
      ...defaultConfig,
    }

    const connection = new EnhancedMockConnection(config)
    
    // 设置事件监听器
    const statusUnsubscribe = connection.onStatus((status) => {
      setConnectionStatus(status)
      setIsConnected(status === ConnectionStatus.CONNECTED)
      
      console.log('🎭 Mock连接状态变化:', status)
    })

    const messageUnsubscribe = connection.onMessage((message) => {
      console.log('🎭 Mock消息接收:', {
        messageId: message.id,
        type: message.payload?.type,
        userId: message.userId,
      })
      
      // 转发到unified-chat-store
      unifiedChatStoreActions.handleIncomingMessage(message)
    })

    // 存储清理函数（在连接销毁时调用）
    connection.onDestroy = () => {
      statusUnsubscribe()
      messageUnsubscribe()
    }

    return connection
  }, [mockConfigData, defaultConfig, unifiedChatStoreActions])

  const enableMockMode = useCallback(async () => {
    try {
      console.log('🎭 启用Mock模式...')
      
      // 如果已经是Mock模式，直接返回
      if (isMockMode && mockConnection?.isConnected()) {
        console.log('🎭 Mock模式已启用')
        return
      }

      // 通过ConnectionStore切换到Mock模式
      await connectionStore.switchToMockMode(mockConfigData.mode.defaultScenario)
      
      // 创建增强Mock连接
      const connection = createMockConnection()
      setMockConnection(connection)
      
      // 连接Mock服务
      await connection.connect()
      
      // 初始化当前场景
      const scenario = EnhancedScenarioManager.getScenario(mockConfigData.mode.defaultScenario)
      if (scenario) {
        setCurrentScenario(scenario)
        await connection.switchScenario(scenario.id)
      }
      
      // 启动统计更新
      startStatsUpdate()
      
      console.log('✅ Mock模式启用成功')
      
    } catch (error) {
      console.error('❌ Mock模式启用失败:', error)
      throw error
    }
  }, [isMockMode, mockConnection, connectionStore, mockConfigData, createMockConnection])

  const disableMockMode = useCallback(async () => {
    try {
      console.log('🎭 禁用Mock模式...')
      
      // 停止统计更新
      stopStatsUpdate()
      
      // 断开Mock连接
      if (mockConnection) {
        await mockConnection.disconnect()
        mockConnection.destroy()
        setMockConnection(null)
      }
      
      // 重置状态
      setIsConnected(false)
      setConnectionStatus(ConnectionStatus.DISCONNECTED)
      setCurrentScenario(null)
      
      // 切换到Socket模式（如果可用）
      if (connectionStore.canUseSocketIO()) {
        await connectionStore.switchToSocketMode()
      }
      
      console.log('✅ Mock模式已禁用')
      
    } catch (error) {
      console.error('❌ Mock模式禁用失败:', error)
      throw error
    }
  }, [mockConnection, connectionStore])

  const toggleMockMode = useCallback(async () => {
    if (isMockMode) {
      await disableMockMode()
    } else {
      await enableMockMode()
    }
  }, [isMockMode, enableMockMode, disableMockMode])

  // ============================================================================
  // 场景管理
  // ============================================================================

  const availableScenarios = EnhancedScenarioManager.getAllEnhancedScenarios()

  const switchScenario = useCallback(async (scenarioId: string): Promise<boolean> => {
    if (!mockConnection) {
      console.warn('Mock连接未建立，无法切换场景')
      return false
    }

    try {
      const success = await mockConnection.switchScenario(scenarioId)
      if (success) {
        const scenario = EnhancedScenarioManager.getScenario(scenarioId)
        setCurrentScenario(scenario)
        console.log('🎭 场景切换成功:', scenario?.name)
      }
      return success
    } catch (error) {
      console.error('场景切换失败:', error)
      return false
    }
  }, [mockConnection])

  // ============================================================================
  // 数据管理
  // ============================================================================

  const generateScenarioData = useCallback(async (scenarioId: string): Promise<ClientMessage[]> => {
    try {
      const sessionId = `scenario_${scenarioId}_${Date.now()}`
      const messages = mockDataManager.generateScenarioData(scenarioId, sessionId)
      
      console.log('📊 生成场景数据:', {
        scenarioId,
        sessionId,
        messageCount: messages.length,
      })
      
      return messages
    } catch (error) {
      console.error('场景数据生成失败:', error)
      return []
    }
  }, [])

  const generateConversationData = useCallback(async (messageCount: number = 10): Promise<ClientMessage[]> => {
    try {
      const sessionId = `conversation_${Date.now()}`
      const messages = mockDataManager.generateConversationData(messageCount, sessionId)
      
      console.log('💬 生成对话数据:', {
        sessionId,
        messageCount: messages.length,
      })
      
      return messages
    } catch (error) {
      console.error('对话数据生成失败:', error)
      return []
    }
  }, [])

  const clearMockData = useCallback(() => {
    try {
      mockDataManager.clearAll()
      console.log('🗑️ Mock数据已清空')
    } catch (error) {
      console.error('清空Mock数据失败:', error)
    }
  }, [])

  // ============================================================================
  // 开发工具
  // ============================================================================

  const toggleDevTools = useCallback(() => {
    setShowDevTools(prev => !prev)
    console.log('🛠️ 开发工具切换:', !showDevTools)
  }, [showDevTools])

  const exportDiagnostics = useCallback((): string => {
    const diagnostics = {
      timestamp: new Date().toISOString(),
      mockConnection: mockConnection ? {
        isConnected: mockConnection.isConnected(),
        type: mockConnection.getType(),
        stats: mockConnection.getStats(),
        eventHistory: mockConnection.getEventHistory(),
      } : null,
      connectionStore: {
        status: connectionStore.connectionStatus,
        isConnected: connectionStore.isConnected,
        isMockMode: connectionStore.isMockMode(),
      },
      unifiedChatStore: unifiedChatStoreActions.getFullDiagnostics(),
      mockData: mockDataManager.getStats(),
      mockConfig: mockConfigData,
      currentScenario: currentScenario,
    }

    return JSON.stringify(diagnostics, null, 2)
  }, [mockConnection, connectionStore, unifiedChatStoreActions, mockDataManager, mockConfigData, currentScenario])

  // ============================================================================
  // 统计更新
  // ============================================================================

  const startStatsUpdate = useCallback(() => {
    stopStatsUpdate() // 确保不重复启动
    
    statsUpdateTimer.current = setInterval(() => {
      if (mockConnection) {
        setConnectionStats(mockConnection.getStats())
      }
      setDataStats(mockDataManager.getStats())
    }, 2000) // 每2秒更新一次
  }, [mockConnection])

  const stopStatsUpdate = useCallback(() => {
    if (statsUpdateTimer.current) {
      clearInterval(statsUpdateTimer.current)
      statsUpdateTimer.current = null
    }
  }, [])

  // ============================================================================
  // 配置更新处理
  // ============================================================================

  const updateMockConfig = useCallback((updates: Partial<MockConfig>) => {
    updateMockConfigData(updates)
    
    // 如果连接存在，更新连接配置
    if (mockConnection && updates.behavior) {
      console.log('🔧 更新Mock连接配置:', updates.behavior)
      // 这里可以添加动态更新连接配置的逻辑
    }
  }, [updateMockConfigData, mockConnection])

  // ============================================================================
  // 清理
  // ============================================================================

  useEffect(() => {
    return () => {
      stopStatsUpdate()
      if (mockConnection) {
        mockConnection.destroy()
      }
    }
  }, [stopStatsUpdate, mockConnection])

  // ============================================================================
  // Context 值
  // ============================================================================

  const contextValue: MockWebSocketContextValue = {
    // Mock连接实例
    mockConnection,
    
    // 连接状态
    isConnected,
    connectionStatus,
    
    // Mock模式控制
    isMockMode,
    enableMockMode,
    disableMockMode,
    toggleMockMode,
    
    // 场景管理
    currentScenario,
    availableScenarios,
    switchScenario,
    
    // Mock配置
    mockConfig: mockConfigData,
    updateMockConfig,
    
    // 数据管理
    generateScenarioData,
    generateConversationData,
    clearMockData,
    
    // 开发工具
    showDevTools,
    toggleDevTools,
    exportDiagnostics,
    
    // 统计信息
    connectionStats,
    dataStats,
  }

  // ============================================================================
  // 渲染
  // ============================================================================

  return (
    <MockWebSocketContext.Provider value={contextValue}>
      {children}
      
      {/* 开发模式提示 */}
      {process.env.NODE_ENV === 'development' && isMockMode && (
        <div className="fixed bottom-4 left-4 z-50 bg-yellow-100 border border-yellow-400 rounded-lg px-3 py-2 text-sm text-yellow-800 shadow-lg">
          🎭 Mock模式已启用
          {currentScenario && (
            <div className="text-xs text-yellow-600 mt-1">
              场景: {currentScenario.name}
            </div>
          )}
        </div>
      )}
    </MockWebSocketContext.Provider>
  )
}

// ============================================================================
// Hook
// ============================================================================

/**
 * 使用Mock WebSocket的Hook
 */
export function useMockWebSocket(): MockWebSocketContextValue {
  const context = useContext(MockWebSocketContext)
  
  if (!context) {
    throw new Error('useMockWebSocket must be used within a MockWebSocketProvider')
  }
  
  return context
}

/**
 * 便捷的Mock模式检测Hook
 */
export function useIsMockMode(): boolean {
  const context = useContext(MockWebSocketContext)
  return context?.isMockMode ?? false
}

/**
 * Mock场景管理Hook
 */
export function useMockScenarios() {
  const context = useContext(MockWebSocketContext)
  
  if (!context) {
    return {
      currentScenario: null,
      availableScenarios: [],
      switchScenario: async () => false,
    }
  }
  
  return {
    currentScenario: context.currentScenario,
    availableScenarios: context.availableScenarios,
    switchScenario: context.switchScenario,
  }
}

/**
 * Mock数据管理Hook
 */
export function useMockDataManager() {
  const context = useContext(MockWebSocketContext)
  
  if (!context) {
    return {
      generateScenarioData: async () => [],
      generateConversationData: async () => [],
      clearMockData: () => {},
      dataStats: {},
    }
  }
  
  return {
    generateScenarioData: context.generateScenarioData,
    generateConversationData: context.generateConversationData,
    clearMockData: context.clearMockData,
    dataStats: context.dataStats,
  }
}

/**
 * 开发工具Hook
 */
export function useMockDevTools() {
  const context = useContext(MockWebSocketContext)
  
  if (!context) {
    return {
      showDevTools: false,
      toggleDevTools: () => {},
      exportDiagnostics: () => '{}',
      connectionStats: {},
    }
  }
  
  return {
    showDevTools: context.showDevTools,
    toggleDevTools: context.toggleDevTools,
    exportDiagnostics: context.exportDiagnostics,
    connectionStats: context.connectionStats,
  }
}

// ============================================================================
// 类型导出
// ============================================================================

export type { MockWebSocketContextValue, MockWebSocketProviderProps }