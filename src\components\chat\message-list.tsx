/**
 * 消息列表组件 - 专门负责消息渲染和滚动管理
 *
 * 功能：消息列表渲染、虚拟化支持、滚动控制
 * 依赖：UnifiedMessage、StreamingMessageBubble、message-adapter
 * 性能：支持虚拟化渲染，优化长对话性能
 *
 * 数据流：Messages -> MessageList -> Individual Message Components
 */

'use client'

import React, { forwardRef, useMemo } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'
import { Send, ArrowDown } from 'lucide-react'

// 消息组件
import { UnifiedMessage } from '@/components/ui/unified-message'
import { StreamingMessageBubble } from '@/components/ui/streaming-message-bubble'
import { TypingIndicator } from '@/components/ui/typing-indicator'
import { adaptWebSocketMessage, isStreamingMessage } from '@/components/ui/message-adapter'

// UI组件
import { ScrollArea } from '@/components/ui/scroll-area'
import { Button } from '@/components/ui/button'

// 类型
import type { BaseWebSocketMessage } from '@/types/websocket-event-type'
import { isStreamingPayload } from '@/types/websocket-event-type'
import type { ChatSession } from '@/stores/session-store'

// Session Provider
import { useSession } from '@/components/providers/session-provider'

// ============================================================================
// 组件接口定义
// ============================================================================

export interface MessageListProps {
  /** 消息数据 */
  messages: BaseWebSocketMessage[]

  /** 滚动状态 */
  isAtBottom: boolean
  unreadCount: number

  /** 滚动控制 */
  onScroll: (event: React.UIEvent) => void
  onScrollToBottom: () => void
  messagesEndRef: React.RefObject<HTMLDivElement>

  /** 外观配置 */
  appearance: {
    showAvatar: boolean
    showTimestamp: boolean
    renderMode: 'bubble' | 'card' | 'system'
    userAvatarUrl?: string
    assistantAvatarUrl?: string
  }

  /** 移动端适配 */
  isMobile: boolean

  /** 事件回调 */
  onMessageRetry?: ((messageId: string) => void) | undefined
  onMessageDelete?: ((messageId: string) => void) | undefined
  onMessageCopy?: ((content: string) => void) | undefined

  /** 🎯 Pure WebSocket Flow：发送中消息 */
  pendingSentMessages?: Array<{
    tempId: string
    content: string
    timestamp: number
    sessionId: string
    userId: string
  }>
}

// ============================================================================
// 发送中消息组件
// ============================================================================

const PendingSentMessage: React.FC<{
  pendingMessage: {
    tempId: string
    content: string
    timestamp: number
    sessionId: string
    userId: string
  }
  appearance: MessageListProps['appearance']
  currentUserId: string
}> = ({ pendingMessage, appearance, currentUserId }) => {
  // 创建临时的BaseWebSocketMessage格式用于渲染
  const tempMessage: BaseWebSocketMessage = {
    id: pendingMessage.tempId,
    groupChatId: 'temp-group',
    sessionId: pendingMessage.sessionId,
    userId: pendingMessage.userId,
    organizationId: 'temp-org',
    payload: {
      type: 'streaming',
      delta: pendingMessage.content,
      isComplete: true,
    },
    timestamp: pendingMessage.timestamp,
    status: 'sending' as any,
    metadata: {}
  }

  return (
    <div className="relative">
      <div className="opacity-60">
        <MessageItem
          message={tempMessage}
          appearance={appearance}
          currentUserId={currentUserId}
          onRetry={undefined}
          onDelete={undefined}
          onCopy={undefined}
        />
      </div>
      {/* 发送中指示器 */}
      <div className="absolute top-2 right-2 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">
        发送中...
      </div>
    </div>
  )
}

// ============================================================================
// 空状态组件
// ============================================================================

const EmptyState: React.FC<{ isMobile: boolean }> = ({ isMobile }) => (
  <div className="flex flex-col items-center justify-center py-12 sm:py-20 text-center">
    <div className="w-12 h-12 sm:w-16 sm:h-16 rounded-full bg-muted flex items-center justify-center mb-4">
      <Send className="w-6 h-6 sm:w-8 sm:h-8 text-muted-foreground" />
    </div>
    <h3 className="text-base sm:text-lg font-medium text-foreground mb-2">开始对话</h3>
    <p className="text-sm sm:text-base text-muted-foreground max-w-xs sm:max-w-sm px-4 sm:px-0">
      输入消息开始与AI助手对话，我会为您提供准确和有用的回答。
    </p>
  </div>
)

// ============================================================================
// 单个消息渲染组件
// ============================================================================

interface MessageItemProps {
  message: BaseWebSocketMessage
  appearance: MessageListProps['appearance']
  currentUserId?: string
  onRetry?: ((messageId: string) => void) | undefined
  onDelete?: ((messageId: string) => void) | undefined
  onCopy?: ((content: string) => void) | undefined
}

const MessageItem: React.FC<MessageItemProps> = React.memo(
  ({ message, appearance, currentUserId, onRetry, onDelete, onCopy }) => {
    const { showAvatar, showTimestamp, renderMode, userAvatarUrl, assistantAvatarUrl } = appearance

    // 判断是否为streaming消息
    const isStreaming = isStreamingMessage(message)

    if (isStreaming) {
      // 使用StreamingMessageBubble渲染streaming消息
      const bubbleProps = adaptWebSocketMessage(message, {
        currentUserId: currentUserId || 'anonymous',
        showAvatar,
        showTimestamp,
        userAvatarUrl,
        assistantAvatarUrl,
        enableCopy: true,
        onCopy: content => {
          onCopy?.(content)
        },
      })

      if (bubbleProps) {
        return <StreamingMessageBubble key={message.id} {...bubbleProps} />
      }
    }

    // 使用UnifiedMessage渲染其他类型消息
    return (
      <UnifiedMessage
        key={message.id}
        message={message}
        showAvatar={showAvatar}
        showTimestamp={showTimestamp}
        showStatus={true}
        renderMode={renderMode}
        userAvatarUrl={userAvatarUrl}
        assistantAvatarUrl={assistantAvatarUrl}
        enableCopy={true}
        enableMenu={true}
        onRetry={onRetry ? () => onRetry(message.id) : undefined}
        onDelete={onDelete ? () => onDelete(message.id) : undefined}
        onErrorRetry={onRetry ? (errorId: string) => onRetry(errorId) : undefined}
        onErrorDismiss={onDelete ? (errorId: string) => onDelete(errorId) : undefined}
      />
    )
  }
)

MessageItem.displayName = 'MessageItem'

// ============================================================================
// 简化设计：移除复杂的虚拟化逻辑，使用常规渲染
// ============================================================================

// ============================================================================
// 常规消息列表
// ============================================================================

const RegularMessageList = forwardRef<HTMLDivElement, MessageListProps>(
  (
    {
      messages,
      isAtBottom,
      unreadCount,
      onScroll,
      onScrollToBottom,
      messagesEndRef,
      appearance,
      isMobile,
      onMessageRetry,
      onMessageDelete,
      onMessageCopy,
      pendingSentMessages = [],
    },
    ref
  ) => {
    // 🎯 获取当前用户信息用于角色判断
    const { user } = useSession()
    const currentUserId = user?.id

    // 🎯 简化消息处理 - Store层已处理streaming累积，直接使用messages
    const processedMessages = useMemo(() => {
      return messages
    }, [messages])

    // 检测是否有typing状态
    const isTyping = useMemo(() => {
      // 这里可以根据实际需求检测typing状态
      // 目前简化处理
      return false
    }, [])

    return (
      <>
        <ScrollArea className="h-full w-full" ref={ref} onScrollCapture={onScroll}>
          <div className="p-3 sm:p-4 space-y-3 sm:space-y-4">
            {processedMessages.length === 0 && pendingSentMessages.length === 0 ? (
              <EmptyState isMobile={isMobile} />
            ) : (
              <>
                {/* 已确认的消息 */}
                {processedMessages.map(message => (
                  <MessageItem
                    key={message.id}
                    message={message}
                    appearance={appearance}
                    currentUserId={currentUserId}
                    onRetry={onMessageRetry}
                    onDelete={onMessageDelete}
                    onCopy={onMessageCopy}
                  />
                ))}

                {/* 🎯 Pure WebSocket Flow：发送中的消息 */}
                {pendingSentMessages.map(pendingMessage => (
                  <motion.div
                    key={pendingMessage.tempId}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -10 }}
                    transition={{ duration: 0.2 }}
                  >
                    <PendingSentMessage
                      pendingMessage={pendingMessage}
                      appearance={appearance}
                      currentUserId={currentUserId || ''}
                    />
                  </motion.div>
                ))}
              </>
            )}

            {/* Typing指示器 */}
            <AnimatePresence>
              {isTyping && (
                <TypingIndicator
                  visible={isTyping}
                  name="AI助手"
                  avatarUrl={appearance.assistantAvatarUrl}
                  mode={appearance.renderMode === 'bubble' ? 'bubble' : 'bubble'}
                />
              )}
            </AnimatePresence>
          </div>

          {/* 滚动锚点 */}
          <div ref={messagesEndRef} />
        </ScrollArea>

        {/* 未读消息提示 */}
        <AnimatePresence>
          {!isAtBottom && unreadCount > 0 && (
            <motion.div
              className="absolute bottom-16 sm:bottom-4 left-1/2 transform -translate-x-1/2 z-10"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
            >
              <Button
                size="sm"
                className="rounded-full shadow-lg text-xs sm:text-sm px-3 sm:px-4 h-8 sm:h-9"
                onClick={onScrollToBottom}
              >
                <ArrowDown className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
                <span className="hidden xs:inline">{unreadCount} 条</span>
                <span className="xs:hidden">{unreadCount}</span>
                <span className="hidden sm:inline">新消息</span>
              </Button>
            </motion.div>
          )}
        </AnimatePresence>
      </>
    )
  }
)

RegularMessageList.displayName = 'RegularMessageList'

// ============================================================================
// 主要组件
// ============================================================================

export const MessageList = forwardRef<HTMLDivElement, MessageListProps>((props, ref) => {
  // 简化设计：直接使用常规渲染，移除复杂的虚拟化选择逻辑
  return <RegularMessageList {...props} ref={ref} />
})

MessageList.displayName = 'MessageList'

// ============================================================================
// 简化设计：移除复杂的性能优化Hook，使用常规消息渲染
// 如果未来真正需要虚拟化，可以使用成熟的第三方库如 react-window
// ============================================================================

export default MessageList
