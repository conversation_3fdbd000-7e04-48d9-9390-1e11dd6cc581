/**
 * Mock控制面板
 * 
 * 功能：
 * 1. 实时Mock控制面板
 * 2. 场景切换、参数调整
 * 3. 消息流量监控
 * 4. 错误注入控制
 * 5. 数据管理和诊断
 * 
 * 设计原则：
 * - 开发者友好的UI界面
 * - 实时状态监控和反馈
 * - 丰富的调试和分析工具
 * - 与Mock系统深度集成
 */

'use client'

import React, { useState, useEffect, useCallback, useRef } from 'react'
import { useMockWebSocket, useMockDevTools, useMockScenarios, useMockDataManager } from '@/lib/mock/mock-websocket-provider'
import { EnhancedScenarioManager, type EnhancedMockScenario } from '@/lib/mock/mock-scenarios-enhanced'
import { ConnectionStatus } from '@/lib/connection/types'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Separator } from '@/components/ui/separator'
import { AlertTriangle, Activity, Database, Settings, Play, Pause, RotateCcw, Download, Upload, Trash2 } from 'lucide-react'

// ============================================================================
// 主控制面板组件
// ============================================================================

export function MockControlPanel() {
  const mockWebSocket = useMockWebSocket()
  const { showDevTools, toggleDevTools } = useMockDevTools()

  // 只在开发环境显示
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  // 不在Mock模式下显示简化版本
  if (!mockWebSocket.isMockMode) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Card className="w-64 shadow-lg">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center gap-2">
              🎭 Mock控制面板
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <Button 
              onClick={mockWebSocket.enableMockMode}
              className="w-full"
              size="sm"
            >
              启用Mock模式
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // Mock模式激活时显示完整控制面板
  if (!showDevTools) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={toggleDevTools}
          variant="outline"
          size="sm"
          className="shadow-lg bg-white"
        >
          🛠️ 开发工具
        </Button>
      </div>
    )
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96 max-h-[80vh]">
      <Card className="shadow-xl bg-white">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center gap-2">
              🎭 Mock控制面板
              <ConnectionStatusBadge status={mockWebSocket.connectionStatus} />
            </CardTitle>
            <Button
              onClick={toggleDevTools}
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
            >
              ✕
            </Button>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <Tabs defaultValue="scenarios" className="w-full">
            <TabsList className="grid w-full grid-cols-4 text-xs">
              <TabsTrigger value="scenarios">场景</TabsTrigger>
              <TabsTrigger value="config">配置</TabsTrigger>
              <TabsTrigger value="data">数据</TabsTrigger>
              <TabsTrigger value="monitor">监控</TabsTrigger>
            </TabsList>
            
            <div className="mt-3 max-h-96 overflow-y-auto">
              <TabsContent value="scenarios" className="mt-0">
                <ScenarioControlPanel />
              </TabsContent>
              
              <TabsContent value="config" className="mt-0">
                <ConfigControlPanel />
              </TabsContent>
              
              <TabsContent value="data" className="mt-0">
                <DataControlPanel />
              </TabsContent>
              
              <TabsContent value="monitor" className="mt-0">
                <MonitorPanel />
              </TabsContent>
            </div>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}

// ============================================================================
// 连接状态徽章
// ============================================================================

function ConnectionStatusBadge({ status }: { status: ConnectionStatus }) {
  const getStatusConfig = (status: ConnectionStatus) => {
    switch (status) {
      case ConnectionStatus.CONNECTED:
        return { text: '已连接', variant: 'default' as const, color: 'bg-green-500' }
      case ConnectionStatus.CONNECTING:
        return { text: '连接中', variant: 'secondary' as const, color: 'bg-yellow-500' }
      case ConnectionStatus.DISCONNECTED:
        return { text: '未连接', variant: 'secondary' as const, color: 'bg-gray-500' }
      case ConnectionStatus.ERROR:
        return { text: '错误', variant: 'destructive' as const, color: 'bg-red-500' }
      default:
        return { text: '未知', variant: 'secondary' as const, color: 'bg-gray-500' }
    }
  }

  const config = getStatusConfig(status)
  
  return (
    <div className="flex items-center gap-1">
      <div className={`w-2 h-2 rounded-full ${config.color}`} />
      <Badge variant={config.variant} className="text-xs">
        {config.text}
      </Badge>
    </div>
  )
}

// ============================================================================
// 场景控制面板
// ============================================================================

function ScenarioControlPanel() {
  const { currentScenario, availableScenarios, switchScenario } = useMockScenarios()
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [searchQuery, setSearchQuery] = useState('')

  // 过滤场景
  const filteredScenarios = availableScenarios.filter(scenario => {
    const matchesCategory = selectedCategory === 'all' || scenario.category === selectedCategory
    const matchesSearch = searchQuery === '' || 
      scenario.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      scenario.description.toLowerCase().includes(searchQuery.toLowerCase())
    
    return matchesCategory && matchesSearch
  })

  // 获取分类列表
  const categories = Array.from(new Set(availableScenarios.map(s => s.category)))

  const handleScenarioSwitch = async (scenarioId: string) => {
    const success = await switchScenario(scenarioId)
    if (success) {
      console.log('✅ 场景切换成功')
    } else {
      console.error('❌ 场景切换失败')
    }
  }

  return (
    <div className="space-y-3">
      {/* 当前场景 */}
      {currentScenario && (
        <div className="p-2 bg-blue-50 rounded-lg border">
          <div className="flex items-center justify-between">
            <div>
              <div className="font-medium text-sm">{currentScenario.name}</div>
              <div className="text-xs text-gray-600">{currentScenario.description}</div>
            </div>
            <Badge variant="outline" className="text-xs">
              {currentScenario.difficulty}
            </Badge>
          </div>
        </div>
      )}

      {/* 搜索和筛选 */}
      <div className="space-y-2">
        <Input
          placeholder="搜索场景..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="h-8 text-sm"
        />
        
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="h-8 text-sm">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部分类</SelectItem>
            {categories.map(category => (
              <SelectItem key={category} value={category}>
                {category}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* 场景列表 */}
      <ScrollArea className="h-48">
        <div className="space-y-2">
          {filteredScenarios.map(scenario => (
            <ScenarioCard
              key={scenario.id}
              scenario={scenario}
              isActive={currentScenario?.id === scenario.id}
              onSelect={() => handleScenarioSwitch(scenario.id)}
            />
          ))}
        </div>
      </ScrollArea>

      {/* 快速操作 */}
      <div className="flex gap-2">
        <Button
          size="sm"
          variant="outline"
          onClick={() => {
            const randomScenario = availableScenarios[Math.floor(Math.random() * availableScenarios.length)]
            handleScenarioSwitch(randomScenario.id)
          }}
          className="flex-1 text-xs"
        >
          🎲 随机场景
        </Button>
        <Button
          size="sm"
          variant="outline"
          onClick={() => handleScenarioSwitch('streaming-fast')}
          className="flex-1 text-xs"
        >
          ⚡ 快速测试
        </Button>
      </div>
    </div>
  )
}

// 场景卡片组件
function ScenarioCard({ 
  scenario, 
  isActive, 
  onSelect 
}: { 
  scenario: EnhancedMockScenario
  isActive: boolean
  onSelect: () => void 
}) {
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'hard': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div 
      className={`p-2 rounded border cursor-pointer transition-colors ${
        isActive ? 'bg-blue-100 border-blue-300' : 'bg-white border-gray-200 hover:bg-gray-50'
      }`}
      onClick={onSelect}
    >
      <div className="flex items-center justify-between">
        <div className="flex-1 min-w-0">
          <div className="font-medium text-sm truncate">{scenario.name}</div>
          <div className="text-xs text-gray-600 truncate">{scenario.description}</div>
          {scenario.tags && (
            <div className="flex gap-1 mt-1">
              {scenario.tags.slice(0, 2).map(tag => (
                <Badge key={tag} variant="secondary" className="text-xs px-1 py-0">
                  {tag}
                </Badge>
              ))}
            </div>
          )}
        </div>
        <div className="flex flex-col items-end gap-1 ml-2">
          <Badge className={`text-xs ${getDifficultyColor(scenario.difficulty)}`}>
            {scenario.difficulty}
          </Badge>
          <div className="text-xs text-gray-500">
            {Math.round(scenario.estimatedDuration / 1000)}s
          </div>
        </div>
      </div>
    </div>
  )
}

// ============================================================================
// 配置控制面板
// ============================================================================

function ConfigControlPanel() {
  const { mockConfig, updateMockConfig } = useMockWebSocket()
  const [localConfig, setLocalConfig] = useState(mockConfig)

  useEffect(() => {
    setLocalConfig(mockConfig)
  }, [mockConfig])

  const handleConfigUpdate = (section: keyof typeof mockConfig, updates: any) => {
    const newConfig = {
      ...localConfig,
      [section]: { ...localConfig[section], ...updates }
    }
    setLocalConfig(newConfig)
    updateMockConfig({ [section]: { ...mockConfig[section], ...updates } })
  }

  return (
    <div className="space-y-4">
      {/* 行为配置 */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">行为配置</Label>
        
        <div className="grid grid-cols-2 gap-2 text-sm">
          <div>
            <Label className="text-xs">基础延迟 (ms)</Label>
            <Input
              type="number"
              value={localConfig.behavior.baseDelay}
              onChange={(e) => handleConfigUpdate('behavior', { baseDelay: parseInt(e.target.value) })}
              className="h-8"
            />
          </div>
          
          <div>
            <Label className="text-xs">延迟变化 (ms)</Label>
            <Input
              type="number"
              value={localConfig.behavior.delayVariance}
              onChange={(e) => handleConfigUpdate('behavior', { delayVariance: parseInt(e.target.value) })}
              className="h-8"
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-2 text-sm">
          <div>
            <Label className="text-xs">错误率 (0-1)</Label>
            <Input
              type="number"
              step="0.01"
              min="0"
              max="1"
              value={localConfig.behavior.errorRate}
              onChange={(e) => handleConfigUpdate('behavior', { errorRate: parseFloat(e.target.value) })}
              className="h-8"
            />
          </div>
          
          <div>
            <Label className="text-xs">重连率 (0-1)</Label>
            <Input
              type="number"
              step="0.01"
              min="0"
              max="1"
              value={localConfig.behavior.reconnectRate}
              onChange={(e) => handleConfigUpdate('behavior', { reconnectRate: parseFloat(e.target.value) })}
              className="h-8"
            />
          </div>
        </div>

        <div className="flex items-center justify-between">
          <Label className="text-xs">自动回复</Label>
          <Switch
            checked={localConfig.behavior.autoReply}
            onCheckedChange={(checked) => handleConfigUpdate('behavior', { autoReply: checked })}
          />
        </div>
      </div>

      <Separator />

      {/* 模式配置 */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">模式配置</Label>
        
        <div className="flex items-center justify-between">
          <Label className="text-xs">允许场景切换</Label>
          <Switch
            checked={localConfig.mode.allowScenarioSwitching}
            onCheckedChange={(checked) => handleConfigUpdate('mode', { allowScenarioSwitching: checked })}
          />
        </div>

        <div className="flex items-center justify-between">
          <Label className="text-xs">允许错误注入</Label>
          <Switch
            checked={localConfig.mode.allowErrorInjection}
            onCheckedChange={(checked) => handleConfigUpdate('mode', { allowErrorInjection: checked })}
          />
        </div>

        <div className="flex items-center justify-between">
          <Label className="text-xs">显示开发工具</Label>
          <Switch
            checked={localConfig.mode.showDevTools}
            onCheckedChange={(checked) => handleConfigUpdate('mode', { showDevTools: checked })}
          />
        </div>
      </div>

      <Separator />

      {/* 快速配置预设 */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">快速预设</Label>
        <div className="grid grid-cols-2 gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => {
              updateMockConfig({
                behavior: { ...mockConfig.behavior, baseDelay: 100, errorRate: 0 }
              })
            }}
            className="text-xs"
          >
            🚀 高速模式
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => {
              updateMockConfig({
                behavior: { ...mockConfig.behavior, baseDelay: 1000, errorRate: 0.1 }
              })
            }}
            className="text-xs"
          >
            🐌 慢速调试
          </Button>
        </div>
      </div>
    </div>
  )
}

// ============================================================================
// 数据控制面板
// ============================================================================

function DataControlPanel() {
  const { generateScenarioData, generateConversationData, clearMockData, dataStats } = useMockDataManager()
  const { exportDiagnostics } = useMockDevTools()
  const [isLoading, setIsLoading] = useState(false)

  const handleGenerateScenario = async (scenarioId: string) => {
    setIsLoading(true)
    try {
      await generateScenarioData(scenarioId)
      console.log('✅ 场景数据生成完成')
    } catch (error) {
      console.error('❌ 场景数据生成失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleGenerateConversation = async () => {
    setIsLoading(true)
    try {
      await generateConversationData(10)
      console.log('✅ 对话数据生成完成')
    } catch (error) {
      console.error('❌ 对话数据生成失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleExportDiagnostics = () => {
    const diagnostics = exportDiagnostics()
    const blob = new Blob([diagnostics], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `mock-diagnostics-${Date.now()}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <div className="space-y-4">
      {/* 数据统计 */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">数据统计</Label>
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div className="p-2 bg-gray-50 rounded">
            <div className="font-medium">总用户数</div>
            <div className="text-lg">{dataStats.totalUsers || 0}</div>
          </div>
          <div className="p-2 bg-gray-50 rounded">
            <div className="font-medium">总会话数</div>
            <div className="text-lg">{dataStats.totalSessions || 0}</div>
          </div>
          <div className="p-2 bg-gray-50 rounded">
            <div className="font-medium">总消息数</div>
            <div className="text-lg">{dataStats.totalMessages || 0}</div>
          </div>
          <div className="p-2 bg-gray-50 rounded">
            <div className="font-medium">内存使用</div>
            <div className="text-lg">{dataStats.memoryUsage || '0 KB'}</div>
          </div>
        </div>
      </div>

      <Separator />

      {/* 数据生成 */}
      <div className="space-y-2">
        <Label className="text-sm font-medium">数据生成</Label>
        
        <div className="grid grid-cols-2 gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={handleGenerateConversation}
            disabled={isLoading}
            className="text-xs"
          >
            💬 生成对话
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleGenerateScenario('streaming-fast')}
            disabled={isLoading}
            className="text-xs"
          >
            🔄 生成流式数据
          </Button>
        </div>

        <Button
          size="sm"
          variant="outline"
          onClick={() => handleGenerateScenario('data-analysis-workflow')}
          disabled={isLoading}
          className="w-full text-xs"
        >
          📊 生成分析工作流
        </Button>
      </div>

      <Separator />

      {/* 数据管理 */}
      <div className="space-y-2">
        <Label className="text-sm font-medium flex items-center gap-2">
          数据管理
          {isLoading && <div className="w-3 h-3 border border-blue-500 border-t-transparent rounded-full animate-spin" />}
        </Label>
        
        <div className="grid grid-cols-2 gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={handleExportDiagnostics}
            className="text-xs flex items-center gap-1"
          >
            <Download className="w-3 h-3" />
            导出诊断
          </Button>
          <Button
            size="sm"
            variant="outline"
            onClick={clearMockData}
            className="text-xs flex items-center gap-1 text-red-600 hover:text-red-700"
          >
            <Trash2 className="w-3 h-3" />
            清空数据
          </Button>
        </div>
      </div>
    </div>
  )
}

// ============================================================================
// 监控面板
// ============================================================================

function MonitorPanel() {
  const { connectionStats } = useMockDevTools()
  const { dataStats } = useMockDataManager()
  const [realtimeStats, setRealtimeStats] = useState<any>({})

  // 实时更新统计数据
  useEffect(() => {
    const interval = setInterval(() => {
      setRealtimeStats({
        timestamp: Date.now(),
        connectionDuration: connectionStats.connectionDuration || 0,
        messagesSent: connectionStats.messagesSent || 0,
        messagesReceived: connectionStats.messagesReceived || 0,
        errorCount: connectionStats.errorCount || 0,
        latency: connectionStats.latencyStats?.avg || 0,
      })
    }, 1000)

    return () => clearInterval(interval)
  }, [connectionStats])

  const formatDuration = (ms: number) => {
    const seconds = Math.floor(ms / 1000)
    const minutes = Math.floor(seconds / 60)
    return minutes > 0 ? `${minutes}m ${seconds % 60}s` : `${seconds}s`
  }

  const getLatencyColor = (latency: number) => {
    if (latency < 100) return 'text-green-600'
    if (latency < 300) return 'text-yellow-600'
    return 'text-red-600'
  }

  return (
    <div className="space-y-4">
      {/* 连接监控 */}
      <div className="space-y-2">
        <Label className="text-sm font-medium flex items-center gap-2">
          <Activity className="w-4 h-4" />
          连接监控
        </Label>
        
        <div className="space-y-2 text-xs">
          <div className="flex justify-between">
            <span>连接时长:</span>
            <span className="font-mono">{formatDuration(realtimeStats.connectionDuration)}</span>
          </div>
          
          <div className="flex justify-between">
            <span>平均延迟:</span>
            <span className={`font-mono ${getLatencyColor(realtimeStats.latency)}`}>
              {Math.round(realtimeStats.latency)}ms
            </span>
          </div>
          
          <div className="flex justify-between">
            <span>发送消息:</span>
            <span className="font-mono text-blue-600">{realtimeStats.messagesSent}</span>
          </div>
          
          <div className="flex justify-between">
            <span>接收消息:</span>
            <span className="font-mono text-green-600">{realtimeStats.messagesReceived}</span>
          </div>
          
          <div className="flex justify-between">
            <span>错误次数:</span>
            <span className={`font-mono ${realtimeStats.errorCount > 0 ? 'text-red-600' : 'text-gray-600'}`}>
              {realtimeStats.errorCount}
            </span>
          </div>
        </div>
      </div>

      <Separator />

      {/* 消息类型分布 */}
      {dataStats.messagesByType && (
        <div className="space-y-2">
          <Label className="text-sm font-medium">消息类型分布</Label>
          <div className="space-y-1">
            {Object.entries(dataStats.messagesByType).map(([type, count]) => (
              <div key={type} className="flex justify-between items-center text-xs">
                <span className="capitalize">{type}</span>
                <div className="flex items-center gap-2 flex-1 ml-2">
                  <Progress 
                    value={(count as number) / Math.max(...Object.values(dataStats.messagesByType)) * 100} 
                    className="h-1 flex-1"
                  />
                  <span className="font-mono w-8 text-right">{count}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      <Separator />

      {/* 系统状态 */}
      <div className="space-y-2">
        <Label className="text-sm font-medium flex items-center gap-2">
          <Database className="w-4 h-4" />
          系统状态
        </Label>
        
        <div className="space-y-1 text-xs">
          <div className="flex justify-between">
            <span>活跃会话:</span>
            <span className="font-mono">{dataStats.activeSessions || 0}</span>
          </div>
          
          <div className="flex justify-between">
            <span>内存使用:</span>
            <span className="font-mono">{dataStats.memoryUsage || '0 KB'}</span>
          </div>
          
          <div className="flex justify-between">
            <span>最新消息:</span>
            <span className="font-mono text-xs">
              {dataStats.newestMessage ? new Date(dataStats.newestMessage).toLocaleTimeString() : '无'}
            </span>
          </div>
        </div>
      </div>

      {/* 实时指示器 */}
      <div className="flex items-center justify-center pt-2">
        <div className="flex items-center gap-2 text-xs text-gray-500">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
          实时监控中
        </div>
      </div>
    </div>
  )
}

// ============================================================================
// 导出
// ============================================================================

export { MockControlPanel }