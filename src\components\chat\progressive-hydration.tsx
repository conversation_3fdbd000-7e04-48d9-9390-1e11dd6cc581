/**
 * Progressive Hydration - 渐进式hydration组件
 * 
 * 🎯 设计目标：
 * 1. 实现分阶段的组件hydration
 * 2. 优化首屏加载性能
 * 3. 提供用户体验友好的加载过程
 * 4. 支持错误恢复和降级策略
 * 
 * ⚡ 核心特性：
 * - 按优先级分批hydration
 * - 视口内优先hydration
 * - 交互驱动的hydration
 * - 自适应加载策略
 */

'use client'

import React, { 
  useState, 
  useEffect, 
  useRef, 
  useMemo, 
  useCallback,
  createContext,
  useContext
} from 'react'
import { useIntersectionObserver } from '@/hooks/use-intersection-observer'
import { useHydrationManager, type SSRData } from '@/lib/chat/hydration-manager'

// ============================================================================
// 类型定义
// ============================================================================

/**
 * Hydration 优先级
 */
export enum HydrationPriority {
  /** 立即hydration（关键组件） */
  IMMEDIATE = 0,
  /** 高优先级（用户可见区域） */
  HIGH = 1,
  /** 中等优先级（折叠区域） */
  MEDIUM = 2,
  /** 低优先级（非关键功能） */
  LOW = 3,
  /** 按需hydration（用户交互时） */
  ON_DEMAND = 4
}

/**
 * Hydration 策略
 */
export type HydrationStrategy = 
  /** 立即hydration */
  | 'immediate'
  /** 视口内hydration */
  | 'visible'
  /** 交互时hydration */
  | 'interaction'
  /** 空闲时hydration */
  | 'idle'
  /** 延迟hydration */
  | 'delayed'

/**
 * 渐进式hydration配置
 */
export interface ProgressiveHydrationConfig {
  /** Hydration策略 */
  strategy?: HydrationStrategy
  /** 优先级 */
  priority?: HydrationPriority
  /** 延迟时间（毫秒） */
  delay?: number
  /** 是否启用视口检测 */
  useIntersectionObserver?: boolean
  /** 交互事件列表 */
  interactionEvents?: string[]
  /** 错误降级组件 */
  fallback?: React.ComponentType<any>
  /** 加载占位符 */
  placeholder?: React.ComponentType<any>
}

/**
 * Hydration上下文
 */
interface ProgressiveHydrationContextValue {
  /** 已hydration的组件数量 */
  hydratedCount: number
  /** 总组件数量 */
  totalCount: number
  /** 是否全部完成 */
  isComplete: boolean
  /** 注册组件 */
  registerComponent: (id: string, config: ProgressiveHydrationConfig) => void
  /** 取消注册组件 */
  unregisterComponent: (id: string) => void
  /** 触发组件hydration */
  triggerHydration: (id: string) => void
}

// ============================================================================
// Context 创建
// ============================================================================

const ProgressiveHydrationContext = createContext<ProgressiveHydrationContextValue | null>(null)

// ============================================================================
// 主要组件
// ============================================================================

/**
 * 渐进式hydration提供者
 */
export interface ProgressiveHydrationProviderProps {
  children: React.ReactNode
  /** SSR初始数据 */
  ssrData?: SSRData
  /** 全局配置 */
  config?: {
    /** 批处理大小 */
    batchSize?: number
    /** 批处理间隔（毫秒） */
    batchInterval?: number
    /** 是否启用性能监控 */
    enablePerformanceMonitoring?: boolean
  }
  /** Hydration完成回调 */
  onComplete?: () => void
}

export function ProgressiveHydrationProvider({
  children,
  ssrData,
  config = {},
  onComplete
}: ProgressiveHydrationProviderProps) {
  const {
    batchSize = 3,
    batchInterval = 100,
    enablePerformanceMonitoring = true
  } = config

  // ============================================================================
  // 状态管理
  // ============================================================================

  const [components, setComponents] = useState<Map<string, {
    config: ProgressiveHydrationConfig
    isHydrated: boolean
    element?: HTMLElement
  }>>(new Map())

  const [hydrationQueue, setHydrationQueue] = useState<string[]>([])
  const [isProcessing, setIsProcessing] = useState(false)
  
  const hydrationManager = useHydrationManager({
    enablePerformanceMonitoring,
    enableDebug: process.env.NODE_ENV === 'development'
  })

  // ============================================================================
  // Hydration 处理
  // ============================================================================

  /**
   * 注册组件
   */
  const registerComponent = useCallback((id: string, componentConfig: ProgressiveHydrationConfig) => {
    setComponents(prev => {
      const newMap = new Map(prev)
      newMap.set(id, {
        config: componentConfig,
        isHydrated: false
      })
      return newMap
    })

    // 根据策略和优先级决定是否立即hydration
    if (componentConfig.strategy === 'immediate' || componentConfig.priority === HydrationPriority.IMMEDIATE) {
      triggerHydration(id)
    }
  }, [])

  /**
   * 取消注册组件
   */
  const unregisterComponent = useCallback((id: string) => {
    setComponents(prev => {
      const newMap = new Map(prev)
      newMap.delete(id)
      return newMap
    })
  }, [])

  /**
   * 触发组件hydration
   */
  const triggerHydration = useCallback((id: string) => {
    setHydrationQueue(prev => {
      if (!prev.includes(id)) {
        return [...prev, id]
      }
      return prev
    })
  }, [])

  /**
   * 处理hydration队列
   */
  useEffect(() => {
    if (hydrationQueue.length === 0 || isProcessing) {
      return
    }

    setIsProcessing(true)

    const processBatch = async () => {
      const batch = hydrationQueue.slice(0, batchSize)
      const remaining = hydrationQueue.slice(batchSize)

      // 处理当前批次
      await Promise.all(
        batch.map(async (componentId) => {
          try {
            await hydrateComponent(componentId)
            
            setComponents(prev => {
              const newMap = new Map(prev)
              const component = newMap.get(componentId)
              if (component) {
                newMap.set(componentId, {
                  ...component,
                  isHydrated: true
                })
              }
              return newMap
            })
          } catch (error) {
            console.error(`❌ 组件 ${componentId} hydration失败:`, error)
          }
        })
      )

      // 更新队列
      setHydrationQueue(remaining)

      // 如果还有剩余组件，继续处理
      if (remaining.length > 0) {
        setTimeout(processBatch, batchInterval)
      } else {
        setIsProcessing(false)
      }
    }

    processBatch()
  }, [hydrationQueue, isProcessing, batchSize, batchInterval])

  /**
   * 执行单个组件的hydration
   */
  const hydrateComponent = async (componentId: string): Promise<void> => {
    const component = components.get(componentId)
    if (!component || component.isHydrated) {
      return
    }

    const { config } = component
    
    // 根据策略执行不同的hydration逻辑
    switch (config.strategy) {
      case 'delayed':
        if (config.delay) {
          await new Promise(resolve => setTimeout(resolve, config.delay))
        }
        break
        
      case 'idle':
        if ('requestIdleCallback' in window) {
          await new Promise(resolve => {
            requestIdleCallback(() => resolve(void 0))
          })
        }
        break
        
      default:
        // immediate, visible, interaction 策略直接执行
        break
    }

    // 执行实际的hydration
    // 这里应该触发组件的客户端激活
    console.log(`✅ 组件 ${componentId} hydration完成`)
  }

  // ============================================================================
  // 全局hydration完成检测
  // ============================================================================

  const hydratedCount = Array.from(components.values()).filter(c => c.isHydrated).length
  const totalCount = components.size
  const isComplete = totalCount > 0 && hydratedCount === totalCount

  useEffect(() => {
    if (isComplete && totalCount > 0) {
      console.log('🎉 所有组件hydration完成')
      onComplete?.()
    }
  }, [isComplete, totalCount, onComplete])

  // ============================================================================
  // Context 值
  // ============================================================================

  const contextValue = useMemo(() => ({
    hydratedCount,
    totalCount,
    isComplete,
    registerComponent,
    unregisterComponent,
    triggerHydration
  }), [hydratedCount, totalCount, isComplete, registerComponent, unregisterComponent, triggerHydration])

  return (
    <ProgressiveHydrationContext.Provider value={contextValue}>
      {children}
    </ProgressiveHydrationContext.Provider>
  )
}

// ============================================================================
// 渐进式hydration包装器组件
// ============================================================================

export interface ProgressiveHydratedProps {
  /** 组件唯一ID */
  id: string
  /** Hydration配置 */
  config?: ProgressiveHydrationConfig
  /** 子组件 */
  children: React.ReactNode
  /** 加载中占位符 */
  placeholder?: React.ReactNode
  /** 错误降级组件 */
  fallback?: React.ReactNode
}

export function ProgressiveHydrated({
  id,
  config = {},
  children,
  placeholder = <div className="animate-pulse bg-gray-200 rounded h-16" />,
  fallback
}: ProgressiveHydratedProps) {
  const context = useContext(ProgressiveHydrationContext)
  const elementRef = useRef<HTMLDivElement>(null)
  const [isHydrated, setIsHydrated] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  if (!context) {
    throw new Error('ProgressiveHydrated must be used within ProgressiveHydrationProvider')
  }

  const {
    strategy = 'visible',
    priority = HydrationPriority.MEDIUM,
    useIntersectionObserver = true,
    interactionEvents = ['click', 'focus', 'mouseenter'],
    ...restConfig
  } = config

  // ============================================================================
  // 视口检测
  // ============================================================================

  const [isVisible, setIsVisible] = useState(false)
  
  useIntersectionObserver(
    elementRef,
    (entry) => {
      if (entry.isIntersecting && !isVisible) {
        setIsVisible(true)
        if (strategy === 'visible') {
          context.triggerHydration(id)
        }
      }
    },
    {
      enabled: useIntersectionObserver && strategy === 'visible',
      threshold: 0.1
    }
  )

  // ============================================================================
  // 交互事件处理
  // ============================================================================

  useEffect(() => {
    if (strategy !== 'interaction' || !elementRef.current) {
      return
    }

    const element = elementRef.current
    
    const handleInteraction = () => {
      context.triggerHydration(id)
    }

    interactionEvents.forEach(eventName => {
      element.addEventListener(eventName, handleInteraction, { once: true })
    })

    return () => {
      interactionEvents.forEach(eventName => {
        element.removeEventListener(eventName, handleInteraction)
      })
    }
  }, [strategy, interactionEvents, context, id])

  // ============================================================================
  // 组件注册和生命周期
  // ============================================================================

  useEffect(() => {
    const fullConfig = {
      strategy,
      priority,
      useIntersectionObserver,
      interactionEvents,
      ...restConfig
    }

    context.registerComponent(id, fullConfig)

    return () => {
      context.unregisterComponent(id)
    }
  }, [id, context, strategy, priority, useIntersectionObserver, interactionEvents, restConfig])

  // ============================================================================
  // Hydration状态同步
  // ============================================================================

  useEffect(() => {
    // 这里应该监听实际的hydration状态
    // 暂时使用简单的延迟模拟
    const timer = setTimeout(() => {
      setIsHydrated(true)
    }, 1000)

    return () => clearTimeout(timer)
  }, [])

  // ============================================================================
  // 错误处理
  // ============================================================================

  if (error && fallback) {
    return <>{fallback}</>
  }

  // ============================================================================
  // 渲染逻辑
  // ============================================================================

  return (
    <div ref={elementRef} data-hydration-id={id} data-hydration-strategy={strategy}>
      {isHydrated ? children : placeholder}
    </div>
  )
}

// ============================================================================
// 特殊用途组件
// ============================================================================

/**
 * 立即hydration的组件包装器
 */
export function ImmediateHydrated({ children, ...props }: Omit<ProgressiveHydratedProps, 'config'>) {
  return (
    <ProgressiveHydrated
      {...props}
      config={{
        strategy: 'immediate',
        priority: HydrationPriority.IMMEDIATE
      }}
    >
      {children}
    </ProgressiveHydrated>
  )
}

/**
 * 视口内hydration的组件包装器
 */
export function VisibleHydrated({ children, ...props }: Omit<ProgressiveHydratedProps, 'config'>) {
  return (
    <ProgressiveHydrated
      {...props}
      config={{
        strategy: 'visible',
        priority: HydrationPriority.HIGH,
        useIntersectionObserver: true
      }}
    >
      {children}
    </ProgressiveHydrated>
  )
}

/**
 * 交互时hydration的组件包装器
 */
export function InteractionHydrated({ 
  children, 
  events = ['click', 'focus'],
  ...props 
}: Omit<ProgressiveHydratedProps, 'config'> & { events?: string[] }) {
  return (
    <ProgressiveHydrated
      {...props}
      config={{
        strategy: 'interaction',
        priority: HydrationPriority.ON_DEMAND,
        interactionEvents: events
      }}
    >
      {children}
    </ProgressiveHydrated>
  )
}

/**
 * 空闲时hydration的组件包装器
 */
export function IdleHydrated({ children, ...props }: Omit<ProgressiveHydratedProps, 'config'>) {
  return (
    <ProgressiveHydrated
      {...props}
      config={{
        strategy: 'idle',
        priority: HydrationPriority.LOW
      }}
    >
      {children}
    </ProgressiveHydrated>
  )
}

// ============================================================================
// Hooks
// ============================================================================

/**
 * 使用渐进式hydration上下文
 */
export function useProgressiveHydration() {
  const context = useContext(ProgressiveHydrationContext)
  
  if (!context) {
    throw new Error('useProgressiveHydration must be used within ProgressiveHydrationProvider')
  }
  
  return context
}

/**
 * 使用组件hydration状态
 */
export function useComponentHydration(id: string) {
  const context = useProgressiveHydration()
  const [isHydrated, setIsHydrated] = useState(false)

  useEffect(() => {
    // 监听特定组件的hydration状态
    const checkHydration = () => {
      // 这里应该检查实际的hydration状态
      // 暂时返回简单的状态
      setIsHydrated(true)
    }

    const timer = setTimeout(checkHydration, 100)
    return () => clearTimeout(timer)
  }, [id])

  return {
    isHydrated,
    triggerHydration: () => context.triggerHydration(id)
  }
}

// ============================================================================
// 工具函数
// ============================================================================

/**
 * 根据优先级排序组件
 */
export function sortByPriority<T extends { priority: HydrationPriority }>(
  components: T[]
): T[] {
  return components.sort((a, b) => a.priority - b.priority)
}

/**
 * 检查组件是否应该立即hydration
 */
export function shouldHydrateImmediately(config: ProgressiveHydrationConfig): boolean {
  return config.strategy === 'immediate' || config.priority === HydrationPriority.IMMEDIATE
}

/**
 * 获取hydration延迟时间
 */
export function getHydrationDelay(priority: HydrationPriority): number {
  switch (priority) {
    case HydrationPriority.IMMEDIATE:
      return 0
    case HydrationPriority.HIGH:
      return 50
    case HydrationPriority.MEDIUM:
      return 100
    case HydrationPriority.LOW:
      return 200
    case HydrationPriority.ON_DEMAND:
      return Infinity
    default:
      return 100
  }
}

export default ProgressiveHydrationProvider