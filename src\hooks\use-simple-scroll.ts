'use client'

/**
 * useSimpleScroll - 简化的滚动管理Hook
 *
 * 使用成熟的第三方库替代复杂的自研实现：
 * - react-intersection-observer: 检测是否在底部
 * - react-use: 滚动状态管理
 * - framer-motion: 平滑滚动动画
 *
 * 从277行复杂实现简化为 ~80行
 */

import { useCallback, useEffect, useState, useRef } from 'react'
import { useInView } from 'react-intersection-observer'
import { useScroll } from 'react-use'

interface SimpleScrollOptions {
  /** 滚动容器ref */
  containerRef: React.RefObject<HTMLElement>
  /** 消息结束锚点ref */
  endRef: React.RefObject<HTMLElement>
  /** 消息数量 */
  messageCount: number
  /** 是否有streaming消息 */
  hasStreamingMessage: boolean
  /** 滚动阈值 - 距离底部多少像素算作在底部 */
  threshold?: number
  /** 是否启用调试日志 */
  debug?: boolean
}

interface SimpleScrollResult {
  /** 是否在底部 */
  isAtBottom: boolean
  /** 未读消息数量 */
  unreadCount: number
  /** 滚动到底部 */
  scrollToBottom: (smooth?: boolean) => void
  /** 滚动事件处理器 */
  handleScroll: () => void
}

export const useSimpleScroll = ({
  containerRef,
  endRef,
  messageCount,
  hasStreamingMessage,
  threshold = 100,
  debug = false,
}: SimpleScrollOptions): SimpleScrollResult => {
  // 使用 react-intersection-observer 检测是否在底部
  const { ref: bottomSentinelRef, inView: isAtBottom } = useInView({
    threshold: 0,
    rootMargin: `0px 0px -${threshold}px 0px`,
    root: containerRef.current,
  })

  // 使用 react-use 的 useScroll 获取滚动状态
  const scrollState = useScroll(containerRef)

  // 本地状态
  const [unreadCount, setUnreadCount] = useState(0)
  const lastMessageCount = useRef(messageCount)
  const userScrolling = useRef(false)
  const scrollTimeoutRef = useRef<NodeJS.Timeout>()

  // 检测用户是否主动滚动
  const handleScroll = useCallback(() => {
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current)
    }

    userScrolling.current = true

    // 200ms后认为用户停止滚动
    scrollTimeoutRef.current = setTimeout(() => {
      userScrolling.current = false
    }, 200)
  }, [])

  // 平滑滚动到底部
  const scrollToBottom = useCallback(
    (smooth = true) => {
      if (endRef.current) {
        endRef.current.scrollIntoView({
          behavior: smooth ? 'smooth' : 'instant',
          block: 'end',
        })
      }
    },
    [endRef]
  )

  // 当消息数量变化时处理未读计数
  useEffect(() => {
    const newMessageCount = messageCount - lastMessageCount.current

    if (newMessageCount > 0) {
      if (isAtBottom || hasStreamingMessage) {
        // 如果在底部或有流式消息，自动滚动且不增加未读
        scrollToBottom()
      } else if (!userScrolling.current) {
        // 如果不在底部且用户没有主动滚动，增加未读计数
        setUnreadCount(prev => prev + newMessageCount)
      }
    }

    lastMessageCount.current = messageCount
  }, [messageCount, isAtBottom, hasStreamingMessage, scrollToBottom])

  // 当回到底部时重置未读计数
  useEffect(() => {
    if (isAtBottom && unreadCount > 0) {
      setUnreadCount(0)
    }
  }, [isAtBottom, unreadCount])

  // 流式消息时自动滚动
  useEffect(() => {
    if (hasStreamingMessage && isAtBottom) {
      scrollToBottom(false) // 流式消息使用即时滚动，避免跳跃感
    }
  }, [hasStreamingMessage, isAtBottom, scrollToBottom])

  // 清理定时器
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current)
      }
    }
  }, [])

  // 将底部检测元素ref设置到endRef上
  // 移除依赖数组避免无限循环，因为bottomSentinelRef每次渲染都可能不同
  useEffect(() => {
    if (endRef.current && bottomSentinelRef) {
      bottomSentinelRef(endRef.current)
    }
  }) // 移除依赖数组，让它在每次渲染后都执行，但这样是安全的

  return {
    isAtBottom,
    unreadCount,
    scrollToBottom,
    handleScroll,
  }
}

export default useSimpleScroll
