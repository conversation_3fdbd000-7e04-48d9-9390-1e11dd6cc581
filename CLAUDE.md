# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 必须使用中文进行回复

# REQUIREMENT

**Always use sub agents**

## Usage

## Development Commands

**Core Development:**

- `pnpm run dev` - Start development server with Turbo
- `pnpm run dev:development` - Development environment with specific config
- `pnpm run dev:production` - Production-like development mode
- `pnpm run build` - Build for production (includes prebuild/postbuild scripts)
- `pnpm start` - Start production server

**Code Quality:**

- `pnpm run lint` - Run ESLint
- `pnpm run lint:fix` - Fix ESLint issues automatically
- `pnpm run type-check` - TypeScript type checking
- `pnpm run type-check:watch` - Watch mode type checking
- `pnpm run format` - Format code with Prettier
- `pnpm run format:check` - Check code formatting

**Utilities:**

- `pnpm run clean` - Clean build artifacts
- `pnpm run clean:cache` - Clean caches

## Project Architecture

This is a Next.js 14 (App Router) application with TypeScript, built around embedded iframe functionality and multi-tenant authentication.

### Core Technologies

- **Framework:** Next.js 14+ with App Router
- **Auth:** Better Auth with Google OAuth, email/password, phone OTP
- **Database:** PostgreSQL with connection pooling
- **State Management:** Zustand with Immer middleware
- **UI:** Radix UI primitives + Tailwind CSS
- **Real-time:** Socket.IO integration (through custom hooks)
- **Testing:** Playwright for E2E testing

### Key Architectural Patterns

**Authentication System:**

- All authentication is handled through Better Auth (`src/lib/auth/auth.ts`)
- User sessions support cross-subdomain cookies for embedded contexts
- Phone number verification with OTP integration
- FastAPI backend synchronization for user data

**HTTP Request Management:**

- **MUST** use `src/lib/http/unified-http.ts` for all API calls
- Never use raw `fetch` or `axios` directly in components
- Provides hooks: `useHttp`, `useGet`, `usePost`, `useAutoGet`, `useConditionalGet`
- Built-in error handling, loading states, and toast notifications

**Embed/Iframe System:**

- Dedicated iframe pages under `src/app/[locale]/embed/[page]/`
- All embed state managed through `EmbedContext` (`src/contexts/embed-context.tsx`)
- Use `useEmbed()` hook to access user info, tokens, and parent communication
- Separate component tree under `src/components/embed/`

**Internationalization:**

- All user-facing routes are under `[locale]` dynamic route
- Messages in `src/lib/i18n/messages/` (en.json, ja.json, zh.json)
- Use `next-intl` for translations

### Directory Structure Rules

**Components Organization:**

- `src/components/ui/` - Reusable UI primitives (Button, Card, etc.)
- `src/components/forms/` - Form-specific components
- `src/components/embed/` - Iframe-specific components only
- `src/components/layout/` - Layout and navigation components

**State Management:**

- `src/stores/` - Zustand stores (files end with `-store.ts`)
- Use Immer middleware for complex state updates
- Create custom selector hooks for performance

**Hooks:**

- `src/hooks/` - Global custom hooks (prefix with `use-`)
- `src/lib/hooks/` - Library-specific hook utilities

**API Routes:**

- `src/app/api/[...proxy]/` - Proxy to FastAPI backend
- `src/app/api/auth/` - Better Auth endpoints
- `src/app/api/embed/` - Iframe-specific API endpoints

### Critical Development Rules

**HTTP Requests:**

- Always use hooks from `src/lib/http/unified-http.ts`
- For authenticated requests, the system automatically handles tokens
- Use `useConditionalGet` for requests that depend on auth state
- Legacy compatibility available through `CompatibilityAPI`

**Embed Development:**

- All embed pages must be wrapped with `EmbedProvider`
- Access embed context via `useEmbed()` hook only
- Communication with parent frames through `notifyParent` function
- Token validation happens automatically in embed routes

**State Management:**

- Create stores per domain/feature area
- Use Immer for nested state updates
- Always create selector hooks for component consumption

**Authentication:**

- Session management is automatic through Better Auth
- Use `useAuth.useSession()` for current user state
- Support for email/password, Google OAuth, and phone OTP
- Automatic session refresh and cookie management

## Build System Notes

- Build process includes UI gallery exclusion (prebuild/postbuild scripts)
- Turbo mode enabled for faster development
- Memory optimization with increased Node.js heap size
- TypeScript incremental compilation for faster type checking
