/**
 * SSR Chat Provider - 支持服务端渲染的聊天系统Provider
 * 
 * 🎯 设计目标：
 * 1. 避免hydration不匹配问题
 * 2. 支持初始消息数据预加载
 * 3. 处理服务端和客户端状态差异
 * 4. 提供渐进式增强体验
 * 
 * ⚡ 核心特性：
 * - 服务端和客户端状态同步
 * - 防止hydration错误
 * - 支持初始数据注入
 * - 客户端降级策略
 */

'use client'

import React, { 
  createContext, 
  useContext, 
  useMemo, 
  useCallback, 
  useEffect, 
  useState,
  useRef
} from 'react'
import dynamic from 'next/dynamic'
import type { ClientMessage } from '@/types/websocket-event-type'
import type { ChatSession } from '@/stores/session-store'
import type { ChatAPI, ChatProviderProps } from './chat-provider'

// 动态导入客户端专用组件，避免SSR错误
const ClientOnlyChatProvider = dynamic(
  () => import('./chat-provider').then(mod => ({ default: mod.ChatProvider })),
  { 
    ssr: false,
    loading: () => <SSRChatSkeleton />
  }
)

// 扩展props以支持onReady回调
interface ClientOnlyChatProviderProps extends ChatProviderProps {
  onReady?: (api: ChatAPI) => void
}

// 客户端ChatProvider包装器
const ClientChatProviderWrapper: React.FC<ClientOnlyChatProviderProps> = ({ 
  children, 
  onReady, 
  ...props 
}) => {
  const [apiReady, setApiReady] = useState(false)

  return (
    <ClientOnlyChatProvider {...props}>
      <ClientChatAPIBridge onReady={onReady} onApiReady={setApiReady}>
        {children}
      </ClientChatAPIBridge>
    </ClientOnlyChatProvider>
  )
}

// API桥接组件
const ClientChatAPIBridge: React.FC<{
  children: React.ReactNode
  onReady?: (api: ChatAPI) => void
  onApiReady?: (ready: boolean) => void
}> = ({ children, onReady, onApiReady }) => {
  useEffect(() => {
    // 延迟调用以确保Provider完全初始化
    const timer = setTimeout(() => {
      try {
        // 这里可以从context获取API并调用回调
        onApiReady?.(true)
        // onReady?.(api) // 需要从实际的context中获取
      } catch (error) {
        console.error('客户端API桥接失败:', error)
      }
    }, 100)

    return () => clearTimeout(timer)
  }, [onReady, onApiReady])

  return <>{children}</>
}

// ============================================================================
// SSR相关类型定义
// ============================================================================

/**
 * SSR初始化数据
 */
export interface SSRChatInitialData {
  /** 初始消息列表 */
  messages?: ClientMessage[]
  /** 当前会话信息 */
  currentSession?: ChatSession | null
  /** 会话统计信息 */
  sessionStats?: {
    totalMessages: number
    userMessages: number
    assistantMessages: number
    lastMessageTime: number
  }
  /** 服务端时间戳 */
  serverTimestamp?: number
}

/**
 * SSR Chat Provider 属性
 */
export interface SSRChatProviderProps extends Omit<ChatProviderProps, 'children'> {
  children: React.ReactNode
  /** SSR初始数据 */
  initialData?: SSRChatInitialData
  /** 是否启用SSR */
  enableSSR?: boolean
  /** SSR降级策略 */
  fallbackStrategy?: 'skeleton' | 'empty' | 'error'
  /** hydration完成回调 */
  onHydrationComplete?: () => void
  /** hydration错误回调 */
  onHydrationError?: (error: Error) => void
  /** 客户端API就绪回调 */
  onClientReady?: (api: ChatAPI) => void
}

/**
 * SSR Chat API - 在hydration之前提供基础功能
 */
interface SSRChatAPI extends Omit<ChatAPI, 'sendMessage' | 'retryMessage' | 'reconnect'> {
  /** 是否已完成hydration */
  isHydrated: boolean
  /** 发送消息（SSR版本） */
  sendMessage: (content: string) => Promise<void>
  /** 重试消息（SSR版本） */
  retryMessage: (messageId: string) => Promise<void>
  /** 重连（SSR版本） */
  reconnect: () => Promise<void>
  /** SSR初始数据 */
  initialData: SSRChatInitialData | null
}

// ============================================================================
// Context 创建
// ============================================================================

const SSRChatContext = createContext<SSRChatAPI | null>(null)

// ============================================================================
// SSR Chat Provider 实现
// ============================================================================

export const SSRChatProvider: React.FC<SSRChatProviderProps> = ({
  children,
  initialData,
  enableSSR = true,
  fallbackStrategy = 'skeleton',
  onHydrationComplete,
  onHydrationError,
  onClientReady,
  ...chatProviderProps
}) => {
  // ============================================================================
  // Hydration 状态管理
  // ============================================================================
  
  const [isHydrated, setIsHydrated] = useState(false)
  const [hydrationError, setHydrationError] = useState<Error | null>(null)
  const [clientAPI, setClientAPI] = useState<ChatAPI | null>(null)
  const hydrationCompleteRef = useRef(false)

  // ============================================================================
  // SSR安全的状态初始化
  // ============================================================================
  
  const [ssrSafeState, setSSRSafeState] = useState(() => ({
    messages: initialData?.messages || [],
    currentSession: initialData?.currentSession || null,
    connectionStatus: 'disconnected' as const,
    isConnected: false,
    connectionError: undefined as string | undefined,
  }))

  // ============================================================================
  // Hydration 处理
  // ============================================================================
  
  useEffect(() => {
    // 客户端hydration检测
    if (typeof window !== 'undefined' && !hydrationCompleteRef.current) {
      try {
        // 延迟设置hydration状态，确保DOM完全同步
        const timer = setTimeout(() => {
          setIsHydrated(true)
          hydrationCompleteRef.current = true
          onHydrationComplete?.()
        }, 0)

        return () => clearTimeout(timer)
      } catch (error) {
        const hydrationErr = error instanceof Error 
          ? error 
          : new Error('Hydration failed')
        
        setHydrationError(hydrationErr)
        onHydrationError?.(hydrationErr)
        console.error('❌ Chat SSR Hydration失败:', hydrationErr)
      }
    }
  }, [onHydrationComplete, onHydrationError])

  // ============================================================================
  // SSR API 实现
  // ============================================================================
  
  const ssrAPI = useMemo<SSRChatAPI>(() => {
    // 如果已经hydrated且有客户端API，则使用客户端API
    if (isHydrated && clientAPI) {
      return {
        ...clientAPI,
        isHydrated: true,
        initialData,
      }
    }

    // SSR/预hydration状态的API
    return {
      // 基础状态
      isHydrated: false,
      initialData,
      
      // 消息操作（SSR期间为stub实现）
      sendMessage: async (content: string) => {
        if (isHydrated && clientAPI) {
          return clientAPI.sendMessage(content)
        }
        console.warn('💡 SSR模式下发送消息被延迟，等待hydration完成')
        // 可以在这里实现消息队列，hydration后发送
      },
      
      retryMessage: async (messageId: string) => {
        if (isHydrated && clientAPI) {
          return clientAPI.retryMessage(messageId)
        }
        console.warn('💡 SSR模式下重试消息被延迟，等待hydration完成')
      },
      
      updateMessage: (messageId: string, updates) => {
        if (isHydrated && clientAPI) {
          return clientAPI.updateMessage(messageId, updates)
        }
        console.warn('💡 SSR模式下更新消息被延迟，等待hydration完成')
        return false
      },
      
      deleteMessage: (messageId: string) => {
        if (isHydrated && clientAPI) {
          return clientAPI.deleteMessage(messageId)
        }
        console.warn('💡 SSR模式下删除消息被延迟，等待hydration完成')
        return false
      },
      
      // 消息查询（使用SSR安全数据）
      messages: ssrSafeState.messages,
      pendingMessages: [],
      getMessage: (messageId: string) => {
        return ssrSafeState.messages.find(msg => msg.id === messageId)
      },
      searchMessages: (query: string) => {
        return ssrSafeState.messages.filter(msg => {
          const content = 'content' in msg.payload ? msg.payload.content : ''
          return content.toLowerCase().includes(query.toLowerCase())
        })
      },
      
      // 会话管理
      currentSession: ssrSafeState.currentSession,
      createSession: async () => {
        if (isHydrated && clientAPI) {
          return clientAPI.createSession()
        }
        throw new Error('会话创建需要等待hydration完成')
      },
      switchSession: async (sessionId: string) => {
        if (isHydrated && clientAPI) {
          return clientAPI.switchSession(sessionId)
        }
        console.warn('💡 SSR模式下切换会话被延迟，等待hydration完成')
      },
      deleteSession: (sessionId: string) => {
        if (isHydrated && clientAPI) {
          return clientAPI.deleteSession(sessionId)
        }
        console.warn('💡 SSR模式下删除会话被延迟，等待hydration完成')
      },
      
      // 连接状态（SSR期间为静态值）
      connectionStatus: ssrSafeState.connectionStatus,
      isConnected: ssrSafeState.isConnected,
      connectionError: ssrSafeState.connectionError,
      reconnect: async () => {
        if (isHydrated && clientAPI) {
          return clientAPI.reconnect()
        }
        console.warn('💡 SSR模式下重连被延迟，等待hydration完成')
      },
      
      // UI状态和交互（SSR友好的默认值）
      input: {
        content: '',
        isTyping: false,
        isFocused: false,
        updateContent: (content: string) => {
          if (isHydrated && clientAPI) {
            clientAPI.input.updateContent(content)
          }
        },
        clearContent: () => {
          if (isHydrated && clientAPI) {
            clientAPI.input.clearContent()
          }
        },
        setFocus: (focused: boolean) => {
          if (isHydrated && clientAPI) {
            clientAPI.input.setFocus(focused)
          }
        }
      },
      
      interaction: {
        selectedMessages: [],
        editingMessage: null,
        selectMessage: (messageId: string, multiple?: boolean) => {
          if (isHydrated && clientAPI) {
            clientAPI.interaction.selectMessage(messageId, multiple)
          }
        },
        clearSelection: () => {
          if (isHydrated && clientAPI) {
            clientAPI.interaction.clearSelection()
          }
        },
        startEdit: (messageId: string) => {
          if (isHydrated && clientAPI) {
            clientAPI.interaction.startEdit(messageId)
          }
        },
        endEdit: () => {
          if (isHydrated && clientAPI) {
            clientAPI.interaction.endEdit()
          }
        }
      },
      
      interface: {
        theme: 'auto',
        density: 'comfortable',
        sidebarExpanded: false,
        isMobile: false,
        setTheme: (theme) => {
          if (isHydrated && clientAPI) {
            clientAPI.interface.setTheme(theme)
          }
        },
        setDensity: (density) => {
          if (isHydrated && clientAPI) {
            clientAPI.interface.setDensity(density)
          }
        },
        toggleSidebar: () => {
          if (isHydrated && clientAPI) {
            clientAPI.interface.toggleSidebar()
          }
        }
      },
      
      // 扩展系统
      registry: {
        registerProcessor: () => {
          console.warn('💡 注册功能需要等待hydration完成')
        },
        registerRenderer: () => {
          console.warn('💡 注册功能需要等待hydration完成')
        },
        getRegisteredTypes: () => ({ processors: [], renderers: [] }),
        getDiagnostics: () => ({ 
          totalProcessors: 0, 
          totalRenderers: 0, 
          registeredTypes: { processors: [], renderers: [] }
        })
      } as any,
      
      registerMessageType: () => {
        console.warn('💡 消息类型注册需要等待hydration完成')
      },
      
      getSupportedTypes: () => [],
      
      // 工具方法
      notify: {
        success: (message: string) => console.log('✅', message),
        error: (message: string) => console.error('❌', message),
        info: (message: string) => console.info('ℹ️', message),
        warning: (message: string) => console.warn('⚠️', message)
      },
      
      getDiagnostics: () => ({
        messages: {
          total: ssrSafeState.messages.length,
          pending: 0,
          sessions: ssrSafeState.currentSession ? 1 : 0
        },
        connection: {
          status: ssrSafeState.connectionStatus,
          type: 'ssr',
          uptime: 0
        },
        registry: {
          processors: 0,
          renderers: 0,
          types: []
        },
        performance: {
          memoryUsage: '0 KB',
          renderTime: 0
        }
      })
    }
  }, [isHydrated, clientAPI, ssrSafeState, initialData])

  // ============================================================================
  // 客户端API桥接
  // ============================================================================
  
  const handleClientAPIReady = useCallback((api: ChatAPI) => {
    setClientAPI(api)
    onClientReady?.(api)
    console.log('✅ 客户端Chat API已就绪')
  }, [onClientReady])

  // ============================================================================
  // 错误边界处理
  // ============================================================================
  
  if (hydrationError && fallbackStrategy === 'error') {
    return (
      <div className="flex items-center justify-center p-8 text-center">
        <div className="max-w-md">
          <h3 className="text-lg font-semibold text-red-600 mb-2">
            聊天系统加载失败
          </h3>
          <p className="text-gray-600 mb-4">
            {hydrationError.message}
          </p>
          <button 
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            重新加载
          </button>
        </div>
      </div>
    )
  }

  // ============================================================================
  // 渲染逻辑
  // ============================================================================
  
  return (
    <SSRChatContext.Provider value={ssrAPI}>
      {!enableSSR || isHydrated ? (
        // 客户端渲染或hydration完成后使用完整功能
        <ClientChatProviderWrapper 
          {...chatProviderProps}
          onReady={handleClientAPIReady}
        >
          {children}
        </ClientChatProviderWrapper>
      ) : (
        // SSR期间使用简化版本
        <div className="chat-ssr-container">
          {fallbackStrategy === 'skeleton' ? (
            <SSRChatSkeleton />
          ) : fallbackStrategy === 'empty' ? (
            <div className="chat-empty-state">
              <p>正在加载聊天系统...</p>
            </div>
          ) : (
            children
          )}
        </div>
      )}
    </SSRChatContext.Provider>
  )
}

// ============================================================================
// SSR Skeleton 组件
// ============================================================================

const SSRChatSkeleton: React.FC = () => {
  return (
    <div className="chat-skeleton animate-pulse">
      <div className="flex flex-col h-full">
        {/* 消息区域骨架屏 */}
        <div className="flex-1 p-4 space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex space-x-3">
              <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
              <div className="flex-1 space-y-2">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          ))}
        </div>
        
        {/* 输入框骨架屏 */}
        <div className="p-4 border-t">
          <div className="flex space-x-2">
            <div className="flex-1 h-10 bg-gray-200 rounded"></div>
            <div className="w-20 h-10 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    </div>
  )
}

// ============================================================================
// Hook导出
// ============================================================================

/**
 * 使用SSR安全的Chat API
 */
export function useSSRChat(): SSRChatAPI {
  const context = useContext(SSRChatContext)
  
  if (!context) {
    throw new Error('useSSRChat must be used within a SSRChatProvider')
  }
  
  return context
}

/**
 * 检查是否已完成hydration
 */
export function useIsHydrated(): boolean {
  const { isHydrated } = useSSRChat()
  return isHydrated
}

/**
 * 轻量级Hook - 仅获取SSR安全的消息
 */
export function useSSRChatMessages() {
  const { messages, isHydrated, initialData } = useSSRChat()
  return { 
    messages, 
    isHydrated, 
    initialMessages: initialData?.messages || [] 
  }
}

// ============================================================================
// 高阶组件
// ============================================================================

/**
 * withSSRChat HOC - 为组件注入SSR兼容的chat功能
 */
export function withSSRChat<P extends object>(
  Component: React.ComponentType<P & { chat: SSRChatAPI }>
) {
  return function WrappedComponent(props: P) {
    const chat = useSSRChat()
    return <Component {...props} chat={chat} />
  }
}

/**
 * 仅在hydration完成后渲染的组件
 */
export function HydratedOnly({ 
  children, 
  fallback = null 
}: { 
  children: React.ReactNode
  fallback?: React.ReactNode 
}) {
  const isHydrated = useIsHydrated()
  
  return <>{isHydrated ? children : fallback}</>
}

export default SSRChatProvider