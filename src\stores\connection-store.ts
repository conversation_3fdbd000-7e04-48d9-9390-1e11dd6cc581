/**
 * 连接状态管理 Store
 *
 * 功能：WebSocket连接状态管理、连接生命周期控制
 * 依赖：connection-factory、ConnectionManager接口
 * 性能：轻量级状态管理，无复杂计算逻辑
 *
 * 数据流：WebSocket Events -> ConnectionManager -> Store -> UI Components
 */

import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { subscribeWithSelector } from 'zustand/middleware'

import {
  createConnection,
  createConnectionByType,
  canUseSocketIO,
} from '@/lib/connection/connection-factory'
// 🎯 新增：环境配置管理
import { environmentConfig, useEnvironmentConfig } from '@/lib/config/environment-config'
// 🛡️ 新增：环境安全保护
import { ensureNotProduction } from '@/lib/utils/environment-security'
import { ConnectionType } from '@/lib/connection/types'
import {
  ConnectionStatus,
  type IConnectionManager,
  type ConnectionConfig,
} from '@/lib/connection/types'

// ============================================================================
// Connection State Interface
// ============================================================================

export interface ConnectionState {
  // 连接状态
  connectionStatus: ConnectionStatus
  isConnecting: boolean
  isConnected: boolean
  lastError: string | undefined

  // 连接实例
  connectionManager: IConnectionManager | undefined

  // 清理函数
  cleanupFunctions: (() => void)[] | undefined
}

// ============================================================================
// Connection Actions Interface
// ============================================================================

export interface ConnectionActions {
  // 连接管理
  initializeConnection: (config?: ConnectionConfig) => Promise<void>
  connect: (config?: ConnectionConfig) => Promise<void>
  disconnect: () => void
  updateConnectionStatus: (status: ConnectionStatus) => void

  // Mock模式管理
  switchToMockMode: (scenarioId?: string) => Promise<void>
  switchToSocketMode: () => Promise<void>
  switchMockScenario: (scenarioId: string) => boolean
  getCurrentMockScenario: () => any
  getAvailableMockScenarios: () => any[]

  // 连接信息获取
  getConnectionType: () => ConnectionType | undefined
  isMockMode: () => boolean
  isSocketMode: () => boolean
  canUseSocketIO: () => boolean

  // 清理
  cleanup: () => void
}

export type ConnectionStore = ConnectionState & ConnectionActions

// ============================================================================
// Connection Store Implementation
// ============================================================================

export const useConnectionStore = create<ConnectionStore>()(
  subscribeWithSelector(
    immer((set, get) => ({
      // ============================================================================
      // 初始状态
      // ============================================================================
      connectionStatus: ConnectionStatus.DISCONNECTED,
      isConnecting: false,
      isConnected: false,
      lastError: undefined,
      connectionManager: undefined,
      cleanupFunctions: undefined,

      // ============================================================================
      // 连接管理方法
      // ============================================================================

      initializeConnection: async (config?: ConnectionConfig) => {
        set(state => {
          console.log('🔌 Initializing connection manager...')

          // 创建连接管理器
          state.connectionManager = createConnection(config)

          // 设置连接状态监听
          const statusUnsubscribe = state.connectionManager.onStatus(status => {
            get().updateConnectionStatus(status)
          })

          // 🔥 关键修复：注册消息处理器
          const { useMessageStore } = require('@/stores/message-store')
          const messageUnsubscribe = state.connectionManager.onMessage(message => {
            console.log('🔗 [ConnectionStore] Forwarding message to MessageStore:', {
              messageId: message.id,
              userId: message.userId,
              payloadType: message.payload?.type,
            })
            useMessageStore.getState().handleIncomingMessage(message)
          })

          // 保存清理函数
          state.cleanupFunctions = [statusUnsubscribe, messageUnsubscribe]
        })

        console.log('✅ Connection Manager 初始化完成 - 消息处理器已注册')
      },

      connect: async (config?: ConnectionConfig) => {
        const state = get()

        // 如果已经连接，直接返回
        if (state.isConnected) {
          console.log('🔌 Already connected')
          return Promise.resolve()
        }

        // 如果正在连接，等待连接完成
        if (state.isConnecting) {
          console.log('🔌 Connection in progress, waiting...')
          return Promise.resolve()
        }

        // 如果没有初始化，先初始化
        if (!state.connectionManager) {
          await get().initializeConnection(config)
        }

        const { connectionManager } = get()
        if (!connectionManager) {
          throw new Error('Connection Manager not initialized')
        }

        set(state => {
          state.isConnecting = true
          state.lastError = undefined
        })

        try {
          console.log('🔌 Attempting to connect...')
          const result = await connectionManager.connect(config)
          if (!result.success) {
            throw new Error(result.error || 'Connection failed')
          }
          console.log('✅ Connection established')
          return Promise.resolve()
        } catch (error) {
          console.error('❌ Connection failed:', error)
          set(state => {
            state.lastError = error instanceof Error ? error.message : 'Connection failed'
            state.isConnecting = false
          })
          throw error
        }
      },

      disconnect: () => {
        console.log('🔌 Disconnecting...')
        const { connectionManager, cleanupFunctions } = get()

        if (connectionManager) {
          connectionManager.disconnect()
        }

        // 清理事件监听器
        if (cleanupFunctions) {
          cleanupFunctions.forEach(cleanup => cleanup())
        }

        set(state => {
          state.isConnecting = false
          state.cleanupFunctions = []
        })

        console.log('✅ Disconnected')
      },

      updateConnectionStatus: (status: ConnectionStatus) => {
        set(state => {
          console.log('📡 Connection status updated:', status)
          state.connectionStatus = status
          state.isConnecting =
            status === ConnectionStatus.CONNECTING || status === ConnectionStatus.RECONNECTING
          state.isConnected = status === ConnectionStatus.CONNECTED
        })
      },

      // ============================================================================
      // Mock模式管理
      // ============================================================================

      switchToMockMode: async (scenarioId?: string) => {
        // 🛡️ 严格的生产环境安全检查
        ensureNotProduction('switchToMockMode', { 
          scenarioId,
          currentConnectionType: get().getConnectionType(),
          timestamp: new Date().toISOString()
        })
        
        console.log('🎭 Switching to Mock mode...')
        const { connectionManager, cleanupFunctions } = get()

        // 先断开当前连接
        if (connectionManager) {
          await connectionManager.disconnect()
        }

        // 清理事件监听器
        if (cleanupFunctions) {
          cleanupFunctions.forEach(cleanup => cleanup())
        }

        // 创建Mock连接
        const mockConnection = createConnectionByType(ConnectionType.MOCK, {
          mockScenario: scenarioId || 'streaming-fast',
          enableScenarioSwitching: true,
        })

        set(state => {
          state.connectionManager = mockConnection
          state.cleanupFunctions = []
        })

        // 重新设置事件监听
        const statusUnsubscribe = mockConnection.onStatus(status => {
          get().updateConnectionStatus(status)
        })

        // 🔥 关键修复：为Mock连接注册消息处理器
        const { useMessageStore } = require('@/stores/message-store')
        const messageUnsubscribe = mockConnection.onMessage(message => {
          console.log('🎭 [ConnectionStore] Mock message forwarding to MessageStore:', {
            messageId: message.id,
            userId: message.userId,
            payloadType: message.payload?.type,
          })
          useMessageStore.getState().handleIncomingMessage(message)
        })

        set(state => {
          state.cleanupFunctions = [statusUnsubscribe, messageUnsubscribe]
        })

        // 连接Mock服务
        await mockConnection.connect()

        console.log('🎭 Switched to Mock mode')
      },

      switchToSocketMode: async () => {
        // 🛡️ 严格的生产环境安全检查
        ensureNotProduction('switchToSocketMode', {
          canUseSocketIO: canUseSocketIO(),
          currentConnectionType: get().getConnectionType(),
          timestamp: new Date().toISOString()
        })
        
        if (!canUseSocketIO()) {
          throw new Error('Socket.IO mode is not available (no URL configured or forced mock mode)')
        }

        console.log('🌐 Switching to Socket.IO mode...')
        const { connectionManager, cleanupFunctions } = get()

        // 先断开当前连接
        if (connectionManager) {
          await connectionManager.disconnect()
        }

        // 清理事件监听器
        if (cleanupFunctions) {
          cleanupFunctions.forEach(cleanup => cleanup())
        }

        // 创建Socket.IO连接
        const socketConnection = createConnectionByType(ConnectionType.SOCKET)

        set(state => {
          state.connectionManager = socketConnection
          state.cleanupFunctions = []
        })

        // 重新设置事件监听
        const statusUnsubscribe = socketConnection.onStatus(status => {
          get().updateConnectionStatus(status)
        })

        // 🔥 关键修复：为Socket连接注册消息处理器
        const { useMessageStore } = require('@/stores/message-store')
        const messageUnsubscribe = socketConnection.onMessage(message => {
          console.log('🌐 [ConnectionStore] Socket message forwarding to MessageStore:', {
            messageId: message.id,
            userId: message.userId,
            payloadType: message.payload?.type,
          })
          useMessageStore.getState().handleIncomingMessage(message)
        })

        set(state => {
          state.cleanupFunctions = [statusUnsubscribe, messageUnsubscribe]
        })

        // 连接Socket.IO服务
        await socketConnection.connect()

        console.log('🌐 Switched to Socket.IO mode')
      },

      switchMockScenario: (scenarioId: string) => {
        // 🛡️ 严格的生产环境安全检查
        ensureNotProduction('switchMockScenario', {
          scenarioId,
          currentConnectionType: get().getConnectionType(),
          timestamp: new Date().toISOString()
        })
        
        const { connectionManager } = get()
        if (
          connectionManager?.getType() === ConnectionType.MOCK &&
          connectionManager.switchScenario
        ) {
          console.log('🎭 Switching mock scenario to:', scenarioId)
          return connectionManager.switchScenario(scenarioId)
        }
        return false
      },

      getCurrentMockScenario: () => {
        const { connectionManager } = get()
        if (
          connectionManager?.getType() === ConnectionType.MOCK &&
          connectionManager.getCurrentScenario
        ) {
          return connectionManager.getCurrentScenario()
        }
        return null
      },

      getAvailableMockScenarios: () => {
        const { connectionManager } = get()
        if (
          connectionManager?.getType() === ConnectionType.MOCK &&
          connectionManager.getAvailableScenarios
        ) {
          return connectionManager.getAvailableScenarios()
        }
        return []
      },

      // ============================================================================
      // 连接信息获取
      // ============================================================================

      getConnectionType: () => {
        const { connectionManager } = get()
        return connectionManager?.getType()
      },

      isMockMode: () => {
        return get().getConnectionType() === ConnectionType.MOCK
      },

      isSocketMode: () => {
        return get().getConnectionType() === ConnectionType.SOCKET
      },

      canUseSocketIO: () => {
        return canUseSocketIO()
      },

      // ============================================================================
      // 清理
      // ============================================================================

      cleanup: () => {
        console.log('🗑️ Cleaning up connection store...')
        const { disconnect } = get()
        disconnect()
        console.log('✅ Connection store cleanup completed')
      },
    }))
  )
)

// ============================================================================
// Connection Hook - 专门的连接管理Hook
// ============================================================================

export const useConnection = () => {
  const connectionStatus = useConnectionStore(state => state.connectionStatus)
  const isConnecting = useConnectionStore(state => state.isConnecting)
  const isConnected = useConnectionStore(state => state.isConnected)
  const lastError = useConnectionStore(state => state.lastError)

  const initializeConnection = useConnectionStore(state => state.initializeConnection)
  const connect = useConnectionStore(state => state.connect)
  const disconnect = useConnectionStore(state => state.disconnect)
  const switchToMockMode = useConnectionStore(state => state.switchToMockMode)
  const switchToSocketMode = useConnectionStore(state => state.switchToSocketMode)
  const switchMockScenario = useConnectionStore(state => state.switchMockScenario)
  const getCurrentMockScenario = useConnectionStore(state => state.getCurrentMockScenario)
  const getAvailableMockScenarios = useConnectionStore(state => state.getAvailableMockScenarios)

  const getConnectionType = useConnectionStore(state => state.getConnectionType)
  const isMockMode = useConnectionStore(state => state.isMockMode)
  const isSocketMode = useConnectionStore(state => state.isSocketMode)
  const canUseSocketIOMode = useConnectionStore(state => state.canUseSocketIO)

  return {
    // 状态
    connectionStatus,
    isConnecting,
    isConnected,
    lastError,

    // 操作
    initializeConnection,
    connect,
    disconnect,
    switchToMockMode,
    switchToSocketMode,
    switchMockScenario,
    getCurrentMockScenario,
    getAvailableMockScenarios,

    // 信息
    connectionType: getConnectionType(),
    isMockMode: isMockMode(),
    isSocketMode: isSocketMode(),
    canUseSocketIO: canUseSocketIOMode(),
  }
}

// 清理函数
export const cleanupConnectionStore = () => {
  const { cleanup } = useConnectionStore.getState()
  cleanup()
}
