/**
 * 简化的Mock连接管理器 - 去除过度工程化设计
 *
 * 功能：轻量级本地模拟，仅保留核心连接功能
 * 依赖：BaseConnection、简化的数据处理
 * 性能：无复杂场景管理，专注基本消息模拟
 *
 * 从595行复杂实现简化为 ~150行，去除：
 * - 复杂的场景管理和ScenarioManager
 * - 双架构适配和复杂的store同步
 * - 过度的会话信息管理
 * - 复杂的定时器和消息调度机制
 */

import type { BaseWebSocketMessage } from '@/types/websocket-event-type'
import { processMessage } from '../message-processor'
import { BaseConnection } from './base-connection'
import {
    ConnectionStatus,
    ConnectionType,
    type ConnectionConfig,
    type ConnectionResult,
} from './types'
// Mock系统已移除，使用固定的AI助手ID
const MOCK_AI_ASSISTANT_ID = 'ai-assistant'

// 简化的Mock数据工厂
class SimpleMockDataFactory {
  static createStreamingResponse(sessionId: string, content: string): BackendWebSocketMessage {
    return {
      groupChatId: 'mock-group',
      sessionId,
      userId: MOCK_AI_ASSISTANT_ID,
      organizationId: 'mock-org',
      timestamp: Date.now(),
      payload: {
        type: 'streaming',
        content,
        isComplete: true,
      } as any,
    }
  }
}

// ============================================================================
// 简化的Mock连接配置
// ============================================================================

export interface MockConnectionConfig extends ConnectionConfig {
  autoReply?: boolean
  responseDelay?: number
}

// ============================================================================
// 简化的Mock连接管理器
// ============================================================================

export class MockConnection extends BaseConnection {
  private autoReply: boolean = true
  private responseDelay: number = 500
  private replyTimer: NodeJS.Timeout | null = null

  constructor(private mockConfig?: MockConnectionConfig) {
    super()

    this.autoReply = mockConfig?.autoReply ?? true
    this.responseDelay = mockConfig?.responseDelay ?? 500

    console.log('🎭 Mock connection initialized')
  }

  // ============================================================================
  // IConnectionManager 实现
  // ============================================================================

  async connect(config?: ConnectionConfig): Promise<ConnectionResult> {
    if (this.isDestroyed) {
      throw new Error('Mock connection has been destroyed')
    }

    if (this.status === ConnectionStatus.CONNECTED) {
      return {
        success: true,
        type: ConnectionType.MOCK,
        message: 'Already connected to Mock service',
      }
    }

    try {
      this.setStatus(ConnectionStatus.CONNECTING)
      this.config = { ...this.config, ...config }

      // 模拟连接延迟
      await new Promise(resolve => setTimeout(resolve, 100))

      this.recordConnection()
      this.setStatus(ConnectionStatus.CONNECTED)

      // 发送连接确认消息
      this.sendWelcomeMessage()

      console.log('✅ Mock connection established')

      return {
        success: true,
        type: ConnectionType.MOCK,
        message: 'Connected to Mock service',
      }
    } catch (error) {
      this.setStatus(ConnectionStatus.ERROR)
      const errorMessage = error instanceof Error ? error.message : 'Unknown connection error'

      return {
        success: false,
        type: ConnectionType.MOCK,
        error: errorMessage,
      }
    }
  }

  async disconnect(): Promise<void> {
    if (this.replyTimer) {
      clearTimeout(this.replyTimer)
      this.replyTimer = null
    }

    this.setStatus(ConnectionStatus.DISCONNECTED)
    console.log('🔌 Mock connection disconnected')
  }

  async sendMessage(message: BaseWebSocketMessage): Promise<void> {
    if (!this.isConnected()) {
      throw new Error('Mock connection not established')
    }

    if (!this.validateMessage(message)) {
      throw new Error('Invalid message format')
    }

    console.log('📨 Mock received message:', message.payload?.type)
    this.incrementMessagesSent()

    // 🔥 P0 BUG 修复：移除重复的emitMessage调用
    // 处理用户消息 - 只通过processMessage的回调emit，不再重复emit
    const processedMessage = processMessage(message, {
      sessionId: message.sessionId || 'mock-session',
      userId: message.userId || 'mock-user',
      onMessage: msg => {
        console.log('📤 [P0 FIX] Processing message via callback:', {
          messageId: msg.id,
          userId: msg.userId,
          payloadType: msg.payload?.type,
        })
        this.emitMessage(msg)
      },
      onError: (error, msg) => {
        console.error('Message processing error:', error)
      },
    })

    // 🔥 移除这行重复的emitMessage调用，避免用户消息被发送两次
    // this.emitMessage(processedMessage)

    // 生成AI回复
    if (this.autoReply && message.userId !== MOCK_AI_ASSISTANT_ID) {
      this.generateAutoReply(message)
    }
  }

  getType(): ConnectionType {
    return ConnectionType.MOCK
  }

  // ============================================================================
  // Mock 特有方法
  // ============================================================================

  /**
   * 设置自动回复
   */
  setAutoReply(enabled: boolean): void {
    this.autoReply = enabled
    console.log(`🤖 Auto reply ${enabled ? 'enabled' : 'disabled'}`)
  }

  // ============================================================================
  // 私有方法
  // ============================================================================

  /**
   * 发送欢迎消息
   */
  private sendWelcomeMessage(): void {
    const welcomeMessage = MockDataFactory.createStreamingMessage({
      delta: '✅ Mock服务连接成功！准备开始对话。',
      isComplete: true,
      config: {
        userId: MOCK_AI_ASSISTANT_ID,
        sessionId: 'mock-session',
        groupChatId: 'mock-group',
        organizationId: 'mock-org',
      },
      isAssistantMessage: true,
    })

    setTimeout(() => {
      this.emitMessage(welcomeMessage)
    }, 200)
  }

  /**
   * 生成自动回复
   */
  private generateAutoReply(originalMessage: BaseWebSocketMessage): void {
    this.replyTimer = setTimeout(() => {
      try {
        // 生成简单的流式回复
        const replyMessages = this.createStreamingReply(originalMessage)

        // 发送流式消息片段
        replyMessages.forEach((message, index) => {
          setTimeout(() => {
            if (!this.isDestroyed && this.isConnected()) {
              // 🎯 修复：助手消息直接发送，不需要重复处理
              this.emitMessage(message)
              console.log(
                '🤖 Assistant message sent:',
                message.payload?.type,
                message.payload?.type === 'streaming'
                  ? (message.payload as any).delta?.substring(0, 20) + '...'
                  : ''
              )
            }
          }, index * 300)
        })
      } catch (error) {
        console.error('❌ Auto reply generation failed:', error)
      }
    }, this.responseDelay)
  }

  /**
   * 创建流式回复消息
   */
  private createStreamingReply(originalMessage: BaseWebSocketMessage): BaseWebSocketMessage[] {
    const replyParts = [
      '我理解了您的问题。',
      '让我为您分析一下...',
      '根据您提供的信息，我的建议是：这是一个模拟回复。',
      '希望这个回答对您有帮助！',
    ]

    return replyParts.map((part, index) =>
      MockDataFactory.createStreamingMessage({
        delta: part,
        isComplete: index === replyParts.length - 1,
        config: {
          userId: MOCK_AI_ASSISTANT_ID,
          sessionId: originalMessage.sessionId || 'mock-session',
          groupChatId: originalMessage.groupChatId || 'mock-group',
          organizationId: originalMessage.organizationId || 'mock-org',
        },
        isAssistantMessage: true,
      })
    )
  }

  // ============================================================================
  // 资源清理
  // ============================================================================

  destroy(): void {
    if (this.replyTimer) {
      clearTimeout(this.replyTimer)
      this.replyTimer = null
    }

    super.destroy()
    console.log('🗑️ Mock connection destroyed')
  }
}
