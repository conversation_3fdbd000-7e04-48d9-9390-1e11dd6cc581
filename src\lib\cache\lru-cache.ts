/**
 * LRU缓存系统 - 内存管理优化
 * 
 * 🎯 设计目标：
 * 1. 高性能的LRU算法实现
 * 2. 支持过期时间和TTL
 * 3. 内存使用监控和统计
 * 4. 可配置的缓存策略
 * 5. 线程安全和错误处理
 * 
 * ⚡ 核心特性：
 * - O(1)时间复杂度的get/set操作
 * - 自动过期和垃圾回收
 * - 内存使用限制和监控
 * - 缓存命中率统计
 * - 支持序列化和持久化
 */

/**
 * 缓存项接口
 */
export interface CacheItem<T = any> {
  key: string
  value: T
  timestamp: number
  lastAccessed: number
  expiresAt?: number
  size?: number
}

/**
 * LRU节点
 */
class LRUNode<T = any> {
  constructor(
    public key: string,
    public item: CacheItem<T>,
    public prev: LRUNode<T> | null = null,
    public next: LRUNode<T> | null = null
  ) {}
}

/**
 * 缓存配置选项
 */
export interface LRUCacheOptions {
  /** 最大缓存条目数 */
  maxSize: number
  /** 默认TTL（毫秒） */
  defaultTTL?: number
  /** 最大内存使用量（字节） */
  maxMemoryUsage?: number
  /** 是否启用统计 */
  enableStats?: boolean
  /** 垃圾回收间隔（毫秒） */
  gcInterval?: number
  /** 是否启用自动清理 */
  autoCleanup?: boolean
  /** 清理阈值（内存使用百分比） */
  cleanupThreshold?: number
}

/**
 * 缓存统计信息
 */
export interface CacheStats {
  /** 总请求数 */
  totalRequests: number
  /** 缓存命中数 */
  hits: number
  /** 缓存未命中数 */
  misses: number
  /** 命中率 */
  hitRate: number
  /** 当前条目数 */
  size: number
  /** 最大条目数 */
  maxSize: number
  /** 当前内存使用量（字节） */
  memoryUsage: number
  /** 最大内存使用量（字节） */
  maxMemoryUsage?: number
  /** 过期清理次数 */
  evictions: number
  /** 手动清理次数 */
  manualClears: number
  /** 最后访问时间 */
  lastAccess: number
  /** 创建时间 */
  createdAt: number
}

/**
 * 高性能LRU缓存实现
 */
export class LRUCache<T = any> {
  private cache = new Map<string, LRUNode<T>>()
  private head: LRUNode<T> | null = null
  private tail: LRUNode<T> | null = null
  private options: Required<LRUCacheOptions>
  private stats: CacheStats
  private gcTimer: NodeJS.Timeout | null = null
  private currentMemoryUsage = 0

  constructor(options: LRUCacheOptions) {
    this.options = {
      maxSize: options.maxSize,
      defaultTTL: options.defaultTTL || 0, // 0表示不过期
      maxMemoryUsage: options.maxMemoryUsage || Infinity,
      enableStats: options.enableStats !== false,
      gcInterval: options.gcInterval || 60000, // 60秒
      autoCleanup: options.autoCleanup !== false,
      cleanupThreshold: options.cleanupThreshold || 0.8 // 80%
    }

    this.stats = {
      totalRequests: 0,
      hits: 0,
      misses: 0,
      hitRate: 0,
      size: 0,
      maxSize: this.options.maxSize,
      memoryUsage: 0,
      maxMemoryUsage: this.options.maxMemoryUsage,
      evictions: 0,
      manualClears: 0,
      lastAccess: Date.now(),
      createdAt: Date.now()
    }

    // 启动垃圾回收定时器
    if (this.options.autoCleanup) {
      this.startGarbageCollection()
    }
  }

  /**
   * 获取缓存值
   */
  get(key: string): T | undefined {
    if (this.options.enableStats) {
      this.stats.totalRequests++
      this.stats.lastAccess = Date.now()
    }

    const node = this.cache.get(key)
    if (!node) {
      if (this.options.enableStats) {
        this.stats.misses++
        this.updateHitRate()
      }
      return undefined
    }

    // 检查是否过期
    if (this.isExpired(node.item)) {
      this.deleteInternal(key, node)
      if (this.options.enableStats) {
        this.stats.misses++
        this.updateHitRate()
      }
      return undefined
    }

    // 更新访问时间并移到头部
    node.item.lastAccessed = Date.now()
    this.moveToHead(node)

    if (this.options.enableStats) {
      this.stats.hits++
      this.updateHitRate()
    }

    return node.item.value
  }

  /**
   * 设置缓存值
   */
  set(key: string, value: T, ttl?: number): boolean {
    try {
      const now = Date.now()
      const expiresAt = ttl || this.options.defaultTTL 
        ? now + (ttl || this.options.defaultTTL) 
        : undefined

      const itemSize = this.calculateSize(value)
      
      // 检查内存限制
      if (this.options.maxMemoryUsage !== Infinity) {
        const projectedMemory = this.currentMemoryUsage + itemSize
        if (projectedMemory > this.options.maxMemoryUsage) {
          // 尝试清理过期项
          this.cleanupExpired()
          
          // 仍然超出限制，拒绝添加
          if (this.currentMemoryUsage + itemSize > this.options.maxMemoryUsage) {
            return false
          }
        }
      }

      const existingNode = this.cache.get(key)
      
      if (existingNode) {
        // 更新现有项目
        const oldSize = existingNode.item.size || 0
        existingNode.item.value = value
        existingNode.item.timestamp = now
        existingNode.item.lastAccessed = now
        existingNode.item.expiresAt = expiresAt
        existingNode.item.size = itemSize
        
        this.currentMemoryUsage = this.currentMemoryUsage - oldSize + itemSize
        this.moveToHead(existingNode)
      } else {
        // 添加新项目
        const item: CacheItem<T> = {
          key,
          value,
          timestamp: now,
          lastAccessed: now,
          expiresAt,
          size: itemSize
        }

        const newNode = new LRUNode(key, item)
        
        // 检查是否需要驱逐
        if (this.cache.size >= this.options.maxSize) {
          this.removeTail()
        }

        this.cache.set(key, newNode)
        this.addToHead(newNode)
        this.currentMemoryUsage += itemSize
        
        if (this.options.enableStats) {
          this.stats.size = this.cache.size
          this.stats.memoryUsage = this.currentMemoryUsage
        }
      }

      return true
    } catch (error) {
      console.error('LRU缓存设置失败:', error)
      return false
    }
  }

  /**
   * 删除缓存项
   */
  delete(key: string): boolean {
    const node = this.cache.get(key)
    if (!node) {
      return false
    }

    return this.deleteInternal(key, node)
  }

  /**
   * 检查是否存在
   */
  has(key: string): boolean {
    const node = this.cache.get(key)
    if (!node) {
      return false
    }

    if (this.isExpired(node.item)) {
      this.deleteInternal(key, node)
      return false
    }

    return true
  }

  /**
   * 清空缓存
   */
  clear(): void {
    this.cache.clear()
    this.head = null
    this.tail = null
    this.currentMemoryUsage = 0

    if (this.options.enableStats) {
      this.stats.size = 0
      this.stats.memoryUsage = 0
      this.stats.manualClears++
    }
  }

  /**
   * 获取所有键
   */
  keys(): string[] {
    const keys: string[] = []
    let current = this.head

    while (current) {
      if (!this.isExpired(current.item)) {
        keys.push(current.key)
      }
      current = current.next
    }

    return keys
  }

  /**
   * 获取所有值
   */
  values(): T[] {
    const values: T[] = []
    let current = this.head

    while (current) {
      if (!this.isExpired(current.item)) {
        values.push(current.item.value)
      }
      current = current.next
    }

    return values
  }

  /**
   * 获取所有条目
   */
  entries(): Array<[string, T]> {
    const entries: Array<[string, T]> = []
    let current = this.head

    while (current) {
      if (!this.isExpired(current.item)) {
        entries.push([current.key, current.item.value])
      }
      current = current.next
    }

    return entries
  }

  /**
   * 获取缓存大小
   */
  size(): number {
    return this.cache.size
  }

  /**
   * 获取统计信息
   */
  getStats(): CacheStats {
    if (!this.options.enableStats) {
      throw new Error('统计功能未启用')
    }

    return { ...this.stats }
  }

  /**
   * 重置统计信息
   */
  resetStats(): void {
    if (this.options.enableStats) {
      this.stats = {
        ...this.stats,
        totalRequests: 0,
        hits: 0,
        misses: 0,
        hitRate: 0,
        evictions: 0,
        manualClears: 0,
        lastAccess: Date.now()
      }
    }
  }

  /**
   * 手动垃圾回收
   */
  gc(): number {
    return this.cleanupExpired()
  }

  /**
   * 获取内存使用情况
   */
  getMemoryUsage(): {
    current: number
    max: number
    percentage: number
  } {
    const max = this.options.maxMemoryUsage
    return {
      current: this.currentMemoryUsage,
      max: max === Infinity ? 0 : max,
      percentage: max === Infinity ? 0 : (this.currentMemoryUsage / max) * 100
    }
  }

  /**
   * 设置新的配置
   */
  updateOptions(newOptions: Partial<LRUCacheOptions>): void {
    this.options = { ...this.options, ...newOptions }

    // 如果启用了自动清理但定时器未运行，启动定时器
    if (this.options.autoCleanup && !this.gcTimer) {
      this.startGarbageCollection()
    } else if (!this.options.autoCleanup && this.gcTimer) {
      this.stopGarbageCollection()
    }

    // 如果减小了最大大小，需要立即清理
    if (newOptions.maxSize && newOptions.maxSize < this.cache.size) {
      this.enforceMaxSize()
    }
  }

  /**
   * 销毁缓存
   */
  destroy(): void {
    this.stopGarbageCollection()
    this.clear()
  }

  // ============================================================================
  // 私有方法
  // ============================================================================

  private deleteInternal(key: string, node: LRUNode<T>): boolean {
    this.cache.delete(key)
    this.removeNode(node)
    this.currentMemoryUsage -= node.item.size || 0

    if (this.options.enableStats) {
      this.stats.size = this.cache.size
      this.stats.memoryUsage = this.currentMemoryUsage
    }

    return true
  }

  private isExpired(item: CacheItem<T>): boolean {
    if (!item.expiresAt) {
      return false
    }
    return Date.now() > item.expiresAt
  }

  private moveToHead(node: LRUNode<T>): void {
    this.removeNode(node)
    this.addToHead(node)
  }

  private addToHead(node: LRUNode<T>): void {
    node.prev = null
    node.next = this.head

    if (this.head) {
      this.head.prev = node
    }

    this.head = node

    if (!this.tail) {
      this.tail = node
    }
  }

  private removeNode(node: LRUNode<T>): void {
    if (node.prev) {
      node.prev.next = node.next
    } else {
      this.head = node.next
    }

    if (node.next) {
      node.next.prev = node.prev
    } else {
      this.tail = node.prev
    }
  }

  private removeTail(): void {
    if (!this.tail) {
      return
    }

    const key = this.tail.key
    this.deleteInternal(key, this.tail)

    if (this.options.enableStats) {
      this.stats.evictions++
    }
  }

  private calculateSize(value: T): number {
    try {
      // 简单的大小估算
      if (typeof value === 'string') {
        return value.length * 2 // UTF-16字符
      } else if (typeof value === 'number' || typeof value === 'boolean') {
        return 8
      } else if (value === null || value === undefined) {
        return 0
      } else {
        // 对象类型，使用JSON字符串长度估算
        return JSON.stringify(value).length * 2
      }
    } catch {
      // 如无法序列化，返回默认大小
      return 1024
    }
  }

  private updateHitRate(): void {
    if (this.stats.totalRequests > 0) {
      this.stats.hitRate = this.stats.hits / this.stats.totalRequests
    }
  }

  private cleanupExpired(): number {
    let cleanedCount = 0
    const expiredKeys: string[] = []

    // 收集过期的键
    for (const [key, node] of this.cache.entries()) {
      if (this.isExpired(node.item)) {
        expiredKeys.push(key)
      }
    }

    // 删除过期的项
    for (const key of expiredKeys) {
      const node = this.cache.get(key)
      if (node) {
        this.deleteInternal(key, node)
        cleanedCount++
      }
    }

    if (cleanedCount > 0 && this.options.enableStats) {
      this.stats.evictions += cleanedCount
    }

    return cleanedCount
  }

  private enforceMaxSize(): void {
    while (this.cache.size > this.options.maxSize) {
      this.removeTail()
    }
  }

  private startGarbageCollection(): void {
    if (this.gcTimer) {
      return
    }

    this.gcTimer = setInterval(() => {
      try {
        this.cleanupExpired()

        // 检查内存使用情况
        if (this.options.maxMemoryUsage !== Infinity) {
          const memoryUsage = this.getMemoryUsage()
          if (memoryUsage.percentage > this.options.cleanupThreshold * 100) {
            // 内存使用过高，清理一些最旧的项
            const itemsToRemove = Math.floor(this.cache.size * 0.1) // 清理10%
            for (let i = 0; i < itemsToRemove && this.tail; i++) {
              this.removeTail()
            }
          }
        }
      } catch (error) {
        console.error('垃圾回收过程中出错:', error)
      }
    }, this.options.gcInterval)
  }

  private stopGarbageCollection(): void {
    if (this.gcTimer) {
      clearInterval(this.gcTimer)
      this.gcTimer = null
    }
  }
}

/**
 * 缓存管理器工厂
 */
export class CacheManagerFactory {
  private static instances = new Map<string, LRUCache<any>>()

  /**
   * 创建或获取缓存实例
   */
  static createCache<T = any>(
    name: string, 
    options: LRUCacheOptions
  ): LRUCache<T> {
    if (this.instances.has(name)) {
      return this.instances.get(name)!
    }

    const cache = new LRUCache<T>(options)
    this.instances.set(name, cache)
    return cache
  }

  /**
   * 获取缓存实例
   */
  static getCache<T = any>(name: string): LRUCache<T> | undefined {
    return this.instances.get(name)
  }

  /**
   * 删除缓存实例
   */
  static deleteCache(name: string): boolean {
    const cache = this.instances.get(name)
    if (cache) {
      cache.destroy()
      return this.instances.delete(name)
    }
    return false
  }

  /**
   * 获取所有缓存实例名称
   */
  static getCacheNames(): string[] {
    return Array.from(this.instances.keys())
  }

  /**
   * 清理所有缓存
   */
  static clearAll(): void {
    for (const cache of this.instances.values()) {
      cache.clear()
    }
  }

  /**
   * 销毁所有缓存
   */
  static destroyAll(): void {
    for (const cache of this.instances.values()) {
      cache.destroy()
    }
    this.instances.clear()
  }

  /**
   * 获取全局统计信息
   */
  static getGlobalStats(): Record<string, CacheStats> {
    const stats: Record<string, CacheStats> = {}
    
    for (const [name, cache] of this.instances.entries()) {
      try {
        stats[name] = cache.getStats()
      } catch {
        // 统计功能未启用，跳过
      }
    }

    return stats
  }
}