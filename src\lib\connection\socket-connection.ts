/**
 * Socket.IO连接管理器实现
 * 连接到Python FastAPI后端的具体实现
 */

import type { Socket } from 'socket.io-client'
import { BaseConnection } from './base-connection'
import { createSocketClient, type SocketConfig } from '../socket'
import {
  ConnectionType,
  ConnectionStatus,
  type ConnectionConfig,
  type ConnectionResult,
} from './types'
import type { BaseWebSocketMessage } from '@/types/websocket-event-type'

export class SocketConnection extends BaseConnection {
  private socket: Socket | null = null
  private connectionTimeout: NodeJS.Timeout | null = null

  constructor(private socketConfig?: SocketConfig) {
    super()
  }

  // ============================================================================
  // IConnectionManager 实现
  // ============================================================================

  async connect(config?: ConnectionConfig): Promise<ConnectionResult> {
    if (this.isDestroyed) {
      throw new Error('Connection manager has been destroyed')
    }

    if (this.socket?.connected) {
      return {
        success: true,
        type: ConnectionType.SOCKET,
        message: 'Already connected',
      }
    }

    try {
      this.setStatus(ConnectionStatus.CONNECTING)
      this.config = { ...this.config, ...config }

      // 创建Socket.IO客户端
      const socketConfig = this.getSocketConfig()
      this.socket = createSocketClient(socketConfig)

      // 设置事件监听器
      this.setupSocketEvents()

      // 连接到服务器
      await this.performConnection()

      this.recordConnection()
      this.setStatus(ConnectionStatus.CONNECTED)

      return {
        success: true,
        type: ConnectionType.SOCKET,
        message: `Connected to ${socketConfig.url}`,
      }
    } catch (error) {
      this.setStatus(ConnectionStatus.ERROR)
      const errorMessage = error instanceof Error ? error.message : 'Unknown connection error'

      return {
        success: false,
        type: ConnectionType.SOCKET,
        error: errorMessage,
      }
    }
  }

  async disconnect(): Promise<void> {
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout)
      this.connectionTimeout = null
    }

    if (this.socket) {
      this.socket.removeAllListeners()
      this.socket.disconnect()
      this.socket = null
    }

    this.setStatus(ConnectionStatus.DISCONNECTED)
  }

  async sendMessage(message: BaseWebSocketMessage): Promise<void> {
    if (!this.socket?.connected) {
      throw new Error('Socket not connected')
    }

    if (!this.validateMessage(message)) {
      throw new Error('Invalid message format')
    }

    return new Promise((resolve, reject) => {
      this.socket!.emit('user-message', message, (response: any) => {
        if (response?.error) {
          reject(new Error(response.error))
        } else {
          this.incrementMessagesSent()
          resolve()
        }
      })
    })
  }

  getType(): ConnectionType {
    return ConnectionType.SOCKET
  }

  // ============================================================================
  // 私有方法
  // ============================================================================

  private getSocketConfig(): SocketConfig {
    if (this.socketConfig) {
      return this.socketConfig
    }

    const url = this.config.url || process.env.NEXT_PUBLIC_SOCKET_URL
    if (!url) {
      throw new Error('Socket URL not configured')
    }

    const socketConfig: SocketConfig = {
      url,
      timeout: this.config.timeout || 20000,
      reconnection: this.config.reconnection ?? true,
      reconnectionAttempts: this.config.reconnectionAttempts || 5,
      reconnectionDelay: this.config.reconnectionDelay || 1000,
    }

    if (this.config.auth) {
      socketConfig.auth = this.config.auth
    }

    return socketConfig
  }

  private setupSocketEvents(): void {
    if (!this.socket) return

    // 连接事件
    this.socket.on('connect', () => {
      console.log('✅ Socket.IO connected successfully')
      this.setStatus(ConnectionStatus.CONNECTED)
    })

    this.socket.on('connect_error', error => {
      console.error('❌ Socket.IO connection error:', error)
      this.setStatus(ConnectionStatus.ERROR)
      this.emitError(new Error(`Connection failed: ${error.message}`))
    })

    this.socket.on('disconnect', reason => {
      console.warn('🔌 Socket.IO disconnected:', reason)
      this.setStatus(ConnectionStatus.DISCONNECTED)

      if (reason === 'io server disconnect') {
        // 服务器主动断开，尝试重连
        this.attemptReconnection()
      }
    })

    // 重连事件
    this.socket.on('reconnect_attempt', () => {
      console.log('🔄 Attempting to reconnect...')
      this.setStatus(ConnectionStatus.RECONNECTING)
    })

    this.socket.on('reconnect', attemptNumber => {
      console.log('✅ Reconnected after', attemptNumber, 'attempts')
      this.recordReconnect()
      this.setStatus(ConnectionStatus.CONNECTED)
    })

    this.socket.on('reconnect_failed', () => {
      console.error('❌ Reconnection failed')
      this.setStatus(ConnectionStatus.ERROR)
      this.emitError(new Error('Reconnection failed after maximum attempts'))
    })

    // 业务消息事件 - 根据Python后端的事件类型设置
    this.setupBusinessEvents()
  }

  private setupBusinessEvents(): void {
    if (!this.socket) return

    // 连接确认
    this.socket.on('connection-confirmed', (data: BaseWebSocketMessage) => {
      console.log('📨 Connection confirmed:', data)
      this.emitMessage(data)
    })

    // 流式消息
    this.socket.on('streaming-message', (data: BaseWebSocketMessage) => {
      this.emitMessage(data)
    })

    // 表单消息
    this.socket.on('checkpoint-message', (data: BaseWebSocketMessage) => {
      this.emitMessage(data)
    })

    // 报告消息
    this.socket.on('report-message', (data: BaseWebSocketMessage) => {
      this.emitMessage(data)
    })

    // 错误消息
    this.socket.on('error-message', (data: BaseWebSocketMessage) => {
      this.emitMessage(data)
    })

    // 通用消息处理
    this.socket.on('message', (data: BaseWebSocketMessage) => {
      this.emitMessage(data)
    })

    // 错误处理
    this.socket.on('error', (error: any) => {
      console.error('❌ Socket error:', error)
      this.emitError(new Error(`Socket error: ${error}`))
    })
  }

  private performConnection(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.socket) {
        reject(new Error('Socket not initialized'))
        return
      }

      // 设置连接超时
      this.connectionTimeout = setTimeout(() => {
        reject(new Error('Connection timeout'))
      }, this.config.timeout || 20000)

      // 监听连接成功
      const onConnect = () => {
        if (this.connectionTimeout) {
          clearTimeout(this.connectionTimeout)
          this.connectionTimeout = null
        }
        this.socket!.off('connect', onConnect)
        this.socket!.off('connect_error', onError)
        resolve()
      }

      // 监听连接失败
      const onError = (error: Error) => {
        if (this.connectionTimeout) {
          clearTimeout(this.connectionTimeout)
          this.connectionTimeout = null
        }
        this.socket!.off('connect', onConnect)
        this.socket!.off('connect_error', onError)
        reject(error)
      }

      this.socket.on('connect', onConnect)
      this.socket.on('connect_error', onError)

      // 开始连接
      this.socket.connect()
    })
  }

  private attemptReconnection(): void {
    if (this.socket && this.config.reconnection !== false) {
      console.log('🔄 Attempting manual reconnection...')
      this.socket.connect()
    }
  }

  // ============================================================================
  // 资源清理
  // ============================================================================

  destroy(): void {
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout)
      this.connectionTimeout = null
    }

    super.destroy()
  }
}
