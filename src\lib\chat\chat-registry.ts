/**
 * Chat Registry - 消息类型的统一注册和管理中心
 * 
 * 🎯 设计目标：
 * 1. 提供消息处理器和渲染器的注册机制
 * 2. 支持类型安全的扩展开发
 * 3. 实现热插拔和组件替换
 * 4. 简化新功能开发流程（5分钟添加新消息类型）
 * 
 * 🔄 数据流：
 * Message → Registry.getProcessor() → Process → Registry.getRenderer() → UI
 */

import React from 'react'
import type { BaseWebSocketMessage as WebSocketMessage } from '@/types/websocket-event-type'

// ============================================================================
// 核心类型定义
// ============================================================================

/**
 * 消息处理器接口
 */
export interface MessageProcessor<T = any> {
  /**
   * 处理消息的核心逻辑
   */
  process: (message: WebSocketMessage, context?: MessageProcessContext) => T | Promise<T>
  
  /**
   * 验证消息数据有效性
   */
  validate?: (payload: any) => boolean
  
  /**
   * 处理器优先级 (数字越小优先级越高)
   */
  priority?: number
  
  /**
   * 处理器描述信息
   */
  description?: string
}

/**
 * 消息渲染器接口
 */
export interface MessageRenderer {
  /**
   * React组件
   */
  component: React.ComponentType<MessageRendererProps>
  
  /**
   * 渲染器优先级
   */
  priority?: number
  
  /**
   * 支持的消息状态
   */
  supportedStates?: MessageState[]
  
  /**
   * 渲染器描述
   */
  description?: string
}

/**
 * 消息处理上下文
 */
export interface MessageProcessContext {
  sessionId: string
  userId: string
  timestamp: number
  isStreaming?: boolean
  metadata?: Record<string, any>
}

/**
 * 消息渲染器属性
 */
export interface MessageRendererProps {
  message: WebSocketMessage
  context: MessageProcessContext
  onUpdate?: (messageId: string, updates: Partial<WebSocketMessage>) => void
  onAction?: (action: string, data?: any) => void
}

/**
 * 消息状态枚举
 */
export enum MessageState {
  PENDING = 'pending',
  PROCESSING = 'processing', 
  COMPLETED = 'completed',
  FAILED = 'failed',
  STREAMING = 'streaming'
}

/**
 * 注册表配置
 */
export interface RegistryConfig {
  enableHotReload?: boolean
  enableValidation?: boolean
  enableLogging?: boolean
  defaultRenderer?: string
  defaultProcessor?: string
}

// ============================================================================
// ChatRegistry 核心实现
// ============================================================================

export class ChatRegistry {
  private processors = new Map<string, MessageProcessor>()
  private renderers = new Map<string, MessageRenderer>()
  private config: RegistryConfig
  private listeners = new Set<(event: RegistryEvent) => void>()

  constructor(config: RegistryConfig = {}) {
    this.config = {
      enableHotReload: false,
      enableValidation: true,
      enableLogging: process.env.NODE_ENV === 'development',
      defaultRenderer: 'text',
      defaultProcessor: 'default',
      ...config
    }

    // 注册默认处理器和渲染器
    this.registerBuiltinComponents()
  }

  // ============================================================================
  // 注册方法
  // ============================================================================

  /**
   * 注册消息处理器
   */
  registerProcessor<T = any>(
    type: string, 
    processor: MessageProcessor<T>
  ): this {
    if (this.config.enableValidation && this.processors.has(type)) {
      console.warn(`🔄 Chat Registry: 覆盖已存在的处理器 '${type}'`)
    }

    this.processors.set(type, processor)
    
    if (this.config.enableLogging) {
      console.log(`✅ Chat Registry: 注册处理器 '${type}'`, processor)
    }

    this.emit('processor-registered', { type, processor })
    return this
  }

  /**
   * 注册消息渲染器
   */
  registerRenderer(
    type: string, 
    renderer: MessageRenderer
  ): this {
    if (this.config.enableValidation && this.renderers.has(type)) {
      console.warn(`🔄 Chat Registry: 覆盖已存在的渲染器 '${type}'`)
    }

    this.renderers.set(type, renderer)
    
    if (this.config.enableLogging) {
      console.log(`✅ Chat Registry: 注册渲染器 '${type}'`, renderer)
    }

    this.emit('renderer-registered', { type, renderer })
    return this
  }

  /**
   * 批量注册组件
   */
  registerComponents(components: {
    processors?: Record<string, MessageProcessor>
    renderers?: Record<string, MessageRenderer>
  }): this {
    if (components.processors) {
      Object.entries(components.processors).forEach(([type, processor]) => {
        this.registerProcessor(type, processor)
      })
    }

    if (components.renderers) {
      Object.entries(components.renderers).forEach(([type, renderer]) => {
        this.registerRenderer(type, renderer)
      })
    }

    return this
  }

  // ============================================================================
  // 获取方法
  // ============================================================================

  /**
   * 获取消息处理器
   */
  getProcessor(type: string): MessageProcessor | undefined {
    const processor = this.processors.get(type)
    
    if (!processor && this.config.defaultProcessor) {
      return this.processors.get(this.config.defaultProcessor)
    }
    
    return processor
  }

  /**
   * 获取消息渲染器
   */
  getRenderer(type: string): MessageRenderer | undefined {
    const renderer = this.renderers.get(type)
    
    if (!renderer && this.config.defaultRenderer) {
      return this.renderers.get(this.config.defaultRenderer)
    }
    
    return renderer
  }

  /**
   * 获取所有已注册的类型
   */
  getRegisteredTypes(): {
    processors: string[]
    renderers: string[]
  } {
    return {
      processors: Array.from(this.processors.keys()),
      renderers: Array.from(this.renderers.keys())
    }
  }

  /**
   * 检查类型是否已注册
   */
  hasType(type: string): {
    hasProcessor: boolean
    hasRenderer: boolean
  } {
    return {
      hasProcessor: this.processors.has(type),
      hasRenderer: this.renderers.has(type)
    }
  }

  // ============================================================================
  // 处理和渲染方法
  // ============================================================================

  /**
   * 处理消息
   */
  async processMessage(
    message: WebSocketMessage, 
    context?: MessageProcessContext
  ): Promise<any> {
    const processor = this.getProcessor(message.payload?.type || 'default')
    
    if (!processor) {
      throw new Error(`未找到消息类型 '${message.payload?.type}' 的处理器`)
    }

    // 验证消息
    if (processor.validate && !processor.validate(message.payload)) {
      throw new Error(`消息验证失败: ${message.payload?.type}`)
    }

    try {
      const result = await processor.process(message, context)
      
      if (this.config.enableLogging) {
        console.log(`🔄 消息处理完成:`, { type: message.payload?.type, result })
      }
      
      return result
    } catch (error) {
      console.error(`❌ 消息处理失败:`, error)
      throw error
    }
  }

  /**
   * 渲染消息组件
   */
  renderMessage(
    message: WebSocketMessage,
    context: MessageProcessContext,
    props?: Partial<MessageRendererProps>
  ): React.ReactElement | null {
    const renderer = this.getRenderer(message.payload?.type || 'default')
    
    if (!renderer) {
      console.warn(`未找到消息类型 '${message.payload?.type}' 的渲染器`)
      return null
    }

    const Component = renderer.component
    const renderProps: MessageRendererProps = {
      message,
      context,
      ...props
    }

    return React.createElement(Component, renderProps)
  }

  // ============================================================================
  // 事件系统
  // ============================================================================

  /**
   * 监听注册表事件
   */
  on(listener: (event: RegistryEvent) => void): () => void {
    this.listeners.add(listener)
    
    // 返回取消监听函数
    return () => {
      this.listeners.delete(listener)
    }
  }

  /**
   * 发出事件
   */
  private emit(type: string, data: any): void {
    const event: RegistryEvent = { type, data, timestamp: Date.now() }
    this.listeners.forEach(listener => {
      try {
        listener(event)
      } catch (error) {
        console.error('Registry事件监听器错误:', error)
      }
    })
  }

  // ============================================================================
  // 内置组件注册
  // ============================================================================

  private registerBuiltinComponents(): void {
    // 默认文本处理器
    this.registerProcessor('default', {
      process: (message) => message,
      validate: () => true,
      description: '默认消息处理器'
    })

    // 默认文本渲染器
    this.registerRenderer('text', {
      component: ({ message }: MessageRendererProps) => {
        const textContent = 'content' in message.payload ? message.payload.content : ''
        return React.createElement('div', { className: 'text-message' }, textContent)
      },
      description: '默认文本渲染器'
    })

    // 流式消息处理器
    this.registerProcessor('streaming', {
      process: (message, context) => ({
        ...message,
        isStreaming: context?.isStreaming || false
      }),
      description: '流式消息处理器'
    })
  }

  // ============================================================================
  // 工具方法
  // ============================================================================

  /**
   * 获取注册表诊断信息
   */
  getDiagnostics(): RegistryDiagnostics {
    return {
      totalProcessors: this.processors.size,
      totalRenderers: this.renderers.size,
      registeredTypes: this.getRegisteredTypes(),
      config: this.config,
      timestamp: new Date().toISOString()
    }
  }

  /**
   * 清除所有注册
   */
  clear(): void {
    this.processors.clear()
    this.renderers.clear()
    this.registerBuiltinComponents()
    this.emit('registry-cleared', {})
  }
}

// ============================================================================
// 辅助类型定义
// ============================================================================

export interface RegistryEvent {
  type: string
  data: any
  timestamp: number
}

export interface RegistryDiagnostics {
  totalProcessors: number
  totalRenderers: number
  registeredTypes: {
    processors: string[]
    renderers: string[]
  }
  config: RegistryConfig
  timestamp: string
}

// ============================================================================
// 全局单例实例
// ============================================================================

export const chatRegistry = new ChatRegistry({
  enableHotReload: process.env.NODE_ENV === 'development',
  enableValidation: true,
  enableLogging: process.env.NODE_ENV === 'development'
})

// ============================================================================
// 便捷注册函数
// ============================================================================

/**
 * 注册消息处理器的便捷函数
 */
export function registerMessageProcessor<T = any>(
  type: string, 
  processor: MessageProcessor<T>
): void {
  chatRegistry.registerProcessor(type, processor)
}

/**
 * 注册消息渲染器的便捷函数
 */
export function registerMessageRenderer(
  type: string, 
  renderer: MessageRenderer
): void {
  chatRegistry.registerRenderer(type, renderer)
}

/**
 * 获取注册表实例（用于Hook）
 */
export function useChatRegistry(): ChatRegistry {
  return chatRegistry
}