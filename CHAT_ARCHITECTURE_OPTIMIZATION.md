# Chat系统架构优化开发文档

## 🎯 设计理念

> **核心原则：架构优化 = 内部重构，保持UI完全一致**

本次优化基于以下设计理念：

### 1. **分层架构 (Layered Architecture)**
- **数据层** (MessageDataStore): 纯粹的CRUD操作，专注数据存储与检索
- **业务层** (MessageFlowStore): 消息发送接收流程，WebSocket处理
- **表现层** (MessageUIStore): UI状态管理，用户交互处理
- **接口层** (ChatProvider): 统一API，隐藏底层复杂性

### 2. **插件化扩展 (Plugin Architecture)**
- **注册机制**: 消息类型通过Registry统一注册管理
- **处理器分离**: 业务逻辑与渲染逻辑完全解耦
- **热插拔能力**: 运行时动态注册新的消息类型

### 3. **开发者体验优先 (Developer Experience First)**
- **单一依赖**: 组件只需`useChat()`一个Hook
- **类型安全**: 完整的TypeScript支持和智能提示
- **5分钟目标**: 新消息类型开发控制在5分钟内

---

## 🏗️ 核心架构详解

### ChatRegistry - 消息类型注册中心

**文件位置**: `src/lib/chat/chat-registry.ts`

#### 核心类结构

```typescript
export class ChatRegistry {
  private processors: Map<string, MessageProcessor<any>>
  private renderers: Map<string, MessageRenderer>
  private listeners: EventListener[]
  
  // 注册消息处理器
  registerProcessor<T = any>(type: string, processor: MessageProcessor<T>): this
  
  // 注册消息渲染器
  registerRenderer(type: string, renderer: MessageRenderer): this
  
  // 处理消息
  processMessage(message: WebSocketMessage, context?: MessageProcessContext): Promise<any>
  
  // 获取渲染器
  getRenderer(type: string): MessageRenderer | null
  
  // 获取注册类型列表
  getRegisteredTypes(): { processors: string[], renderers: string[] }
  
  // 诊断信息
  getDiagnostics(): RegistryDiagnostics
}
```

#### 使用方法

**1. 注册消息处理器**

```typescript
import { chatRegistry } from '@/lib/chat/chat-registry'

// 定义处理器
const textProcessor: MessageProcessor<string> = {
  process: async (message: WebSocketMessage) => {
    // 验证消息格式
    if (!message.payload?.content) {
      throw new Error('Text message must have content')
    }
    
    // 处理业务逻辑
    const processedContent = sanitizeContent(message.payload.content)
    
    return {
      ...message,
      payload: {
        ...message.payload,
        content: processedContent
      }
    }
  },
  
  validate: (payload: any) => {
    return payload?.type === 'text' && typeof payload.content === 'string'
  },
  
  description: '文本消息处理器'
}

// 注册处理器
chatRegistry.registerProcessor('text', textProcessor)
```

**2. 注册消息渲染器**

```typescript
// 定义渲染组件
const TextMessageRenderer: React.FC<MessageRendererProps> = ({ 
  message, 
  context, 
  onUpdate, 
  onAction 
}) => {
  const handleEdit = () => {
    onAction?.('edit', { messageId: message.id })
  }
  
  return (
    <div className="message-text">
      <p>{message.payload.content}</p>
      {context.isOwner && (
        <button onClick={handleEdit}>编辑</button>
      )}
    </div>
  )
}

// 注册渲染器
chatRegistry.registerRenderer('text', {
  component: TextMessageRenderer,
  description: '文本消息渲染器'
})
```

**3. Registry事件监听**

```typescript
// 监听注册事件
chatRegistry.addEventListener('processorRegistered', (event) => {
  console.log(`已注册处理器: ${event.type}`)
})

chatRegistry.addEventListener('messageProcessed', (event) => {
  console.log(`消息处理完成: ${event.messageId}`)
})
```

---

### MessageDataStore - 数据层

**文件位置**: `src/stores/message-data-store.ts`

#### 状态结构

```typescript
interface MessageDataState {
  // 按会话ID组织的消息数据
  messages: Record<string, WebSocketMessage[]>
  
  // 消息快速索引 {messageId -> {sessionId, index}}
  messageIndex: Record<string, { sessionId: string; index: number }>
  
  // 会话统计信息
  sessionStats: Record<string, {
    messageCount: number
    lastMessageTime: number
    unreadCount: number
  }>
}
```

#### 核心方法

**1. 消息添加**

```typescript
const { addMessage } = useMessageDataStore()

// 添加单条消息
await addMessage(sessionId, {
  id: 'msg-123',
  sessionId: 'session-456', 
  userId: 'user-789',
  groupChatId: 'group-abc',
  organizationId: 'org-def',
  payload: {
    type: 'text',
    content: 'Hello world'
  },
  timestamp: Date.now()
})
```

**2. 批量操作**

```typescript
const { batchAddMessages } = useMessageDataStore()

// 批量添加消息（性能优化）
await batchAddMessages(sessionId, [
  message1, message2, message3
])
```

**3. 消息查询**

```typescript
const { getMessage, findMessages } = useMessageDataStore()

// 通过ID获取消息
const message = getMessage('msg-123')

// 条件查询
const textMessages = findMessages(msg => 
  msg.payload.type === 'text' && 
  msg.payload.content?.includes('search term')
)
```

**4. 性能优化的Selector Hooks**

```typescript
// 获取会话消息（自动memoization）
const messages = useSessionMessages(sessionId)

// 获取会话统计（性能优化）
const stats = useSessionStats(sessionId)

// 获取未读消息数量
const unreadCount = useUnreadCount(sessionId)
```

---

### MessageFlowStore - 业务流程层

**文件位置**: `src/stores/message-flow-store.ts`

#### 状态结构

```typescript
interface MessageFlowState {
  // 发送中的消息队列
  pendingSentMessages: PendingSentMessage[]
  
  // 流式消息缓存
  streamingMessages: Record<string, StreamingMessage>
  
  // 重试配置
  retryConfig: {
    maxRetries: number
    retryDelay: number
    backoffMultiplier: number
  }
  
  // 处理状态
  isProcessing: boolean
}
```

#### 核心方法

**1. 发送消息**

```typescript
const { sendUserMessage } = useMessageFlowStore()

// 发送用户消息
await sendUserMessage(
  'Hello world',           // 消息内容
  'session-123',          // 会话ID（可选）
  {
    messageType: 'text',   // 消息类型
    priority: 'normal',    // 优先级
    metadata: {            // 附加数据
      source: 'web'
    }
  }
)
```

**2. 消息重试机制**

```typescript
const { retryMessage } = useMessageFlowStore()

// 重试失败的消息
await retryMessage('temp-msg-456')
```

**3. 流式消息处理**

```typescript
const { handleStreamingChunk } = useMessageFlowStore()

// 处理流式消息块
handleStreamingChunk(
  'stream-msg-789',       // 消息ID
  'This is a chunk',      // 消息片段
  false                   // 是否完成
)
```

**4. 消息接收处理**

```typescript
const { handleIncomingMessage } = useMessageFlowStore()

// 处理WebSocket接收的消息
handleIncomingMessage({
  id: 'msg-incoming-123',
  sessionId: 'session-456',
  userId: 'assistant',
  groupChatId: 'group-789',
  organizationId: 'org-abc',
  payload: {
    type: 'streaming',
    delta: 'Streaming response...',
    isComplete: false
  },
  timestamp: Date.now()
})
```

---

### MessageUIStore - 表现层

**文件位置**: `src/stores/message-ui-store.ts`

#### 状态结构

```typescript
interface MessageUIState {
  // 输入框状态
  input: {
    content: string
    isTyping: boolean
    isFocused: boolean
    selectionStart: number
    selectionEnd: number
  }
  
  // 消息交互状态
  interaction: {
    selectedMessageIds: string[]
    editingMessageId: string | null
    editingContent: string
    hoveredMessageId: string | null
    contextMenuMessageId: string | null
  }
  
  // 界面设置
  interface: {
    theme: 'light' | 'dark' | 'auto'
    messagesDensity: 'compact' | 'comfortable' | 'spacious'
    sidebarExpanded: boolean
    isMobile: boolean
  }
  
  // 通知系统
  notifications: Notification[]
}
```

#### 核心方法

**1. 输入管理**

```typescript
const { 
  updateInputContent, 
  clearInput, 
  setInputFocus 
} = useMessageUIStore()

// 更新输入内容
updateInputContent('New message content')

// 清空输入
clearInput()

// 设置焦点状态
setInputFocus(true)
```

**2. 消息交互**

```typescript
const { 
  selectMessage, 
  clearSelection,
  startEditMessage,
  endEditMessage
} = useMessageUIStore()

// 选择消息（支持多选）
selectMessage('msg-123', true) // true表示多选模式

// 清空选择
clearSelection()

// 开始编辑消息
startEditMessage('msg-456', 'Original content')

// 结束编辑
endEditMessage()
```

**3. 界面设置**

```typescript
const { 
  setTheme, 
  setMessagesDensity,
  toggleSidebar
} = useMessageUIStore()

// 设置主题
setTheme('dark')

// 设置消息密度
setMessagesDensity('compact')

// 切换侧边栏
toggleSidebar()
```

**4. 通知系统**

```typescript
const { addNotification, removeNotification } = useMessageUIStore()

// 添加通知
const notificationId = addNotification({
  type: 'success',
  title: '消息发送成功',
  message: '您的消息已成功发送',
  duration: 3000, // 3秒后自动消失
  actions: [
    {
      label: '撤销',
      handler: () => undoSendMessage()
    }
  ]
})

// 手动移除通知
removeNotification(notificationId)
```

---

### ChatProvider - 统一接口层

**文件位置**: `src/components/chat/chat-provider.tsx`

#### 接口定义

```typescript
export interface ChatAPI {
  // 消息操作
  sendMessage: (content: string, options?: SendMessageOptions) => Promise<void>
  retryMessage: (messageId: string) => Promise<void>
  updateMessage: (messageId: string, updates: Partial<WebSocketMessage>) => boolean
  deleteMessage: (messageId: string) => boolean
  
  // 消息查询
  messages: WebSocketMessage[]
  pendingMessages: PendingSentMessage[]
  getMessage: (messageId: string) => WebSocketMessage | undefined
  searchMessages: (query: string) => WebSocketMessage[]
  
  // 会话管理
  currentSession: ChatSession | null
  createSession: () => Promise<ChatSession>
  switchSession: (sessionId: string) => Promise<void>
  deleteSession: (sessionId: string) => void
  
  // 连接状态
  connectionStatus: ConnectionStatus
  isConnected: boolean
  connectionError: string | undefined
  reconnect: () => Promise<void>
  
  // UI状态和交互
  input: InputState
  interaction: InteractionState
  interface: InterfaceState
  
  // 扩展系统
  registry: ChatRegistry
  registerMessageType: <T = any>(type: string, config: MessageTypeConfig<T>) => void
  getSupportedTypes: () => string[]
  
  // 工具方法
  notify: NotificationMethods
  getDiagnostics: () => ChatDiagnostics
}
```

#### 使用方法

**1. 基础使用**

```typescript
import { useChat } from '@/components/chat/chat-provider'

function ChatComponent() {
  const {
    messages,           // 当前会话消息
    sendMessage,       // 发送消息方法
    input,             // 输入状态
    connectionStatus,  // 连接状态
    isConnected       // 是否已连接
  } = useChat()
  
  const handleSend = async () => {
    if (input.content.trim() && isConnected) {
      await sendMessage(input.content)
      input.clearContent()
    }
  }
  
  return (
    <div>
      {messages.map(msg => (
        <div key={msg.id}>{msg.payload.content}</div>
      ))}
      
      <input 
        value={input.content}
        onChange={e => input.updateContent(e.target.value)}
        onKeyDown={e => e.key === 'Enter' && handleSend()}
      />
      
      <button 
        onClick={handleSend}
        disabled={!isConnected || !input.content.trim()}
      >
        发送
      </button>
    </div>
  )
}
```

**2. 高级功能使用**

```typescript
function AdvancedChatComponent() {
  const {
    messages,
    sendMessage,
    interaction,
    registerMessageType,
    notify,
    getDiagnostics
  } = useChat()
  
  // 注册自定义消息类型
  useEffect(() => {
    registerMessageType('custom', {
      processor: {
        process: async (message) => {
          // 自定义处理逻辑
          return processCustomMessage(message)
        },
        validate: (payload) => payload.type === 'custom'
      },
      renderer: CustomMessageRenderer
    })
  }, [registerMessageType])
  
  // 发送自定义消息
  const sendCustomMessage = async (data: any) => {
    try {
      await sendMessage('', {
        messageType: 'custom',
        metadata: data
      })
      notify.success('自定义消息发送成功')
    } catch (error) {
      notify.error('发送失败')
    }
  }
  
  // 获取诊断信息
  const handleDebug = () => {
    const diagnostics = getDiagnostics()
    console.log('Chat诊断信息:', diagnostics)
  }
  
  return (
    <div>
      {/* 消息列表 */}
      {messages.map(msg => (
        <div 
          key={msg.id}
          className={interaction.selectedMessages.includes(msg.id) ? 'selected' : ''}
          onClick={() => interaction.selectMessage(msg.id)}
        >
          {/* 消息内容渲染 */}
        </div>
      ))}
      
      <button onClick={() => sendCustomMessage({ type: 'poll' })}>
        发送投票
      </button>
      
      <button onClick={handleDebug}>
        获取诊断信息
      </button>
    </div>
  )
}
```

---

### MessageRenderer - 统一渲染引擎

**文件位置**: `src/components/chat/message-renderer.tsx`

#### 接口定义

```typescript
export interface MessageRendererProps {
  message: WebSocketMessage
  index?: number
  isStreaming?: boolean
  streamingContent?: string
  className?: string
  showActions?: boolean
  enableInteraction?: boolean
  customActions?: MessageAction[]
  onAction?: (action: string, data?: any) => void
}
```

#### 使用方法

**1. 基础渲染**

```typescript
import { MessageRenderer } from '@/components/chat/message-renderer'

function MessageList({ messages }: { messages: WebSocketMessage[] }) {
  return (
    <div className="message-list">
      {messages.map((message, index) => (
        <MessageRenderer
          key={message.id}
          message={message}
          index={index}
          showActions={true}
          enableInteraction={true}
          onAction={(action, data) => {
            console.log('Message action:', action, data)
          }}
        />
      ))}
    </div>
  )
}
```

**2. 自定义渲染器**

```typescript
// 定义自定义消息渲染器
const ImageMessageRenderer: React.FC<MessageRendererProps> = ({ 
  message, 
  context,
  onUpdate,
  onAction 
}) => {
  const imageData = message.payload.data as ImageData
  
  const handleImageClick = () => {
    onAction?.('imageView', { imageUrl: imageData.url })
  }
  
  return (
    <div className="image-message">
      <img 
        src={imageData.url} 
        alt={imageData.alt}
        onClick={handleImageClick}
        className="message-image"
      />
      <p className="image-caption">{imageData.caption}</p>
    </div>
  )
}

// 注册自定义渲染器
chatRegistry.registerRenderer('image', {
  component: ImageMessageRenderer,
  description: '图片消息渲染器'
})
```

---

## 🚀 5分钟开发新消息类型

### 完整开发流程

以开发一个**文件分享消息类型**为例：

**第1分钟：定义类型接口**

```typescript
// src/lib/chat/message-types/file-message.tsx

export interface FileData {
  id: string
  name: string
  size: number
  type: string
  url: string
  downloadUrl?: string
  previewUrl?: string
  uploadedBy: string
  uploadedAt: number
}

export interface FileMessage extends WebSocketMessage {
  payload: {
    type: 'file'
    content: string  // 文件描述
    data: FileData
  }
}
```

**第2分钟：创建处理器**

```typescript
const fileMessageProcessor: MessageProcessor<FileMessage> = {
  process: async (message: WebSocketMessage) => {
    const fileData = message.payload?.data as FileData
    
    // 验证文件数据
    if (!fileData?.id || !fileData?.name) {
      throw new Error('Invalid file data')
    }
    
    // 文件大小格式化
    const formattedSize = formatFileSize(fileData.size)
    
    // 生成文件图标
    const fileIcon = getFileIcon(fileData.type)
    
    return {
      ...message,
      payload: {
        ...message.payload,
        data: {
          ...fileData,
          formattedSize,
          fileIcon
        }
      }
    } as FileMessage
  },
  
  validate: (payload: any) => {
    return payload?.type === 'file' && 
           payload?.data?.id && 
           payload?.data?.name
  }
}
```

**第3分钟：创建渲染器**

```typescript
const FileMessageRenderer: React.FC<MessageRendererProps> = ({ 
  message, 
  onAction 
}) => {
  const fileData = (message as FileMessage).payload.data
  
  const handleDownload = () => {
    onAction?.('download', { fileId: fileData.id })
  }
  
  const handlePreview = () => {
    if (fileData.previewUrl) {
      onAction?.('preview', { fileId: fileData.id })
    }
  }
  
  return (
    <Card className="file-message">
      <CardContent className="flex items-center space-x-3 p-4">
        <div className="flex-shrink-0">
          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            {fileData.fileIcon}
          </div>
        </div>
        
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-medium text-gray-900 truncate">
            {fileData.name}
          </h4>
          <p className="text-sm text-gray-500">
            {fileData.formattedSize} • 由 {fileData.uploadedBy} 分享
          </p>
        </div>
        
        <div className="flex-shrink-0 flex space-x-2">
          {fileData.previewUrl && (
            <Button variant="outline" size="sm" onClick={handlePreview}>
              预览
            </Button>
          )}
          <Button variant="default" size="sm" onClick={handleDownload}>
            下载
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
```

**第4分钟：注册消息类型**

```typescript
export function registerFileMessageType() {
  // 注册处理器
  chatRegistry.registerProcessor('file', fileMessageProcessor)
  
  // 注册渲染器
  chatRegistry.registerRenderer('file', {
    component: FileMessageRenderer,
    description: '文件分享消息渲染器'
  })
  
  console.log('✅ 文件消息类型注册成功')
}

// 便捷创建函数
export function createFileMessage(
  fileData: FileData,
  description = ''
): Omit<FileMessage, 'id' | 'timestamp' | 'sessionId'> {
  return {
    userId: '',
    groupChatId: '',
    organizationId: '',
    payload: {
      type: 'file',
      content: description || `分享了文件: ${fileData.name}`,
      data: fileData
    }
  }
}
```

**第5分钟：使用和测试**

```typescript
// 在应用初始化时注册
registerFileMessageType()

// 使用示例
function FileUploadComponent() {
  const { sendMessage } = useChat()
  
  const handleFileUpload = async (file: File) => {
    try {
      // 上传文件
      const uploadResult = await uploadFile(file)
      
      // 创建文件消息
      const fileMessage = createFileMessage({
        id: uploadResult.id,
        name: file.name,
        size: file.size,
        type: file.type,
        url: uploadResult.url,
        downloadUrl: uploadResult.downloadUrl,
        uploadedBy: currentUser.name,
        uploadedAt: Date.now()
      })
      
      // 发送消息
      await sendMessage('', {
        messageType: 'file',
        metadata: fileMessage.payload
      })
      
    } catch (error) {
      console.error('文件上传失败:', error)
    }
  }
  
  return (
    <input 
      type="file" 
      onChange={e => e.files?.[0] && handleFileUpload(e.files[0])}
    />
  )
}
```

---

## 🔧 高级功能详解

### 错误处理和重试机制

```typescript
// 消息发送错误处理
const { sendMessage, notify } = useChat()

const sendWithRetry = async (content: string, maxRetries = 3) => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      await sendMessage(content)
      return // 成功发送，退出重试
    } catch (error) {
      if (i === maxRetries - 1) {
        // 最后一次重试失败
        notify.error(`消息发送失败: ${error.message}`)
        throw error
      } else {
        // 等待后重试
        await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
      }
    }
  }
}
```

### 性能优化技巧

```typescript
// 1. 使用专门的Selector Hook减少重渲染
const messages = useSessionMessages(sessionId) // 自动memoization
const unreadCount = useUnreadCount(sessionId)   // 只在未读数变化时更新

// 2. 批量操作优化
const { batchAddMessages } = useMessageDataStore()
await batchAddMessages(sessionId, largeMsgArray) // 一次性添加大量消息

// 3. 虚拟化长列表
import { VirtualizedMessageList } from '@/components/chat/virtualized-message-list'

<VirtualizedMessageList
  messages={messages}
  itemHeight={80}
  containerHeight={400}
  renderMessage={(message, index) => (
    <MessageRenderer key={message.id} message={message} index={index} />
  )}
/>
```

### 实时协作功能

```typescript
// 多用户同时编辑检测
const { interaction } = useChat()

useEffect(() => {
  const handleUserTyping = (data: { userId: string, messageId: string }) => {
    if (data.messageId === interaction.editingMessage) {
      notify.warning(`用户 ${data.userId} 也在编辑此消息`)
    }
  }
  
  websocket.on('userTyping', handleUserTyping)
  return () => websocket.off('userTyping', handleUserTyping)
}, [interaction.editingMessage])
```

### 调试和诊断

```typescript
// 获取详细的系统诊断信息
const { getDiagnostics } = useChat()

const debugChat = () => {
  const diagnostics = getDiagnostics()
  
  console.log('=== Chat系统诊断 ===')
  console.log('消息统计:', diagnostics.messages)
  console.log('连接状态:', diagnostics.connection) 
  console.log('注册表状态:', diagnostics.registry)
  console.log('性能指标:', diagnostics.performance)
  
  // 检查是否有异常
  if (diagnostics.messages.pending > 10) {
    console.warn('⚠️ 发送队列积压过多')
  }
  
  if (diagnostics.performance.memoryUsage > '100MB') {
    console.warn('⚠️ 内存使用过高')
  }
}

// 在开发环境中暴露调试方法
if (process.env.NODE_ENV === 'development') {
  (window as any).debugChat = debugChat
}
```

---

## 📚 最佳实践

### 1. 消息类型设计原则

```typescript
// ✅ 好的设计：数据和渲染分离
interface GoodMessageData {
  id: string
  title: string
  items: Array<{ id: string; text: string; value: any }>
  config: { multiSelect: boolean; timeout?: number }
}

// ❌ 不好的设计：包含UI状态
interface BadMessageData {
  id: string
  title: string
  items: Array<{ 
    id: string; 
    text: string; 
    value: any;
    isSelected: boolean;  // ❌ UI状态不应该在数据中
    isHighlighted: boolean; // ❌ UI状态不应该在数据中
  }>
}
```

### 2. 性能优化原则

```typescript
// ✅ 使用专门的Selector Hook
const messages = useSessionMessages(sessionId)

// ❌ 直接使用Store会导致不必要的重渲染
const { messages } = useMessageDataStore() // 所有消息变化都会触发重渲染
```

### 3. 错误处理原则

```typescript
// ✅ 完整的错误处理
const processor: MessageProcessor<CustomData> = {
  process: async (message) => {
    try {
      // 验证数据
      if (!message.payload?.data) {
        throw new ProcessError('缺少消息数据', 'MISSING_DATA')
      }
      
      // 处理业务逻辑
      const result = await processCustomData(message.payload.data)
      
      return {
        ...message,
        payload: {
          ...message.payload,
          data: result
        }
      }
    } catch (error) {
      // 记录错误日志
      console.error('消息处理失败:', error)
      
      // 抛出标准化错误
      throw new ProcessError(
        `处理失败: ${error.message}`,
        'PROCESS_FAILED',
        { originalError: error }
      )
    }
  },
  
  validate: (payload) => {
    // 严格的数据验证
    return payload?.type === 'custom' && 
           payload?.data &&
           typeof payload.data.id === 'string'
  }
}
```

### 4. 类型安全原则

```typescript
// ✅ 完整的TypeScript支持
interface TypeSafeMessageData {
  id: string
  type: 'poll' | 'survey' | 'quiz'
  config: {
    multiSelect: boolean
    allowAnonymous: boolean
    deadline?: Date
  }
}

const typeSafeProcessor: MessageProcessor<TypeSafeMessageData> = {
  process: async (message) => {
    // TypeScript会提供完整的类型检查和智能提示
    const data = message.payload.data
    
    if (data.type === 'poll') {
      // 编译器知道这里的类型信息
      return processPollData(data)
    }
    
    return message
  }
}
```

---

## 🔮 扩展示例集合

### 1. 位置分享消息

```typescript
// 定义数据结构
interface LocationData {
  latitude: number
  longitude: number
  address: string
  accuracy: number
  timestamp: number
}

// 处理器
const locationProcessor: MessageProcessor<LocationData> = {
  process: async (message) => {
    const location = message.payload.data as LocationData
    
    // 获取详细地址信息
    const detailedAddress = await reverseGeocode(
      location.latitude, 
      location.longitude
    )
    
    return {
      ...message,
      payload: {
        ...message.payload,
        data: {
          ...location,
          detailedAddress
        }
      }
    }
  }
}

// 渲染器
const LocationRenderer: React.FC<MessageRendererProps> = ({ message }) => {
  const location = message.payload.data as LocationData
  
  return (
    <Card className="location-message">
      <CardContent>
        <div className="flex items-center space-x-3">
          <MapPin className="w-8 h-8 text-red-500" />
          <div>
            <h4 className="font-medium">{location.address}</h4>
            <p className="text-sm text-gray-500">
              精度: {location.accuracy}米
            </p>
          </div>
        </div>
        <div className="mt-3">
          <a 
            href={`https://maps.google.com/?q=${location.latitude},${location.longitude}`}
            target="_blank"
            className="text-blue-600 hover:underline"
          >
            在地图中查看
          </a>
        </div>
      </CardContent>
    </Card>
  )
}
```

### 2. 代码分享消息

```typescript
interface CodeData {
  language: string
  code: string
  filename?: string
  description?: string
}

const CodeRenderer: React.FC<MessageRendererProps> = ({ message }) => {
  const codeData = message.payload.data as CodeData
  const [copied, setCopied] = useState(false)
  
  const handleCopy = async () => {
    await navigator.clipboard.writeText(codeData.code)
    setCopied(true)
    setTimeout(() => setCopied(false), 2000)
  }
  
  return (
    <Card className="code-message">
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <Badge variant="secondary">{codeData.language}</Badge>
          {codeData.filename && (
            <span className="ml-2 text-sm text-gray-600">
              {codeData.filename}
            </span>
          )}
        </div>
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={handleCopy}
        >
          {copied ? <Check className="w-4 h-4" /> : <Copy className="w-4 h-4" />}
        </Button>
      </CardHeader>
      <CardContent>
        <pre className="bg-gray-100 p-3 rounded-md overflow-x-auto">
          <code className={`language-${codeData.language}`}>
            {codeData.code}
          </code>
        </pre>
        {codeData.description && (
          <p className="mt-2 text-sm text-gray-600">
            {codeData.description}
          </p>
        )}
      </CardContent>
    </Card>
  )
}
```

### 3. 任务分配消息

```typescript
interface TaskData {
  id: string
  title: string
  description: string
  assignee: string
  dueDate: Date
  priority: 'low' | 'medium' | 'high'
  status: 'pending' | 'in_progress' | 'completed'
}

const TaskRenderer: React.FC<MessageRendererProps> = ({ 
  message, 
  onAction 
}) => {
  const task = message.payload.data as TaskData
  
  const handleStatusChange = (newStatus: TaskData['status']) => {
    onAction?.('updateTaskStatus', { 
      taskId: task.id, 
      status: newStatus 
    })
  }
  
  const priorityColors = {
    low: 'text-green-600 bg-green-100',
    medium: 'text-yellow-600 bg-yellow-100',
    high: 'text-red-600 bg-red-100'
  }
  
  return (
    <Card className="task-message">
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h4 className="font-medium">{task.title}</h4>
            <p className="text-sm text-gray-600 mt-1">
              {task.description}
            </p>
            <div className="flex items-center space-x-2 mt-2">
              <Badge className={priorityColors[task.priority]}>
                {task.priority}
              </Badge>
              <span className="text-sm text-gray-500">
                分配给: {task.assignee}
              </span>
              <span className="text-sm text-gray-500">
                截止: {task.dueDate.toLocaleDateString()}
              </span>
            </div>
          </div>
          
          <Select
            value={task.status}
            onValueChange={handleStatusChange}
          >
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pending">待处理</SelectItem>
              <SelectItem value="in_progress">进行中</SelectItem>
              <SelectItem value="completed">已完成</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </CardContent>
    </Card>
  )
}
```

---

## 📖 总结

本文档详细介绍了Chat系统的全新架构设计，从函数调用级别阐述了每个组件的设计理念和使用方法。新架构具有以下核心优势：

### 🎯 **设计理念实现**
- **分层架构**: 数据、业务、表现、接口四层清晰分离
- **插件化扩展**: 通过Registry实现真正的热插拔能力
- **开发者体验优先**: 5分钟开发新功能不再是口号

### 🚀 **技术创新**
- **统一API**: 从4个Store依赖简化为1个Hook
- **类型安全**: 完整的TypeScript支持和智能提示
- **性能优化**: 专门的Selector Hook和批量操作

### 💪 **生产就绪**
- **完整的错误处理**: 标准化的错误类型和重试机制
- **实时协作支持**: 多用户交互和冲突检测
- **调试和诊断**: 完善的监控和性能分析

这个架构不仅解决了当前的技术债务，更为团队的长期发展提供了坚实的技术基础。无论是初级开发者还是资深工程师，都能在这个架构的引导下高效地进行功能开发和维护。

**架构的成功标准不是代码有多复杂，而是让复杂的事情变得简单。**

---

*文档版本: v2.0*  
*最后更新: 2025年1月*  
*维护者: 架构团队*