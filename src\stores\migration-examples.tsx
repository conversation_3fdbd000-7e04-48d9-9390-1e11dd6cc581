/**
 * 聊天Store迁移示例
 * 
 * 这个文件展示如何从旧的三个独立Store迁移到统一的ChatStore
 * 包含具体的代码示例和最佳实践
 */

import { 
  useUnifiedChatStore,
  useSessionMessages,
  usePendingMessages,
  useInputState,
  useChatActions,
  useChatDiagnostics
} from './unified-chat-store'
import type { ClientMessage, BaseWebSocketMessage } from '@/types/websocket-event-type'

// ============================================================================
// 示例1：基础Hook使用（向后兼容）
// ============================================================================

/**
 * 聊天消息列表组件
 * 展示如何使用向后兼容的Hooks
 */
export const ChatMessageList = ({ sessionId }: { sessionId: string }) => {
  // 这些Hooks与原来的使用方式完全相同
  const messages = useSessionMessages(sessionId)
  const pendingMessages = usePendingMessages(sessionId)
  const inputState = useInputState()

  return (
    <div className="chat-messages">
      {/* 显示已发送消息 */}
      {messages.map(message => (
        <div key={message.id} className="message">
          {message.payload.type === 'user_message' && (
            <div className="user-message">
              {message.payload.content}
            </div>
          )}
          {message.payload.type === 'assistant_message' && (
            <div className="assistant-message">
              {message.payload.content}
            </div>
          )}
        </div>
      ))}

      {/* 显示发送中消息 */}
      {pendingMessages.map(pending => (
        <div key={pending.tempId} className="pending-message">
          <span className="content">{pending.content}</span>
          <span className="status">{pending.status}</span>
        </div>
      ))}

      {/* 输入框状态 */}
      <div className="input-info">
        输入内容: {inputState.content}
        正在输入: {inputState.isTyping ? '是' : '否'}
      </div>
    </div>
  )
}

// ============================================================================
// 示例2：统一操作接口使用
// ============================================================================

/**
 * 聊天操作组件
 * 展示如何使用统一的操作接口
 */
export const ChatActions = ({ sessionId }: { sessionId: string }) => {
  // 获取统一的操作接口
  const actions = useChatActions()
  
  // 或者直接使用Store
  const store = useUnifiedChatStore()

  const handleSendMessage = async (content: string) => {
    try {
      // 方式1：使用统一操作接口
      await actions.sendUserMessage(content, sessionId)
      actions.updateInputContent('')

      // 方式2：直接使用Store（相同效果）
      // await store.sendUserMessage(content, sessionId)
      // store.updateInputContent('')

    } catch (error) {
      console.error('发送消息失败:', error)
      
      // 显示错误通知
      store.addNotification({
        type: 'error',
        title: '发送失败',
        message: error instanceof Error ? error.message : '未知错误'
      })
    }
  }

  const handleDeleteMessage = (messageId: string) => {
    const success = store.deleteMessage(messageId)
    
    if (success) {
      store.addNotification({
        type: 'success',
        title: '删除成功',
        duration: 2000
      })
    }
  }

  return (
    <div className="chat-actions">
      <button onClick={() => handleSendMessage('Hello!')}>
        发送消息
      </button>
      <button onClick={() => handleDeleteMessage('some-message-id')}>
        删除消息
      </button>
    </div>
  )
}

// ============================================================================
// 示例3：批量操作使用
// ============================================================================

/**
 * 批量消息操作
 * 展示如何使用批量操作优化性能
 */
export const BatchMessageOperations = () => {
  const store = useUnifiedChatStore()

  const handleBatchAddMessages = (sessionId: string, messages: ClientMessage[]) => {
    // 方式1：使用专门的批量添加方法（推荐）
    store.addMessages(sessionId, messages)

    // 方式2：使用通用批量操作
    const results = store.batchOperation(
      messages.map(message => () => store.addMessage(sessionId, message))
    )
    
    console.log('批量操作结果:', results)
  }

  const handleBatchUpdate = () => {
    // 批量更新消息
    const updates = [
      { messageId: 'msg-1', updates: { status: 'delivered' as const } },
      { messageId: 'msg-2', updates: { status: 'delivered' as const } },
      { messageId: 'msg-3', updates: { status: 'delivered' as const } }
    ]

    const successCount = store.batchUpdateMessages(updates)
    console.log(`成功更新 ${successCount} 条消息`)
  }

  const handleComplexBatchOperation = () => {
    // 复杂的批量操作：添加消息、更新UI状态、清理缓存
    store.batchOperation([
      () => store.addMessage('session-1', createSampleMessage()),
      () => store.updateInputContent(''),
      () => store.scrollToBottom(),
      () => store.cleanupSentMessages('session-1')
    ])
  }

  return (
    <div className="batch-operations">
      <button onClick={() => handleBatchAddMessages('session-1', [])}>
        批量添加消息
      </button>
      <button onClick={handleBatchUpdate}>
        批量更新消息
      </button>
      <button onClick={handleComplexBatchOperation}>
        复杂批量操作
      </button>
    </div>
  )
}

// ============================================================================
// 示例4：类型迁移使用
// ============================================================================

/**
 * 类型迁移示例
 * 展示如何在新旧类型之间转换
 */
export const TypeMigrationExample = () => {
  const store = useUnifiedChatStore()

  const handleOldMessageProcessing = (oldMessage: BaseWebSocketMessage) => {
    // 将旧类型转换为新类型
    const newMessage = store.migrateMessage(oldMessage)
    
    // 使用新类型进行处理
    store.addMessage(newMessage.sessionId, newMessage)
    
    console.log('迁移后的消息:', newMessage)
  }

  const handleNewMessageToOldSystem = (newMessage: ClientMessage) => {
    // 如果需要与旧系统交互，转换为旧类型
    const oldMessage = store.migrateToBaseMessage(newMessage)
    
    // 发送给旧系统（示例）
    // legacySystem.processMessage(oldMessage)
    
    console.log('转换为旧格式:', oldMessage)
  }

  return (
    <div className="type-migration">
      <p>类型迁移示例 - 查看控制台输出</p>
    </div>
  )
}

// ============================================================================
// 示例5：诊断和调试
// ============================================================================

/**
 * 调试面板组件
 * 展示如何使用诊断功能
 */
export const ChatDebugPanel = () => {
  const diagnostics = useChatDiagnostics()
  const store = useUnifiedChatStore()

  const handleFullReset = () => {
    // 清理所有状态
    store.batchOperation([
      () => {
        // 清理所有会话
        const sessionIds = Object.keys(store.data.messages)
        sessionIds.forEach(sessionId => store.deleteSession(sessionId))
      },
      () => store.cleanupSentMessages(),
      () => store.cleanupStreamingMessages(),
      () => store.clearNotifications(),
      () => store.clearInput()
    ])
  }

  const handleRebuildIndex = () => {
    store.rebuildMessageIndex()
    store.addNotification({
      type: 'info',
      title: '索引重建完成',
      duration: 3000
    })
  }

  return (
    <div className="debug-panel">
      <h3>聊天Store诊断信息</h3>
      
      <div className="diagnostics-section">
        <h4>数据层</h4>
        <p>总会话数: {diagnostics.data.totalSessions}</p>
        <p>总消息数: {diagnostics.data.totalMessages}</p>
        <p>索引大小: {diagnostics.data.indexSize}</p>
        <p>内存使用: {diagnostics.data.memoryUsage}</p>
      </div>

      <div className="diagnostics-section">
        <h4>流程层</h4>
        <p>待发送消息: {diagnostics.flow.pendingCount}</p>
        <p>流式消息: {diagnostics.flow.streamingCount}</p>
        <p>处理状态: {diagnostics.flow.processingState ? '处理中' : '空闲'}</p>
      </div>

      <div className="diagnostics-section">
        <h4>UI层</h4>
        <p>输入焦点: {diagnostics.ui.inputState ? '是' : '否'}</p>
        <p>选中消息数: {diagnostics.ui.interactionState}</p>
        <p>通知数量: {diagnostics.ui.notificationCount}</p>
      </div>

      <div className="debug-actions">
        <button onClick={handleRebuildIndex}>重建索引</button>
        <button onClick={handleFullReset} style={{ color: 'red' }}>
          完全重置 (危险)
        </button>
      </div>
    </div>
  )
}

// ============================================================================
// 示例6：流式消息处理
// ============================================================================

/**
 * 流式消息处理示例
 */
export const StreamingMessageExample = () => {
  const store = useUnifiedChatStore()
  const streamingMessages = store.flow.streamingMessages

  const simulateStreamingMessage = () => {
    const messageId = `stream_${Date.now()}`
    const chunks = ['Hello', ' world', '!', ' How', ' are', ' you', '?']
    
    // 模拟流式传输
    chunks.forEach((chunk, index) => {
      setTimeout(() => {
        const isComplete = index === chunks.length - 1
        store.handleStreamingChunk(messageId, chunk, isComplete)
      }, index * 500)
    })
  }

  return (
    <div className="streaming-example">
      <button onClick={simulateStreamingMessage}>
        模拟流式消息
      </button>
      
      <div className="streaming-messages">
        {Object.entries(streamingMessages).map(([messageId, streaming]) => (
          <div key={messageId} className="streaming-message">
            <div className="message-id">ID: {messageId}</div>
            <div className="accumulated-content">
              内容: {streaming.accumulatedContent}
            </div>
            <div className="status">
              状态: {streaming.isComplete ? '完成' : '进行中'}
            </div>
            <div className="chunks">
              块数: {streaming.chunks.length}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

// ============================================================================
// 工具函数
// ============================================================================

/**
 * 创建示例消息
 */
const createSampleMessage = (): ClientMessage => {
  const now = Date.now()
  return {
    // 后端数据
    groupChatId: 'default-group',
    sessionId: 'sample-session',
    userId: 'user-123',
    organizationId: 'org-456',
    payload: {
      type: 'user_message',
      content: 'This is a sample message'
    },    
    timestamp: now,
    
    // 前端状态
    id: `msg_${now}`,
    status: 'sent',
    metadata: {},
    createdAt: now,
    updatedAt: now
  }
}

/**
 * 迁移使用示例组合组件
 */
export const MigrationExamplesApp = () => {
  return (
    <div className="migration-examples-app">
      <h2>聊天Store迁移示例</h2>
      
      <section>
        <h3>1. 基础Hook使用</h3>
        <ChatMessageList sessionId="example-session" />
      </section>

      <section>
        <h3>2. 统一操作接口</h3>
        <ChatActions sessionId="example-session" />
      </section>

      <section>
        <h3>3. 批量操作</h3>
        <BatchMessageOperations />
      </section>

      <section>
        <h3>4. 流式消息</h3>
        <StreamingMessageExample />
      </section>

      <section>
        <h3>5. 调试面板</h3>
        <ChatDebugPanel />
      </section>
    </div>
  )
}

// ============================================================================
// 迁移检查工具
// ============================================================================

/**
 * 迁移兼容性检查
 */
export const checkMigrationCompatibility = () => {
  const store = useUnifiedChatStore.getState()
  
  const report = {
    storeInitialized: !!store,
    dataLayerReady: !!store.data,
    flowLayerReady: !!store.flow,
    uiLayerReady: !!store.ui,
    hooksAvailable: {
      useSessionMessages: typeof useSessionMessages === 'function',
      usePendingMessages: typeof usePendingMessages === 'function', 
      useInputState: typeof useInputState === 'function'
    },
    actionsAvailable: {
      addMessage: typeof store.addMessage === 'function',
      sendUserMessage: typeof store.sendUserMessage === 'function',
      updateInputContent: typeof store.updateInputContent === 'function'
    },
    migrationToolsAvailable: {
      migrateMessage: typeof store.migrateMessage === 'function',
      batchOperation: typeof store.batchOperation === 'function'
    }
  }

  console.log('迁移兼容性检查报告:', report)
  return report
}