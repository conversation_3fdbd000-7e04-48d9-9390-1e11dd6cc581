/**
 * Mock数据管理器
 * 
 * 功能：
 * 1. 持久化Mock数据存储
 * 2. 会话、用户、消息的Mock数据管理  
 * 3. 支持预定义场景和动态数据生成
 * 4. 数据导入/导出功能
 * 5. 与unified-chat-store集成
 * 
 * 设计原则：
 * - 与ClientMessage类型完全兼容
 * - 支持数据持久化和恢复
 * - 提供丰富的数据操作API
 * - 内存优化和垃圾回收
 */

import type { ClientMessage, BackendWebSocketMessage } from '@/types/websocket-event-type'
import { MessageFactory } from '@/types/websocket-event-type'
import { MockDataFactory, MOCK_AI_ASSISTANT_ID } from './mock-data-factory'
import { ScenarioManager } from './mock-scenarios'
import { getMockConfig } from './mock-config'

// ============================================================================
// Mock数据类型定义
// ============================================================================

/**
 * Mock用户信息
 */
export interface MockUser {
  id: string
  name: string
  avatar?: string
  role: 'user' | 'assistant' | 'system'
  organizationId: string
  isOnline: boolean
  lastActive: number
  metadata: Record<string, any>
}

/**
 * Mock会话信息
 */
export interface MockSession {
  sessionId: string
  groupChatId: string
  organizationId: string
  participants: string[] // userId列表
  createdAt: number
  updatedAt: number
  lastMessageTime: number
  messageCount: number
  isActive: boolean
  scenario?: string
  metadata: Record<string, any>
}

/**
 * Mock消息存储项
 */
export interface MockMessageItem {
  message: ClientMessage
  sessionId: string
  createdAt: number
  deliveredAt?: number
  readAt?: number
  tags: string[]
}

/**
 * Mock数据库
 */
export interface MockDatabase {
  users: Record<string, MockUser>
  sessions: Record<string, MockSession>
  messages: Record<string, MockMessageItem>
  scenarios: Record<string, any>
  metadata: {
    version: string
    createdAt: number
    updatedAt: number
    totalMessages: number
    totalSessions: number
  }
}

/**
 * 数据统计信息
 */
export interface MockDataStats {
  totalUsers: number
  totalSessions: number
  totalMessages: number
  activeSessions: number
  memoryUsage: string
  oldestMessage: number
  newestMessage: number
  messagesByType: Record<string, number>
}

// ============================================================================
// Mock数据管理器类
// ============================================================================

export class MockDataManager {
  private static instance: MockDataManager
  private database: MockDatabase
  private cleanupTimer: NodeJS.Timeout | null = null
  private listeners: Set<(event: string, data: any) => void> = new Set()

  private constructor() {
    this.database = this.createEmptyDatabase()
    this.loadFromStorage()
    this.setupCleanupTimer()
    this.initializeDefaultData()

    console.log('🗄️ Mock数据管理器初始化完成')
  }

  /**
   * 获取单例实例
   */
  static getInstance(): MockDataManager {
    if (!MockDataManager.instance) {
      MockDataManager.instance = new MockDataManager()
    }
    return MockDataManager.instance
  }

  // ============================================================================
  // 用户管理
  // ============================================================================

  /**
   * 创建Mock用户
   */
  createUser(options: Partial<MockUser> & Pick<MockUser, 'id' | 'name' | 'organizationId'>): MockUser {
    const user: MockUser = {
      role: 'user',
      isOnline: true,
      lastActive: Date.now(),
      metadata: {},
      ...options,
    }

    this.database.users[user.id] = user
    this.saveToStorage()
    this.notifyListeners('userCreated', user)

    console.log('👤 创建Mock用户:', user.name)
    return user
  }

  /**
   * 获取用户
   */
  getUser(userId: string): MockUser | null {
    return this.database.users[userId] || null
  }

  /**
   * 获取所有用户
   */
  getAllUsers(): MockUser[] {
    return Object.values(this.database.users)
  }

  /**
   * 更新用户信息
   */
  updateUser(userId: string, updates: Partial<MockUser>): MockUser | null {
    const user = this.database.users[userId]
    if (!user) return null

    Object.assign(user, updates)
    user.lastActive = Date.now()
    
    this.saveToStorage()
    this.notifyListeners('userUpdated', user)
    
    return user
  }

  /**
   * 删除用户
   */
  deleteUser(userId: string): boolean {
    if (!this.database.users[userId]) return false

    delete this.database.users[userId]
    this.saveToStorage()
    this.notifyListeners('userDeleted', { userId })
    
    console.log('👤 删除Mock用户:', userId)
    return true
  }

  // ============================================================================
  // 会话管理
  // ============================================================================

  /**
   * 创建Mock会话
   */
  createSession(options: Partial<MockSession> & Pick<MockSession, 'sessionId' | 'organizationId'>): MockSession {
    const now = Date.now()
    const session: MockSession = {
      groupChatId: `group_${Date.now()}`,
      participants: [],
      createdAt: now,
      updatedAt: now,
      lastMessageTime: now,
      messageCount: 0,
      isActive: true,
      metadata: {},
      ...options,
    }

    this.database.sessions[session.sessionId] = session
    this.database.metadata.totalSessions++
    this.saveToStorage()
    this.notifyListeners('sessionCreated', session)

    console.log('💬 创建Mock会话:', session.sessionId)
    return session
  }

  /**
   * 获取会话
   */
  getSession(sessionId: string): MockSession | null {
    return this.database.sessions[sessionId] || null
  }

  /**
   * 获取所有会话
   */
  getAllSessions(): MockSession[] {
    return Object.values(this.database.sessions)
  }

  /**
   * 获取活跃会话
   */
  getActiveSessions(): MockSession[] {
    return Object.values(this.database.sessions).filter(session => session.isActive)
  }

  /**
   * 更新会话信息
   */
  updateSession(sessionId: string, updates: Partial<MockSession>): MockSession | null {
    const session = this.database.sessions[sessionId]
    if (!session) return null

    Object.assign(session, updates)
    session.updatedAt = Date.now()
    
    this.saveToStorage()
    this.notifyListeners('sessionUpdated', session)
    
    return session
  }

  /**
   * 删除会话
   */
  deleteSession(sessionId: string): boolean {
    if (!this.database.sessions[sessionId]) return false

    // 删除会话相关的消息
    const messageIds = Object.keys(this.database.messages).filter(
      id => this.database.messages[id].sessionId === sessionId
    )
    messageIds.forEach(id => delete this.database.messages[id])

    delete this.database.sessions[sessionId]
    this.database.metadata.totalSessions--
    this.saveToStorage()
    this.notifyListeners('sessionDeleted', { sessionId })
    
    console.log('💬 删除Mock会话:', sessionId)
    return true
  }

  // ============================================================================
  // 消息管理
  // ============================================================================

  /**
   * 添加Mock消息
   */
  addMessage(message: ClientMessage, sessionId?: string): MockMessageItem {
    const targetSessionId = sessionId || message.sessionId || 'default-session'
    const now = Date.now()

    // 确保会话存在
    if (!this.database.sessions[targetSessionId]) {
      this.createSession({
        sessionId: targetSessionId,
        organizationId: message.organizationId || 'default-org',
      })
    }

    const messageItem: MockMessageItem = {
      message,
      sessionId: targetSessionId,
      createdAt: now,
      tags: this.generateMessageTags(message),
    }

    this.database.messages[message.id] = messageItem
    this.database.metadata.totalMessages++

    // 更新会话信息
    const session = this.database.sessions[targetSessionId]
    session.messageCount++
    session.lastMessageTime = now
    session.updatedAt = now

    this.saveToStorage()
    this.notifyListeners('messageAdded', messageItem)

    console.log('📨 添加Mock消息:', {
      messageId: message.id,
      sessionId: targetSessionId,
      type: message.payload?.type,
    })

    return messageItem
  }

  /**
   * 获取消息
   */
  getMessage(messageId: string): MockMessageItem | null {
    return this.database.messages[messageId] || null
  }

  /**
   * 获取会话消息
   */
  getSessionMessages(sessionId: string): MockMessageItem[] {
    return Object.values(this.database.messages)
      .filter(item => item.sessionId === sessionId)
      .sort((a, b) => a.createdAt - b.createdAt)
  }

  /**
   * 查找消息
   */
  findMessages(predicate: (item: MockMessageItem) => boolean): MockMessageItem[] {
    return Object.values(this.database.messages).filter(predicate)
  }

  /**
   * 更新消息
   */
  updateMessage(messageId: string, updates: Partial<ClientMessage>): MockMessageItem | null {
    const messageItem = this.database.messages[messageId]
    if (!messageItem) return null

    Object.assign(messageItem.message, updates)
    messageItem.message.updatedAt = Date.now()
    
    this.saveToStorage()
    this.notifyListeners('messageUpdated', messageItem)
    
    return messageItem
  }

  /**
   * 删除消息
   */
  deleteMessage(messageId: string): boolean {
    const messageItem = this.database.messages[messageId]
    if (!messageItem) return false

    // 更新会话消息计数
    const session = this.database.sessions[messageItem.sessionId]
    if (session) {
      session.messageCount = Math.max(0, session.messageCount - 1)
    }

    delete this.database.messages[messageId]
    this.database.metadata.totalMessages--
    this.saveToStorage()
    this.notifyListeners('messageDeleted', { messageId })
    
    return true
  }

  // ============================================================================
  // 场景数据生成
  // ============================================================================

  /**
   * 根据场景生成数据
   */
  generateScenarioData(scenarioId: string, sessionId?: string): ClientMessage[] {
    const targetSessionId = sessionId || `session_${Date.now()}`
    
    // 确保会话存在
    if (!this.database.sessions[targetSessionId]) {
      this.createSession({
        sessionId: targetSessionId,
        organizationId: 'mock-org',
        scenario: scenarioId,
      })
    }

    // 生成场景消息
    const backendMessages = ScenarioManager.generateScenarioData(scenarioId, {
      sessionId: targetSessionId,
      groupChatId: `group_${targetSessionId}`,
      organizationId: 'mock-org',
    })

    // 转换为ClientMessage并添加到数据库
    const clientMessages: ClientMessage[] = []
    for (const backendMessage of backendMessages) {
      const clientMessage = MessageFactory.createClientMessage(backendMessage)
      this.addMessage(clientMessage, targetSessionId)
      clientMessages.push(clientMessage)
    }

    console.log('🎭 生成场景数据:', {
      scenarioId,
      sessionId: targetSessionId,
      messageCount: clientMessages.length,
    })

    return clientMessages
  }

  /**
   * 生成对话数据
   */
  generateConversationData(messageCount: number = 10, sessionId?: string): ClientMessage[] {
    const targetSessionId = sessionId || `conversation_${Date.now()}`
    
    // 确保会话存在
    if (!this.database.sessions[targetSessionId]) {
      this.createSession({
        sessionId: targetSessionId,
        organizationId: 'mock-org',
      })
    }

    const messages: ClientMessage[] = []
    const conversationTopics = [
      '请帮我分析一下市场趋势',
      '我们的产品用户反馈如何？',
      '能否提供一些数据洞察？',
      '下个季度的业务规划建议',
      '竞争对手分析报告',
    ]

    for (let i = 0; i < messageCount; i++) {
      const isUserMessage = i % 2 === 0
      const now = Date.now() + i * 1000

      if (isUserMessage) {
        // 用户消息
        const topic = conversationTopics[i % conversationTopics.length]
        const backendMessage: BackendWebSocketMessage = {
          groupChatId: `group_${targetSessionId}`,
          sessionId: targetSessionId,
          userId: 'mock-user',
          organizationId: 'mock-org',
          payload: {
            type: 'user_message',
            content: topic,
          },
          timestamp: now,
        }
        
        const clientMessage = MessageFactory.createClientMessage(backendMessage)
        this.addMessage(clientMessage, targetSessionId)
        messages.push(clientMessage)
      } else {
        // AI助手回复
        const responses = [
          '根据我的分析，市场呈现稳定增长态势...',
          '用户反馈总体积极，满意度达到4.2/5.0...',
          '从数据来看，主要趋势包括...',
          '建议重点关注以下几个方向...',
          '竞争分析显示我们在以下方面具有优势...',
        ]
        
        const response = responses[Math.floor(i / 2) % responses.length]
        const streamingMessages = MockDataFactory.createStreamingSequence(response, 3, {
          sessionId: targetSessionId,
          groupChatId: `group_${targetSessionId}`,
          userId: MOCK_AI_ASSISTANT_ID,
          organizationId: 'mock-org',
        })

        for (const backendMessage of streamingMessages) {
          const clientMessage = MessageFactory.createClientMessage(backendMessage)
          this.addMessage(clientMessage, targetSessionId)
          messages.push(clientMessage)
        }
      }
    }

    console.log('💬 生成对话数据:', {
      sessionId: targetSessionId,
      messageCount: messages.length,
    })

    return messages
  }

  // ============================================================================
  // 数据统计和监控
  // ============================================================================

  /**
   * 获取数据统计
   */
  getStats(): MockDataStats {
    const messages = Object.values(this.database.messages)
    const sessions = Object.values(this.database.sessions)
    
    const messagesByType: Record<string, number> = {}
    let oldestMessage = Date.now()
    let newestMessage = 0

    messages.forEach(item => {
      const type = item.message.payload?.type || 'unknown'
      messagesByType[type] = (messagesByType[type] || 0) + 1
      
      if (item.createdAt < oldestMessage) {
        oldestMessage = item.createdAt
      }
      if (item.createdAt > newestMessage) {
        newestMessage = item.createdAt
      }
    })

    // 估算内存使用
    const memorySize = JSON.stringify(this.database).length
    const memoryUsage = `${(memorySize / 1024).toFixed(2)} KB`

    return {
      totalUsers: Object.keys(this.database.users).length,
      totalSessions: Object.keys(this.database.sessions).length,
      totalMessages: Object.keys(this.database.messages).length,
      activeSessions: sessions.filter(s => s.isActive).length,
      memoryUsage,
      oldestMessage: messages.length > 0 ? oldestMessage : 0,
      newestMessage: messages.length > 0 ? newestMessage : 0,
      messagesByType,
    }
  }

  /**
   * 清理过期数据
   */
  cleanup(): void {
    const config = getMockConfig()
    const { maxSessions, maxMessagesPerSession } = config.data
    const now = Date.now()
    let cleanedSessions = 0
    let cleanedMessages = 0

    // 清理过多的会话
    const sessions = Object.values(this.database.sessions)
      .sort((a, b) => b.updatedAt - a.updatedAt)
    
    if (sessions.length > maxSessions) {
      const sessionsToDelete = sessions.slice(maxSessions)
      sessionsToDelete.forEach(session => {
        this.deleteSession(session.sessionId)
        cleanedSessions++
      })
    }

    // 清理每个会话中过多的消息
    Object.values(this.database.sessions).forEach(session => {
      const sessionMessages = this.getSessionMessages(session.sessionId)
      if (sessionMessages.length > maxMessagesPerSession) {
        const messagesToDelete = sessionMessages
          .sort((a, b) => a.createdAt - b.createdAt)
          .slice(0, sessionMessages.length - maxMessagesPerSession)
        
        messagesToDelete.forEach(item => {
          this.deleteMessage(item.message.id)
          cleanedMessages++
        })
      }
    })

    if (cleanedSessions > 0 || cleanedMessages > 0) {
      console.log('🧹 数据清理完成:', {
        cleanedSessions,
        cleanedMessages,
        remainingSessions: Object.keys(this.database.sessions).length,
        remainingMessages: Object.keys(this.database.messages).length,
      })
    }
  }

  // ============================================================================
  // 数据持久化
  // ============================================================================

  /**
   * 保存到本地存储
   */
  private saveToStorage(): void {
    if (typeof window === 'undefined') return

    try {
      const config = getMockConfig()
      if (!config.data.persistData) return

      this.database.metadata.updatedAt = Date.now()
      const serialized = JSON.stringify(this.database)
      localStorage.setItem('mock-chat-database', serialized)
    } catch (error) {
      console.warn('保存Mock数据失败:', error)
    }
  }

  /**
   * 从本地存储加载
   */
  private loadFromStorage(): void {
    if (typeof window === 'undefined') return

    try {
      const config = getMockConfig()
      if (!config.data.persistData) return

      const serialized = localStorage.getItem('mock-chat-database')
      if (serialized) {
        const loaded = JSON.parse(serialized) as MockDatabase
        this.database = { ...this.database, ...loaded }
        console.log('🗄️ Mock数据加载完成:', {
          users: Object.keys(this.database.users).length,
          sessions: Object.keys(this.database.sessions).length,
          messages: Object.keys(this.database.messages).length,
        })
      }
    } catch (error) {
      console.warn('加载Mock数据失败:', error)
    }
  }

  /**
   * 导出数据
   */
  exportData(): string {
    return JSON.stringify(this.database, null, 2)
  }

  /**
   * 导入数据
   */
  importData(data: string): void {
    try {
      const imported = JSON.parse(data) as MockDatabase
      this.database = imported
      this.saveToStorage()
      this.notifyListeners('dataImported', { messageCount: Object.keys(imported.messages).length })
      console.log('📥 Mock数据导入完成')
    } catch (error) {
      console.error('Mock数据导入失败:', error)
      throw new Error('数据格式无效')
    }
  }

  /**
   * 清空所有数据
   */
  clearAll(): void {
    this.database = this.createEmptyDatabase()
    this.saveToStorage()
    this.notifyListeners('dataCleared', {})
    console.log('🗑️ Mock数据已清空')
  }

  // ============================================================================
  // 事件监听
  // ============================================================================

  /**
   * 添加事件监听器
   */
  addEventListener(listener: (event: string, data: any) => void): () => void {
    this.listeners.add(listener)
    return () => this.listeners.delete(listener)
  }

  /**
   * 通知监听器
   */
  private notifyListeners(event: string, data: any): void {
    this.listeners.forEach(listener => {
      try {
        listener(event, data)
      } catch (error) {
        console.error('Mock数据监听器执行失败:', error)
      }
    })
  }

  // ============================================================================
  // 私有工具方法
  // ============================================================================

  /**
   * 创建空数据库
   */
  private createEmptyDatabase(): MockDatabase {
    return {
      users: {},
      sessions: {},
      messages: {},
      scenarios: {},
      metadata: {
        version: '1.0.0',
        createdAt: Date.now(),
        updatedAt: Date.now(),
        totalMessages: 0,
        totalSessions: 0,
      },
    }
  }

  /**
   * 初始化默认数据
   */
  private initializeDefaultData(): void {
    // 创建默认AI助手用户
    if (!this.database.users[MOCK_AI_ASSISTANT_ID]) {
      this.createUser({
        id: MOCK_AI_ASSISTANT_ID,
        name: 'AI助手',
        role: 'assistant',
        organizationId: 'mock-org',
        avatar: '🤖',
        metadata: {
          type: 'ai_assistant',
          capabilities: ['text_generation', 'data_analysis', 'form_processing'],
        },
      })
    }

    // 创建默认用户
    if (!this.database.users['mock-user']) {
      this.createUser({
        id: 'mock-user',
        name: '开发测试用户',
        role: 'user',
        organizationId: 'mock-org',
        avatar: '👤',
        metadata: {
          type: 'test_user',
        },
      })
    }
  }

  /**
   * 设置清理定时器
   */
  private setupCleanupTimer(): void {
    const config = getMockConfig()
    const { cleanupInterval } = config.data

    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, cleanupInterval)
  }

  /**
   * 生成消息标签
   */
  private generateMessageTags(message: ClientMessage): string[] {
    const tags: string[] = []
    
    const payloadType = message.payload?.type
    if (payloadType) {
      tags.push(`type:${payloadType}`)
    }

    if (message.userId === MOCK_AI_ASSISTANT_ID) {
      tags.push('ai_generated')
    } else {
      tags.push('user_generated')
    }

    if (message.status) {
      tags.push(`status:${message.status}`)
    }

    return tags
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
      this.cleanupTimer = null
    }
    this.listeners.clear()
    console.log('🗄️ Mock数据管理器已销毁')
  }
}

// ============================================================================
// 便捷工具函数和导出
// ============================================================================

/**
 * 获取Mock数据管理器实例
 */
export const mockDataManager = MockDataManager.getInstance()

/**
 * 便捷的数据操作函数
 */
export const mockData = {
  // 用户操作
  createUser: (options: Parameters<MockDataManager['createUser']>[0]) => 
    mockDataManager.createUser(options),
  getUser: (userId: string) => mockDataManager.getUser(userId),
  getAllUsers: () => mockDataManager.getAllUsers(),
  
  // 会话操作
  createSession: (options: Parameters<MockDataManager['createSession']>[0]) => 
    mockDataManager.createSession(options),
  getSession: (sessionId: string) => mockDataManager.getSession(sessionId),
  getAllSessions: () => mockDataManager.getAllSessions(),
  
  // 消息操作
  addMessage: (message: ClientMessage, sessionId?: string) => 
    mockDataManager.addMessage(message, sessionId),
  getMessage: (messageId: string) => mockDataManager.getMessage(messageId),
  getSessionMessages: (sessionId: string) => mockDataManager.getSessionMessages(sessionId),
  
  // 场景数据生成
  generateScenarioData: (scenarioId: string, sessionId?: string) => 
    mockDataManager.generateScenarioData(scenarioId, sessionId),
  generateConversationData: (messageCount?: number, sessionId?: string) => 
    mockDataManager.generateConversationData(messageCount, sessionId),
  
  // 统计和管理
  getStats: () => mockDataManager.getStats(),
  cleanup: () => mockDataManager.cleanup(),
  exportData: () => mockDataManager.exportData(),
  importData: (data: string) => mockDataManager.importData(data),
  clearAll: () => mockDataManager.clearAll(),
  
  // 事件监听
  addEventListener: (listener: Parameters<MockDataManager['addEventListener']>[0]) => 
    mockDataManager.addEventListener(listener),
}