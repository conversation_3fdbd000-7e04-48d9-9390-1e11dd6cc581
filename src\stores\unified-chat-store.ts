/**
 * Unified Chat Store - 统一聊天状态管理
 * 
 * 🎯 设计目标：
 * 1. 合并三个独立Store为统一状态树
 * 2. 保持分层架构：data、flow、ui三个层级
 * 3. 优化状态同步和避免重复逻辑
 * 4. 使用新的ClientMessage类型
 * 5. 保持向后兼容性
 * 
 * ⚡ 架构原则：
 * - 职责分离：每层专注自己的领域
 * - 统一操作：所有操作通过统一接口
 * - 类型安全：使用ClientMessage替代BaseWebSocketMessage
 * - 性能优化：批量操作和智能订阅
 * - 向后兼容：保持现有hook接口不变
 */

import type {
  ClientMessage
} from '@/types/websocket-event-type'
import { useMemo } from 'react'
import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'

// ============================================================================
// 稳定的常量引用 - 避免React 18 concurrent features的getSnapshot缓存问题
// ============================================================================

/**
 * 🔧 React 18 并发特性修复
 * 
 * 问题：在React 18的concurrent features下，Zustand的getSnapshot函数需要返回
 * 稳定的引用，否则会触发"getSnapshot should be cached"错误。
 * 
 * 解决方案：为所有可能返回空值的selector提供稳定的常量引用，避免每次
 * 创建新的对象或数组。
 * 
 * 影响的Hooks：
 * - useSessionMessages: 空sessionId时返回EMPTY_MESSAGES_ARRAY 
 * - usePendingMessages: 空结果时返回EMPTY_PENDING_MESSAGES_ARRAY
 * - useSessionStats: 空sessionId时返回EMPTY_STATS_OBJECT
 * - useMessage: 无效messageId时返回undefined
 * - useStreamingMessage: 无效messageId时返回undefined  
 * - useMessageInteractionState: 无效messageId时返回EMPTY_MESSAGE_INTERACTION_STATE
 * - useLoadingState: 无效key时返回undefined
 * - useErrorState: 无效key时返回undefined
 */

/** 稳定的空消息数组引用，避免每次创建新数组 */
const EMPTY_MESSAGES_ARRAY: ClientMessage[] = []

/** 稳定的空统计对象引用 */
const EMPTY_STATS_OBJECT = {
  totalMessages: 0,
  userMessages: 0,
  assistantMessages: 0,
  lastMessageTime: 0
} as const

/** 稳定的空发送消息数组引用 */
const EMPTY_PENDING_MESSAGES_ARRAY: PendingSentMessage[] = []

/** 稳定的空消息交互状态对象引用 */
const EMPTY_MESSAGE_INTERACTION_STATE = {
  isSelected: false,
  isEditing: false,
  isHovered: false,
  menuVisible: false,
  copyFeedback: undefined as boolean | undefined
}

// ============================================================================
// 核心类型定义
// ============================================================================

/**
 * 数据层状态 - 纯消息数据存储
 */
export interface ChatDataState {
  /** 消息存储 - sessionId -> messages[] */
  messages: Record<string, ClientMessage[]>
  
  /** 消息索引 - messageId -> { sessionId, index } */
  messageIndex: Record<string, { sessionId: string; index: number }>
  
  /** 会话消息统计 */
  sessionStats: Record<string, {
    totalMessages: number
    userMessages: number
    assistantMessages: number
    lastMessageTime: number
  }>
}

/**
 * 发送中的消息
 */
export interface PendingSentMessage {
  tempId: string
  content: string
  timestamp: number
  sessionId: string
  userId: string
  status: 'pending' | 'sending' | 'sent' | 'failed'
  retryCount?: number
  error?: string
}

/**
 * 流式消息累积状态
 */
export interface StreamingMessage {
  messageId: string
  sessionId: string
  accumulatedContent: string
  lastUpdateTime: number
  isComplete: boolean
  chunks: Array<{
    content: string
    timestamp: number
    chunkIndex: number
  }>
}

/**
 * 流程层状态 - 消息流处理
 */
export interface ChatFlowState {
  /** 发送中的消息队列 */
  pendingSentMessages: PendingSentMessage[]
  
  /** 流式消息累积器 */
  streamingMessages: Record<string, StreamingMessage>
  
  /** 消息处理状态 */
  isProcessing: boolean
  
  /** 重试配置 */
  retryConfig: {
    maxRetries: number
    retryDelay: number
    backoffMultiplier: number
  }
}

/**
 * 输入框状态
 */
export interface InputState {
  content: string
  isTyping: boolean
  isFocused: boolean
  height: number
  maxHeight: number
  placeholder: string
}

/**
 * 消息列表UI状态
 */
export interface MessageListUIState {
  isScrolling: boolean
  isAtBottom: boolean
  showScrollToBottom: boolean
  autoScroll: boolean
  visibleRange: {
    start: number
    end: number
  }
  containerHeight: number
}

/**
 * 消息交互状态
 */
export interface MessageInteractionState {
  selectedMessageIds: string[]
  editingMessageId: string | null
  editingContent: string
  hoveredMessageId: string | null
  messageMenuVisible: string | null
  copyFeedback: Record<string, boolean>
}

/**
 * 聊天界面状态
 */
export interface ChatInterfaceState {
  sidebarExpanded: boolean
  theme: 'light' | 'dark' | 'auto'
  messagesDensity: 'compact' | 'comfortable' | 'spacious'
  showDebugInfo: boolean
  isMobile: boolean
  scale: number
}

/**
 * 通知和反馈状态
 */
export interface NotificationState {
  notifications: Array<{
    id: string
    type: 'info' | 'success' | 'warning' | 'error'
    title: string
    message?: string
    duration?: number
    timestamp: number
  }>
  loadingStates: Record<string, {
    isLoading: boolean
    message?: string
    progress?: number
  }>
  errorStates: Record<string, {
    hasError: boolean
    errorMessage?: string
    canRetry?: boolean
  }>
}

/**
 * UI层状态 - 界面状态管理
 */
export interface ChatUIState {
  input: InputState
  messageList: MessageListUIState
  interaction: MessageInteractionState
  interface: ChatInterfaceState
  notifications: NotificationState
}

/**
 * 统一聊天状态 - 分层架构
 */
export interface UnifiedChatState {
  /** 数据层 - 消息数据存储 */
  data: ChatDataState
  
  /** 流程层 - 消息流处理 */
  flow: ChatFlowState
  
  /** UI层 - 界面状态管理 */
  ui: ChatUIState
}

/**
 * 发送消息选项
 */
export interface SendMessageOptions {
  messageType?: string
  metadata?: Record<string, any>
  priority?: 'low' | 'normal' | 'high'
  timeout?: number
}

// ============================================================================
// 统一操作接口
// ============================================================================

/**
 * 数据层操作
 */
export interface ChatDataActions {
  // 基础CRUD操作
  addMessage: (sessionId: string, message: ClientMessage) => void
  addMessages: (sessionId: string, messages: ClientMessage[]) => void
  updateMessage: (messageId: string, updates: Partial<ClientMessage>) => boolean
  deleteMessage: (messageId: string) => boolean
  clearSessionMessages: (sessionId: string) => void
  deleteSession: (sessionId: string) => void
  
  // 查询操作
  getSessionMessages: (sessionId: string) => ClientMessage[]
  getMessage: (messageId: string) => ClientMessage | undefined
  findMessages: (predicate: (message: ClientMessage) => boolean) => ClientMessage[]
  getSessionStats: (sessionId: string) => ChatDataState['sessionStats'][string] | undefined
  
  // 批量操作
  batchUpdateMessages: (updates: Array<{ messageId: string; updates: Partial<ClientMessage> }>) => number
  batchDeleteMessages: (messageIds: string[]) => number
  
  // 工具方法
  rebuildMessageIndex: () => void
  getDataDiagnostics: () => {
    totalSessions: number
    totalMessages: number
    indexSize: number
    memoryUsage: string
  }
}

/**
 * 流程层操作
 */
export interface ChatFlowActions {
  // 消息发送
  sendUserMessage: (content: string, sessionId?: string, options?: SendMessageOptions) => Promise<void>
  retryMessage: (tempId: string) => Promise<void>
  cancelMessage: (tempId: string) => void
  
  // 消息接收处理
  handleIncomingMessage: (message: ClientMessage) => void
  handleStreamingChunk: (messageId: string, chunk: string, isComplete?: boolean) => void
  completeStreamingMessage: (messageId: string) => void
  
  // 队列管理
  cleanupSentMessages: (sessionId?: string) => void
  cleanupStreamingMessages: (sessionId?: string) => void
  getPendingMessages: (sessionId?: string) => PendingSentMessage[]
  
  // 工具方法
  getFlowDiagnostics: () => {
    pendingCount: number
    streamingCount: number
    processingState: boolean
  }
}

/**
 * UI层操作
 */
export interface ChatUIActions {
  // 输入框操作
  updateInputContent: (content: string) => void
  setInputFocus: (focused: boolean) => void
  setTyping: (typing: boolean) => void
  adjustInputHeight: (height: number) => void
  clearInput: () => void
  
  // 消息列表UI操作
  updateScrollState: (state: Partial<MessageListUIState>) => void
  setVisibleRange: (start: number, end: number) => void
  setAutoScroll: (enabled: boolean) => void
  scrollToBottom: () => void
  
  // 消息交互操作
  selectMessage: (messageId: string, multiple?: boolean) => void
  deselectMessage: (messageId: string) => void
  clearSelection: () => void
  startEditMessage: (messageId: string, content: string) => void
  updateEditContent: (content: string) => void
  endEditMessage: () => void
  setHoveredMessage: (messageId: string | null) => void
  toggleMessageMenu: (messageId: string | null) => void
  setCopyFeedback: (messageId: string, success: boolean) => void
  
  // 界面状态操作
  toggleSidebar: () => void
  setTheme: (theme: ChatInterfaceState['theme']) => void
  setMessagesDensity: (density: ChatInterfaceState['messagesDensity']) => void
  toggleDebugInfo: () => void
  setMobileMode: (isMobile: boolean) => void
  setScale: (scale: number) => void
  
  // 通知和反馈操作
  addNotification: (notification: Omit<NotificationState['notifications'][0], 'id' | 'timestamp'>) => string
  removeNotification: (id: string) => void
  clearNotifications: () => void
  setLoadingState: (key: string, state: NotificationState['loadingStates'][string]) => void
  clearLoadingState: (key: string) => void
  setErrorState: (key: string, state: NotificationState['errorStates'][string]) => void
  clearErrorState: (key: string) => void
}

/**
 * 统一操作接口
 */
export type UnifiedChatActions = ChatDataActions & ChatFlowActions & ChatUIActions & {
  // 类型迁移工具 (已移除，不再需要兼容旧格式)
  migrateToBaseMessage: (newMessage: ClientMessage) => ClientMessage
  
  // 批量操作优化
  batchOperation: <T>(operations: (() => T)[]) => T[]
  
  // 诊断和调试
  getFullDiagnostics: () => {
    data: ReturnType<ChatDataActions['getDataDiagnostics']>
    flow: ReturnType<ChatFlowActions['getFlowDiagnostics']>
    ui: {
      inputState: boolean
      interactionState: number
      notificationCount: number
    }
  }
  
  // 资源清理
  cleanup: () => void
}

/**
 * 完整的统一Store类型
 */
export type UnifiedChatStore = UnifiedChatState & UnifiedChatActions

// ============================================================================
// Store 实现
// ============================================================================

export const useUnifiedChatStore = create<UnifiedChatStore>()(
  subscribeWithSelector(
    immer((set, get) => ({
      // ============================================================================
      // 初始状态
      // ============================================================================
      data: {
        messages: {},
        messageIndex: {},
        sessionStats: {},
      },
      
      flow: {
        pendingSentMessages: [],
        streamingMessages: {},
        isProcessing: false,
        retryConfig: {
          maxRetries: 3,
          retryDelay: 1000,
          backoffMultiplier: 2
        },
      },
      
      ui: {
        input: {
          content: '',
          isTyping: false,
          isFocused: false,
          height: 40,
          maxHeight: 200,
          placeholder: '输入消息...'
        },
        
        messageList: {
          isScrolling: false,
          isAtBottom: true,
          showScrollToBottom: false,
          autoScroll: true,
          visibleRange: { start: 0, end: 0 },
          containerHeight: 0
        },
        
        interaction: {
          selectedMessageIds: [],
          editingMessageId: null,
          editingContent: '',
          hoveredMessageId: null,
          messageMenuVisible: null,
          copyFeedback: {}
        },
        
        interface: {
          sidebarExpanded: false,
          theme: 'auto',
          messagesDensity: 'comfortable',
          showDebugInfo: process.env.NODE_ENV === 'development',
          isMobile: false,
          scale: 1.0
        },
        
        notifications: {
          notifications: [],
          loadingStates: {},
          errorStates: {}
        },
      },

      // ============================================================================
      // 数据层操作实现
      // ============================================================================

      addMessage: (sessionId: string, message: ClientMessage) => {
        set(state => {
          // 初始化会话
          if (!state.data.messages[sessionId]) {
            state.data.messages[sessionId] = []
            state.data.sessionStats[sessionId] = {
              totalMessages: 0,
              userMessages: 0,
              assistantMessages: 0,
              lastMessageTime: 0
            }
          }

          // 添加消息
          const messages = state.data.messages[sessionId]
          messages.push(message)

          // 更新索引
          state.data.messageIndex[message.id] = {
            sessionId,
            index: messages.length - 1
          }

          // 更新统计
          const stats = state.data.sessionStats[sessionId]
          stats.totalMessages++
          stats.lastMessageTime = message.timestamp
          
          if (message.userId) {
            stats.userMessages++
          } else {
            stats.assistantMessages++
          }
        })
      },

      addMessages: (sessionId: string, messages: ClientMessage[]) => {
        if (messages.length === 0) return

        set(state => {
          // 初始化会话
          if (!state.data.messages[sessionId]) {
            state.data.messages[sessionId] = []
            state.data.sessionStats[sessionId] = {
              totalMessages: 0,
              userMessages: 0,
              assistantMessages: 0,
              lastMessageTime: 0
            }
          }

          const sessionMessages = state.data.messages[sessionId]
          const stats = state.data.sessionStats[sessionId]
          let userCount = 0
          let lastTime = stats.lastMessageTime

          // 批量添加消息和索引
          messages.forEach((message) => {
            sessionMessages.push(message)
            state.data.messageIndex[message.id] = {
              sessionId,
              index: sessionMessages.length - 1
            }

            if (message.userId) {
              userCount++
            }

            if (message.timestamp > lastTime) {
              lastTime = message.timestamp
            }
          })

          // 批量更新统计
          stats.totalMessages += messages.length
          stats.userMessages += userCount
          stats.assistantMessages += (messages.length - userCount)
          stats.lastMessageTime = lastTime
        })
      },

      updateMessage: (messageId: string, updates: Partial<ClientMessage>) => {
        const { data } = get()
        const location = data.messageIndex[messageId]
        
        if (!location) {
          return false
        }

        set(state => {
          const message = state.data.messages[location.sessionId][location.index]
          Object.assign(message, updates)
          // 更新时间戳
          message.updatedAt = Date.now()
        })

        return true
      },

      deleteMessage: (messageId: string) => {
        const { data } = get()
        const location = data.messageIndex[messageId]
        
        if (!location) {
          return false
        }

        set(state => {
          const sessionMessages = state.data.messages[location.sessionId]
          const deletedMessage = sessionMessages[location.index]
          
          // 删除消息
          sessionMessages.splice(location.index, 1)
          
          // 删除索引
          delete state.data.messageIndex[messageId]
          
          // 重建该会话的索引
          sessionMessages.forEach((msg, index) => {
            state.data.messageIndex[msg.id] = {
              sessionId: location.sessionId,
              index
            }
          })

          // 更新统计
          const stats = state.data.sessionStats[location.sessionId]
          if (stats) {
            stats.totalMessages--
            if (deletedMessage.userId) {
              stats.userMessages--
            } else {
              stats.assistantMessages--
            }
          }
        })

        return true
      },

      clearSessionMessages: (sessionId: string) => {
        set(state => {
          const messages = state.data.messages[sessionId]
          if (!messages) return

          // 删除所有消息的索引
          messages.forEach(message => {
            delete state.data.messageIndex[message.id]
          })

          // 清空消息列表
          state.data.messages[sessionId] = []

          // 重置统计
          if (state.data.sessionStats[sessionId]) {
            state.data.sessionStats[sessionId] = {
              totalMessages: 0,
              userMessages: 0,
              assistantMessages: 0,
              lastMessageTime: 0
            }
          }
        })
      },

      deleteSession: (sessionId: string) => {
        set(state => {
          const messages = state.data.messages[sessionId]
          if (messages) {
            // 删除所有消息的索引
            messages.forEach(message => {
              delete state.data.messageIndex[message.id]
            })
          }

          // 删除会话数据
          delete state.data.messages[sessionId]
          delete state.data.sessionStats[sessionId]
        })
      },

      getSessionMessages: (sessionId: string) => {
        return get().data.messages[sessionId] || []
      },

      getMessage: (messageId: string) => {
        const { data } = get()
        const location = data.messageIndex[messageId]
        
        if (!location) {
          return undefined
        }

        return data.messages[location.sessionId]?.[location.index]
      },

      findMessages: (predicate: (message: ClientMessage) => boolean) => {
        const { data } = get()
        const results: ClientMessage[] = []

        Object.values(data.messages).forEach(sessionMessages => {
          sessionMessages.forEach(message => {
            if (predicate(message)) {
              results.push(message)
            }
          })
        })

        return results
      },

      getSessionStats: (sessionId: string) => {
        return get().data.sessionStats[sessionId]
      },

      batchUpdateMessages: (updates: Array<{ messageId: string; updates: Partial<ClientMessage> }>) => {
        let successCount = 0

        set(state => {
          updates.forEach(({ messageId, updates: messageUpdates }) => {
            const location = state.data.messageIndex[messageId]
            if (location) {
              const message = state.data.messages[location.sessionId][location.index]
              Object.assign(message, messageUpdates)
              message.updatedAt = Date.now()
              successCount++
            }
          })
        })

        return successCount
      },

      batchDeleteMessages: (messageIds: string[]) => {
        let successCount = 0

        // 按会话分组，提高删除效率
        const messagesBySession: Record<string, string[]> = {}
        
        messageIds.forEach(messageId => {
          const location = get().data.messageIndex[messageId]
          if (location) {
            if (!messagesBySession[location.sessionId]) {
              messagesBySession[location.sessionId] = []
            }
            messagesBySession[location.sessionId].push(messageId)
          }
        })

        // 按会话批量删除
        Object.entries(messagesBySession).forEach(([sessionId, sessionMessageIds]) => {
          sessionMessageIds.forEach(messageId => {
            if (get().deleteMessage(messageId)) {
              successCount++
            }
          })
        })

        return successCount
      },

      rebuildMessageIndex: () => {
        set(state => {
          state.data.messageIndex = {}
          
          Object.entries(state.data.messages).forEach(([sessionId, sessionMessages]) => {
            sessionMessages.forEach((message, index) => {
              state.data.messageIndex[message.id] = {
                sessionId,
                index
              }
            })
          })
        })
      },

      getDataDiagnostics: () => {
        const { data } = get()
        
        const totalSessions = Object.keys(data.messages).length
        const totalMessages = Object.values(data.messages).reduce(
          (sum, sessionMessages) => sum + sessionMessages.length, 
          0
        )
        const indexSize = Object.keys(data.messageIndex).length

        // 估算内存使用
        const memoryEstimate = (
          JSON.stringify(data.messages).length + 
          JSON.stringify(data.messageIndex).length +
          JSON.stringify(data.sessionStats).length
        ) / 1024 // KB

        return {
          totalSessions,
          totalMessages,
          indexSize,
          memoryUsage: `${memoryEstimate.toFixed(2)} KB`
        }
      },

      // ============================================================================
      // 流程层操作实现
      // ============================================================================

      sendUserMessage: async (content: string, sessionId?: string, options?: SendMessageOptions) => {
        const trimmedContent = content.trim()
        if (!trimmedContent) {
          throw new Error('消息内容不能为空')
        }

        // 获取当前会话
        const { useSessionStore } = await import('./session-store')
        const sessionStore = useSessionStore.getState()
        let targetSession = sessionStore.currentSession
        
        if (sessionId) {
          // 使用指定的会话ID
          targetSession = { 
            sessionId, 
            userId: targetSession?.userId || '', 
            groupChatId: '', 
            organizationId: targetSession?.organizationId || 'default-org',
            isActive: true,
            messages: [],
            createdAt: new Date(),
            updatedAt: new Date()
          }
        }
        
        if (!targetSession) {
          // 创建默认会话
          const sessionId = sessionStore.createSession('default-group', 'default-user', 'default-org')
          targetSession = sessionStore.getCurrentSession(sessionId)!
        }

        // 创建临时消息ID和用户消息
        const tempId = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        const now = Date.now()

        // 使用MessageFactory创建ClientMessage
        const userMessage: ClientMessage = {
          // 后端数据
          groupChatId: targetSession.groupChatId,
          sessionId: targetSession.sessionId,
          userId: targetSession.userId,
          organizationId: targetSession.organizationId,
          payload: {
            type: (options?.messageType as any) || 'user_message',
            content: trimmedContent
          },
          timestamp: now,
          
          // 前端状态
          id: tempId,
          status: 'pending',
          metadata: options?.metadata || {},
          createdAt: now,
          updatedAt: now
        }

        // 添加到发送队列
        set(state => {
          state.flow.pendingSentMessages.push({
            tempId,
            content: trimmedContent,
            timestamp: now,
            sessionId: targetSession.sessionId,
            userId: targetSession.userId,
            status: 'pending'
          })
        })

        try {
          // 处理消息（通过注册表）
          const { chatRegistry } = await import('@/lib/chat/chat-registry')
          const processedMessage = await chatRegistry.processMessage(userMessage as any, {
            sessionId: targetSession.sessionId,
            userId: targetSession.userId,
            timestamp: now
          })

          // 更新状态为发送中
          set(state => {
            const pending = state.flow.pendingSentMessages.find(p => p.tempId === tempId)
            if (pending) {
              pending.status = 'sending'
            }
          })

          // 发送到连接管理器
          const { useConnectionStore } = await import('./connection-store')
          const { connectionManager } = useConnectionStore.getState()
          if (!connectionManager) {
            throw new Error('连接管理器未初始化')
          }

          await connectionManager.sendMessage(processedMessage)

          // 发送成功，添加到消息存储
          get().addMessage(targetSession.sessionId, processedMessage as ClientMessage)

          // 更新状态为已发送
          set(state => {
            const pending = state.flow.pendingSentMessages.find(p => p.tempId === tempId)
            if (pending) {
              pending.status = 'sent'
            }
          })

          console.log('✅ 消息发送成功:', { messageId: tempId, sessionId: targetSession.sessionId })

        } catch (error) {
          console.error('❌ 消息发送失败:', error)
          
          // 更新状态为失败
          set(state => {
            const pending = state.flow.pendingSentMessages.find(p => p.tempId === tempId)
            if (pending) {
              pending.status = 'failed'
              pending.error = error instanceof Error ? error.message : '发送失败'
            }
          })

          throw error
        }
      },

      retryMessage: async (tempId: string) => {
        const pending = get().flow.pendingSentMessages.find(p => p.tempId === tempId)
        if (!pending || pending.status !== 'failed') {
          return
        }

        const { maxRetries, retryDelay, backoffMultiplier } = get().flow.retryConfig
        const currentRetryCount = pending.retryCount || 0

        if (currentRetryCount >= maxRetries) {
          console.warn(`⚠️ 消息重试次数已达上限: ${tempId}`)
          return
        }

        // 计算重试延迟
        const delay = retryDelay * Math.pow(backoffMultiplier, currentRetryCount)
        await new Promise(resolve => setTimeout(resolve, delay))

        set(state => {
          const pendingMessage = state.flow.pendingSentMessages.find(p => p.tempId === tempId)
          if (pendingMessage) {
            pendingMessage.retryCount = currentRetryCount + 1
            pendingMessage.status = 'pending'
            delete pendingMessage.error
          }
        })

        try {
          // 重新发送消息
          await get().sendUserMessage(pending.content, pending.sessionId)
        } catch (error) {
          console.error(`❌ 消息重试失败 (${currentRetryCount + 1}/${maxRetries}):`, error)
          
          set(state => {
            const pendingMessage = state.flow.pendingSentMessages.find(p => p.tempId === tempId)
            if (pendingMessage) {
              pendingMessage.status = 'failed'
              pendingMessage.error = error instanceof Error ? error.message : '重试失败'
            }
          })
        }
      },

      cancelMessage: (tempId: string) => {
        set(state => {
          const index = state.flow.pendingSentMessages.findIndex(p => p.tempId === tempId)
          if (index !== -1) {
            state.flow.pendingSentMessages.splice(index, 1)
          }
        })
      },

      handleIncomingMessage: (message: ClientMessage) => {
        set(state => {
          state.flow.isProcessing = true
        })

        try {
          // 直接使用ClientMessage，无需转换
          const clientMessage = message

          // 根据消息类型进行不同处理
          const messageType = clientMessage.payload?.type || 'default'

          switch (messageType) {
            case 'streaming':
              // 流式消息处理
              get().addMessage(clientMessage.sessionId || '', clientMessage)
              break
              
            default:
              // 常规消息直接添加到存储
              get().addMessage(clientMessage.sessionId || '', clientMessage)
              break
          }

          // 清理对应的发送中消息
          get().cleanupSentMessages()

        } catch (error) {
          console.error('❌ 处理传入消息失败:', error)
        } finally {
          set(state => {
            state.flow.isProcessing = false
          })
        }
      },

      handleStreamingChunk: (messageId: string, chunk: string, isComplete = false) => {
        set(state => {
          let streaming = state.flow.streamingMessages[messageId]
          
          if (!streaming) {
            // 初始化流式消息
            streaming = {
              messageId,
              sessionId: '', // 从消息中获取
              accumulatedContent: '',
              lastUpdateTime: Date.now(),
              isComplete: false,
              chunks: []
            }
            state.flow.streamingMessages[messageId] = streaming
          }

          // 添加新的块
          streaming.chunks.push({
            content: chunk,
            timestamp: Date.now(),
            chunkIndex: streaming.chunks.length
          })

          // 累积内容
          streaming.accumulatedContent += chunk
          streaming.lastUpdateTime = Date.now()
          streaming.isComplete = isComplete

          if (isComplete) {
            // 流式消息完成，创建最终消息并添加到存储
            const finalMessage: ClientMessage = {
              // 后端数据
              groupChatId: 'default-group',
              sessionId: streaming.sessionId,
              userId: 'assistant',
              organizationId: 'default-org',
              payload: {
                type: 'assistant_message',
                content: streaming.accumulatedContent
              },
              timestamp: streaming.lastUpdateTime,
              
              // 前端状态
              id: messageId,
              status: 'sent',
              metadata: {
                streamingState: {
                  isComplete: true,
                  accumulatedText: streaming.accumulatedContent
                }
              },
              createdAt: streaming.lastUpdateTime,
              updatedAt: streaming.lastUpdateTime
            }

            get().addMessage(streaming.sessionId, finalMessage)

            // 清理流式消息缓存
            delete state.flow.streamingMessages[messageId]
          }
        })
      },

      completeStreamingMessage: (messageId: string) => {
        get().handleStreamingChunk(messageId, '', true)
      },

      cleanupSentMessages: (sessionId?: string) => {
        set(state => {
          if (sessionId) {
            state.flow.pendingSentMessages = state.flow.pendingSentMessages.filter(
              pending => pending.sessionId !== sessionId || pending.status !== 'sent'
            )
          } else {
            state.flow.pendingSentMessages = state.flow.pendingSentMessages.filter(
              pending => pending.status !== 'sent'
            )
          }
        })
      },

      cleanupStreamingMessages: (sessionId?: string) => {
        set(state => {
          if (sessionId) {
            Object.entries(state.flow.streamingMessages).forEach(([messageId, streaming]) => {
              if (streaming.sessionId === sessionId) {
                delete state.flow.streamingMessages[messageId]
              }
            })
          } else {
            state.flow.streamingMessages = {}
          }
        })
      },

      getPendingMessages: (sessionId?: string) => {
        const { flow } = get()
        
        if (sessionId) {
          return flow.pendingSentMessages.filter(pending => pending.sessionId === sessionId)
        }
        
        return [...flow.pendingSentMessages]
      },

      getFlowDiagnostics: () => {
        const { flow } = get()
        
        return {
          pendingCount: flow.pendingSentMessages.length,
          streamingCount: Object.keys(flow.streamingMessages).length,
          processingState: flow.isProcessing
        }
      },

      // ============================================================================
      // UI层操作实现
      // ============================================================================

      updateInputContent: (content: string) => {
        set(state => {
          state.ui.input.content = content
          state.ui.input.isTyping = content.length > 0
        })
      },

      setInputFocus: (focused: boolean) => {
        set(state => {
          state.ui.input.isFocused = focused
        })
      },

      setTyping: (typing: boolean) => {
        set(state => {
          state.ui.input.isTyping = typing
        })
      },

      adjustInputHeight: (height: number) => {
        set(state => {
          state.ui.input.height = Math.min(height, state.ui.input.maxHeight)
        })
      },

      clearInput: () => {
        set(state => {
          state.ui.input.content = ''
          state.ui.input.isTyping = false
          state.ui.input.height = 40
        })
      },

      updateScrollState: (newState: Partial<MessageListUIState>) => {
        set(state => {
          Object.assign(state.ui.messageList, newState)
        })
      },

      setVisibleRange: (start: number, end: number) => {
        set(state => {
          state.ui.messageList.visibleRange = { start, end }
        })
      },

      setAutoScroll: (enabled: boolean) => {
        set(state => {
          state.ui.messageList.autoScroll = enabled
        })
      },

      scrollToBottom: () => {
        set(state => {
          state.ui.messageList.isAtBottom = true
          state.ui.messageList.showScrollToBottom = false
        })
      },

      selectMessage: (messageId: string, multiple = false) => {
        set(state => {
          if (multiple) {
            if (!state.ui.interaction.selectedMessageIds.includes(messageId)) {
              state.ui.interaction.selectedMessageIds.push(messageId)
            }
          } else {
            state.ui.interaction.selectedMessageIds = [messageId]
          }
        })
      },

      deselectMessage: (messageId: string) => {
        set(state => {
          state.ui.interaction.selectedMessageIds = state.ui.interaction.selectedMessageIds.filter(
            id => id !== messageId
          )
        })
      },

      clearSelection: () => {
        set(state => {
          state.ui.interaction.selectedMessageIds = []
        })
      },

      startEditMessage: (messageId: string, content: string) => {
        set(state => {
          state.ui.interaction.editingMessageId = messageId
          state.ui.interaction.editingContent = content
        })
      },

      updateEditContent: (content: string) => {
        set(state => {
          state.ui.interaction.editingContent = content
        })
      },

      endEditMessage: () => {
        set(state => {
          state.ui.interaction.editingMessageId = null
          state.ui.interaction.editingContent = ''
        })
      },

      setHoveredMessage: (messageId: string | null) => {
        set(state => {
          state.ui.interaction.hoveredMessageId = messageId
        })
      },

      toggleMessageMenu: (messageId: string | null) => {
        set(state => {
          state.ui.interaction.messageMenuVisible = 
            state.ui.interaction.messageMenuVisible === messageId ? null : messageId
        })
      },

      setCopyFeedback: (messageId: string, success: boolean) => {
        set(state => {
          state.ui.interaction.copyFeedback[messageId] = success
        })

        // 自动清除反馈状态 - 使用稳定的清理机制
        const timeoutId = setTimeout(() => {
          // 检查store是否仍然存在，避免内存泄漏
          try {
            const currentState = get()
            if (currentState && currentState.ui.interaction.copyFeedback[messageId] !== undefined) {
              set(state => {
                delete state.ui.interaction.copyFeedback[messageId]
              })
            }
          } catch (error) {
            // Store可能已被销毁，忽略错误
            if (process.env.NODE_ENV === 'development') {
              console.debug('setCopyFeedback cleanup failed:', error)
            }
          }
        }, 2000)

        // 将timeout ID存储到metadata中，便于清理
        set(state => {
          if (!state.ui.interaction.copyFeedback[`${messageId}_timeout`]) {
            state.ui.interaction.copyFeedback[`${messageId}_timeout`] = timeoutId as any
          }
        })
      },

      toggleSidebar: () => {
        set(state => {
          state.ui.interface.sidebarExpanded = !state.ui.interface.sidebarExpanded
        })
      },

      setTheme: (theme: ChatInterfaceState['theme']) => {
        set(state => {
          state.ui.interface.theme = theme
        })
      },

      setMessagesDensity: (density: ChatInterfaceState['messagesDensity']) => {
        set(state => {
          state.ui.interface.messagesDensity = density
        })
      },

      toggleDebugInfo: () => {
        set(state => {
          state.ui.interface.showDebugInfo = !state.ui.interface.showDebugInfo
        })
      },

      setMobileMode: (isMobile: boolean) => {
        set(state => {
          state.ui.interface.isMobile = isMobile
          
          // 移动端模式下自动调整界面
          if (isMobile) {
            state.ui.interface.sidebarExpanded = false
            state.ui.interface.messagesDensity = 'compact'
          }
        })
      },

      setScale: (scale: number) => {
        set(state => {
          state.ui.interface.scale = Math.max(0.5, Math.min(2.0, scale))
        })
      },

      addNotification: (notification) => {
        const id = `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        
        set(state => {
          state.ui.notifications.notifications.push({
            ...notification,
            id,
            timestamp: Date.now()
          })
        })

        // 自动移除通知 - 使用防御性清理机制
        if (notification.duration !== 0) {
          const duration = notification.duration || 5000
          const timeoutId = setTimeout(() => {
            try {
              const currentState = get()
              // 检查通知是否仍然存在
              const exists = currentState.ui.notifications.notifications.some(n => n.id === id)
              if (exists) {
                get().removeNotification(id)
              }
            } catch (error) {
              // Store可能已被销毁，忽略错误
              if (process.env.NODE_ENV === 'development') {
                console.debug('Auto notification removal failed:', error)
              }
            }
          }, duration)

          // 存储timeout ID以便后续清理
          set(state => {
            const notificationIndex = state.ui.notifications.notifications.findIndex(n => n.id === id)
            if (notificationIndex !== -1) {
              (state.ui.notifications.notifications[notificationIndex] as any).timeoutId = timeoutId
            }
          })
        }

        return id
      },

      removeNotification: (id: string) => {
        set(state => {
          // 清理相关的timeout
          const notification = state.ui.notifications.notifications.find(n => n.id === id)
          if (notification && (notification as any).timeoutId) {
            clearTimeout((notification as any).timeoutId)
          }
          
          // 移除通知
          state.ui.notifications.notifications = state.ui.notifications.notifications.filter(
            n => n.id !== id
          )
        })
      },

      clearNotifications: () => {
        set(state => {
          // 清理所有相关的timeout
          state.ui.notifications.notifications.forEach(notification => {
            if ((notification as any).timeoutId) {
              clearTimeout((notification as any).timeoutId)
            }
          })
          
          // 清空通知数组
          state.ui.notifications.notifications = []
        })
      },

      setLoadingState: (key: string, loadingState) => {
        set(state => {
          state.ui.notifications.loadingStates[key] = loadingState
        })
      },

      clearLoadingState: (key: string) => {
        set(state => {
          delete state.ui.notifications.loadingStates[key]
        })
      },

      setErrorState: (key: string, errorState) => {
        set(state => {
          state.ui.notifications.errorStates[key] = errorState
        })
      },

      clearErrorState: (key: string) => {
        set(state => {
          delete state.ui.notifications.errorStates[key]
        })
      },

      // ============================================================================
      // 类型迁移和工具方法
      // ============================================================================

      // migrateMessage 已移除，不再需要兼容旧格式

      migrateToBaseMessage: (newMessage: ClientMessage): ClientMessage => {
        // Return the same message since BaseWebSocketMessage is deprecated
        return newMessage
      },

      batchOperation: <T>(operations: (() => T)[]) => {
        const results: T[] = []
        
        // 批量执行操作，减少重渲染
        set(state => {
          operations.forEach(operation => {
            try {
              const result = operation()
              results.push(result)
            } catch (error) {
              console.error('批量操作中的错误:', error)
            }
          })
        })

        return results
      },

      getFullDiagnostics: () => {
        const store = get()
        
        return {
          data: store.getDataDiagnostics(),
          flow: store.getFlowDiagnostics(),
          ui: {
            inputState: store.ui.input.isFocused,
            interactionState: store.ui.interaction.selectedMessageIds.length,
            notificationCount: store.ui.notifications.notifications.length
          }
        }
      },

      // 清理所有的定时器和资源 - 组件卸载时调用
      cleanup: () => {
        set(state => {
          // 清理通知相关的timeout
          state.ui.notifications.notifications.forEach(notification => {
            if ((notification as any).timeoutId) {
              clearTimeout((notification as any).timeoutId)
            }
          })

          // 清理copyFeedback相关的timeout
          Object.keys(state.ui.interaction.copyFeedback).forEach(key => {
            if (key.endsWith('_timeout')) {
              const timeoutId = state.ui.interaction.copyFeedback[key]
              if (typeof timeoutId === 'number') {
                clearTimeout(timeoutId)
              }
              delete state.ui.interaction.copyFeedback[key]
            }
          })

          if (process.env.NODE_ENV === 'development') {
            console.log('🧹 UnifiedChatStore 清理完成')
          }
        })
      }
    }))
  )
)

// ============================================================================
// 向后兼容的Selector Hooks
// ============================================================================

/**
 * 数据层 Hooks - 保持与MessageDataStore相同的接口
 */

// Selector工厂函数 - 创建稳定的selector引用
const createSessionMessagesSelector = (sessionId: string) => {
  // 为空/默认sessionId提供稳定的空数组引用
  if (!sessionId || sessionId === 'empty-session' || sessionId === '') {
    return () => EMPTY_MESSAGES_ARRAY
  }
  
  return (state: UnifiedChatState) => state.data.messages[sessionId] || EMPTY_MESSAGES_ARRAY
}

/** 获取指定会话的消息（性能优化版） */
export const useSessionMessages = (sessionId: string) => {
  const selector = useMemo(
    () => createSessionMessagesSelector(sessionId),
    [sessionId]
  )
  return useUnifiedChatStore(selector)
}

// Selector工厂函数 - 会话统计
const createSessionStatsSelector = (sessionId: string) => {
  // 为空/默认sessionId提供稳定的空统计对象引用
  if (!sessionId || sessionId === 'empty-session' || sessionId === '') {
    return () => EMPTY_STATS_OBJECT
  }
  
  return (state: UnifiedChatState) => state.data.sessionStats[sessionId] || EMPTY_STATS_OBJECT
}

/** 获取会话统计信息 */
export const useSessionStats = (sessionId: string) => {
  const selector = useMemo(
    () => createSessionStatsSelector(sessionId),
    [sessionId]
  )
  return useUnifiedChatStore(selector)
}

// Selector工厂函数 - 单个消息
const createMessageSelector = (messageId: string) => {
  // 为空/无效messageId提供稳定的undefined引用
  if (!messageId || messageId === '') {
    return () => undefined
  }
  
  return (state: UnifiedChatState) => {
    const location = state.data.messageIndex[messageId]
    if (!location) return undefined
    return state.data.messages[location.sessionId]?.[location.index]
  }
}

/** 获取指定消息 */
export const useMessage = (messageId: string) => {
  const selector = useMemo(
    () => createMessageSelector(messageId),
    [messageId]
  )
  return useUnifiedChatStore(selector)
}

// 静态selector - 不依赖参数
const selectAllSessionIds = (state: UnifiedChatState) => Object.keys(state.data.messages)

/** 获取所有会话ID */
export const useAllSessionIds = () => {
  return useUnifiedChatStore(selectAllSessionIds)
}

// 静态selector - 消息总数
const selectTotalMessageCount = (state: UnifiedChatState) => 
  Object.values(state.data.sessionStats).reduce(
    (sum, stats) => sum + stats.totalMessages, 
    0
  )

/** 获取消息总数 */
export const useTotalMessageCount = () => {
  return useUnifiedChatStore(selectTotalMessageCount)
}

/**
 * 流程层 Hooks - 保持与MessageFlowStore相同的接口
 */

// Selector工厂函数 - 发送中消息
const createPendingMessagesSelector = (sessionId?: string) => {
  // 为空sessionId提供稳定的空数组引用
  if (!sessionId || sessionId === 'empty-session' || sessionId === '') {
    return (state: UnifiedChatState) => state.flow.pendingSentMessages.length === 0 
      ? EMPTY_PENDING_MESSAGES_ARRAY 
      : state.flow.pendingSentMessages
  }
  
  return (state: UnifiedChatState) => {
    const filtered = state.flow.pendingSentMessages.filter(pending => pending.sessionId === sessionId)
    return filtered.length === 0 ? EMPTY_PENDING_MESSAGES_ARRAY : filtered
  }
}

/** 获取指定会话的发送中消息 */
export const usePendingMessages = (sessionId?: string) => {
  const selector = useMemo(
    () => createPendingMessagesSelector(sessionId),
    [sessionId]
  )
  return useUnifiedChatStore(selector)
}

// 静态selector - 流式消息
const selectStreamingMessages = (state: UnifiedChatState) => state.flow.streamingMessages

/** 获取流式消息状态 */
export const useStreamingMessages = () => {
  return useUnifiedChatStore(selectStreamingMessages)
}

// 静态selector - 消息处理状态
const selectMessageProcessing = (state: UnifiedChatState) => state.flow.isProcessing

/** 获取消息处理状态 */
export const useMessageProcessing = () => {
  return useUnifiedChatStore(selectMessageProcessing)
}

// Selector工厂函数 - 单个流式消息
const createStreamingMessageSelector = (messageId: string) => {
  // 为空/无效messageId提供稳定的undefined引用
  if (!messageId || messageId === '') {
    return () => undefined
  }
  
  return (state: UnifiedChatState) => state.flow.streamingMessages[messageId]
}

/** 获取指定消息的流式状态 */
export const useStreamingMessage = (messageId: string) => {
  const selector = useMemo(
    () => createStreamingMessageSelector(messageId),
    [messageId]
  )
  return useUnifiedChatStore(selector)
}

/**
 * UI层 Hooks - 保持与MessageUIStore相同的接口
 */

// 静态selector - 输入框状态
const selectInputState = (state: UnifiedChatState) => state.ui.input

/** 输入框状态 */
export const useInputState = () => {
  return useUnifiedChatStore(selectInputState)
}

// 静态selector - 消息列表UI状态
const selectMessageListUI = (state: UnifiedChatState) => state.ui.messageList

/** 消息列表UI状态 */
export const useMessageListUI = () => {
  return useUnifiedChatStore(selectMessageListUI)
}

// 静态selector - 消息交互状态
const selectMessageInteraction = (state: UnifiedChatState) => state.ui.interaction

/** 消息交互状态 */
export const useMessageInteraction = () => {
  return useUnifiedChatStore(selectMessageInteraction)
}

// 静态selector - 界面设置
const selectChatInterface = (state: UnifiedChatState) => state.ui.interface

/** 界面设置 */
export const useChatInterface = () => {
  return useUnifiedChatStore(selectChatInterface)
}

// 静态selector - 通知状态
const selectNotifications = (state: UnifiedChatState) => state.ui.notifications

/** 通知状态 */
export const useNotifications = () => {
  return useUnifiedChatStore(selectNotifications)
}

// Selector工厂函数 - 消息交互状态
const createMessageInteractionStateSelector = (messageId: string) => {
  // 为空/无效messageId提供稳定的空交互状态引用
  if (!messageId || messageId === '') {
    return (state: UnifiedChatStore) => EMPTY_MESSAGE_INTERACTION_STATE
  }
  
  return (state: UnifiedChatStore) => {
    const copyFeedback = state.ui.interaction.copyFeedback[messageId]
    return {
      isSelected: state.ui.interaction.selectedMessageIds.includes(messageId),
      isEditing: state.ui.interaction.editingMessageId === messageId,
      isHovered: state.ui.interaction.hoveredMessageId === messageId,
      menuVisible: state.ui.interaction.messageMenuVisible === messageId,
      copyFeedback: copyFeedback
    }
  }
}

/** 特定消息的交互状态 */
export const useMessageInteractionState = (messageId: string) => {
  const selector = useMemo(
    () => createMessageInteractionStateSelector(messageId),
    [messageId]
  )
  return useUnifiedChatStore(selector)
}

// Selector工厂函数 - 加载状态
const createLoadingStateSelector = (key: string) => {
  // 为空/无效key提供稳定的undefined引用
  if (!key || key === '') {
    return () => undefined
  }
  
  return (state: UnifiedChatState) => state.ui.notifications.loadingStates[key]
}

/** 加载状态 */
export const useLoadingState = (key: string) => {
  const selector = useMemo(
    () => createLoadingStateSelector(key),
    [key]
  )
  return useUnifiedChatStore(selector)
}

// Selector工厂函数 - 错误状态
const createErrorStateSelector = (key: string) => {
  // 为空/无效key提供稳定的undefined引用
  if (!key || key === '') {
    return () => undefined
  }
  
  return (state: UnifiedChatState) => state.ui.notifications.errorStates[key]
}

/** 错误状态 */
export const useErrorState = (key: string) => {
  const selector = useMemo(
    () => createErrorStateSelector(key),
    [key]
  )
  return useUnifiedChatStore(selector)
}

// ============================================================================
// 新增统一Hooks
// ============================================================================

// 静态selector - 完整聊天状态
const selectChatState = (state: UnifiedChatState) => ({
  data: state.data,
  flow: state.flow,
  ui: state.ui
})

/** 获取完整的聊天状态 */
export const useChatState = () => {
  return useUnifiedChatStore(selectChatState)
}

// 静态selector - 聊天操作接口（需要完整的store状态）
const selectChatActions = (state: UnifiedChatStore) => ({
  // 数据操作
  addMessage: state.addMessage,
  updateMessage: state.updateMessage,
  deleteMessage: state.deleteMessage,
  
  // 流程操作
  sendUserMessage: state.sendUserMessage,
  handleIncomingMessage: state.handleIncomingMessage,
  
  // UI操作
  updateInputContent: state.updateInputContent,
  selectMessage: state.selectMessage,
  
  // 工具操作
  batchOperation: state.batchOperation
})

/** 获取聊天操作接口 */
export const useChatActions = () => {
  return useUnifiedChatStore(selectChatActions)
}

// 静态selector - 诊断信息（需要完整的store状态）
const selectChatDiagnostics = (state: UnifiedChatStore) => state.getFullDiagnostics()

/** 获取诊断信息 */
export const useChatDiagnostics = () => {
  return useUnifiedChatStore(selectChatDiagnostics)
}

// ============================================================================
// 向后兼容的Store Hooks - 用于替代旧的独立Store
// ============================================================================

/**
 * 向后兼容的MessageDataStore Hook
 * 提供与原 useMessageDataStore 相同的接口
 */

// 创建稳定的数据方法引用
const createStableDataActions = (() => {
  let cachedActions: any = null
  
  return (state: UnifiedChatStore) => {
    if (!cachedActions) {
      cachedActions = {
        getMessage: (messageId: string) => {
          const location = state.data.messageIndex[messageId]
          if (!location) return undefined
          return state.data.messages[location.sessionId]?.[location.index]
        },
        updateMessage: state.updateMessage,
        deleteMessage: state.deleteMessage,
        findMessages: (predicate: (message: ClientMessage) => boolean) => {
          const allMessages: ClientMessage[] = []
          Object.values(state.data.messages).forEach(sessionMessages => {
            allMessages.push(...sessionMessages)
          })
          return allMessages.filter(predicate)
        },
        getDiagnostics: () => ({
          totalMessages: Object.values(state.data.sessionStats).reduce(
            (sum, stats) => sum + stats.totalMessages, 0
          ),
          totalSessions: Object.keys(state.data.messages).length,
          memoryUsage: `${Object.keys(state.data.messageIndex).length} indexed messages`
        })
      }
    }
    return cachedActions
  }
})()

// 静态selector - 向后兼容的MessageDataStore
const selectMessageDataStore = (state: UnifiedChatStore) => {
  const stableActions = createStableDataActions(state)
  
  return {
    ...state.data,
    ...stableActions
  }
}

export const useMessageDataStore = (selector?: (state: ChatDataState & {
  getMessage: (messageId: string) => ClientMessage | undefined
  updateMessage: (messageId: string, updates: Partial<ClientMessage>) => boolean
  deleteMessage: (messageId: string) => boolean
  findMessages: (predicate: (message: ClientMessage) => boolean) => ClientMessage[]
  getDiagnostics: () => any
}) => any) => {
  const dataStore = useUnifiedChatStore(selectMessageDataStore)
  return selector ? selector(dataStore) : dataStore
}

// 为向后兼容添加getState方法
useMessageDataStore.getState = () => {
  const state = useUnifiedChatStore.getState()
  return selectMessageDataStore(state)
}

/**
 * 向后兼容的MessageFlowStore Hook
 * 提供与原 useMessageFlowStore 相同的接口
 */

// 创建稳定的流程方法引用
const createStableFlowActions = (() => {
  let cachedActions: any = null
  
  return (state: UnifiedChatStore) => {
    if (!cachedActions) {
      cachedActions = {
        sendUserMessage: state.sendUserMessage,
        retryMessage: async (messageId: string) => {
          // 重试逻辑的简化实现
          if (process.env.NODE_ENV === 'development') {
            console.warn('retryMessage 需要根据具体业务逻辑实现')
          }
        },
        getFlowDiagnostics: () => ({
          pendingCount: state.flow.pendingSentMessages.length,
          streamingCount: Object.keys(state.flow.streamingMessages).length,
          isProcessing: state.flow.isProcessing
        })
      }
    }
    return cachedActions
  }
})()

// 静态selector - 向后兼容的MessageFlowStore
const selectMessageFlowStore = (state: UnifiedChatStore) => {
  const stableActions = createStableFlowActions(state)
  
  return {
    ...state.flow,
    ...stableActions
  }
}

export const useMessageFlowStore = (selector?: (state: ChatFlowState & {
  sendUserMessage: (content: string, sessionId?: string, options?: any) => Promise<void>
  retryMessage: (messageId: string) => Promise<void>
  getFlowDiagnostics: () => any
}) => any) => {
  const flowStore = useUnifiedChatStore(selectMessageFlowStore)
  return selector ? selector(flowStore) : flowStore
}

// 为向后兼容添加getState方法
useMessageFlowStore.getState = () => {
  const state = useUnifiedChatStore.getState()
  return selectMessageFlowStore(state)
}

/**
 * 向后兼容的MessageUIStore Hook
 * 提供与原 useMessageUIStore 相同的接口
 */

// 创建稳定的方法引用 - 避免每次调用时创建新函数
const createStableUIActions = (() => {
  const debugFunctions = {
    setInputFocus: (focused: boolean) => {
      if (process.env.NODE_ENV === 'development') {
        console.debug('setInputFocus:', focused)
      }
    },
    clearSelection: () => {
      if (process.env.NODE_ENV === 'development') {
        console.debug('clearSelection called')
      }
    },
    startEditMessage: (messageId: string, content: string) => {
      if (process.env.NODE_ENV === 'development') {
        console.debug('startEditMessage:', messageId, content)
      }
    },
    endEditMessage: () => {
      if (process.env.NODE_ENV === 'development') {
        console.debug('endEditMessage called')
      }
    },
    setTheme: (theme: 'light' | 'dark' | 'auto') => {
      if (process.env.NODE_ENV === 'development') {
        console.debug('setTheme:', theme)
      }
    },
    setMessagesDensity: (density: 'compact' | 'comfortable' | 'spacious') => {
      if (process.env.NODE_ENV === 'development') {
        console.debug('setMessagesDensity:', density)
      }
    },
    toggleSidebar: () => {
      if (process.env.NODE_ENV === 'development') {
        console.debug('toggleSidebar called')
      }
    },
    addNotification: (notification: any) => {
      if (process.env.NODE_ENV === 'development') {
        console.debug('addNotification:', notification)
      }
    }
  }
  
  // 缓存函数引用
  let cachedActions: any = null
  
  return (state: UnifiedChatStore) => {
    if (!cachedActions) {
      cachedActions = {
        updateInputContent: state.updateInputContent,
        clearInput: () => state.updateInputContent(''),
        setInputFocus: debugFunctions.setInputFocus,
        selectMessage: state.selectMessage,
        clearSelection: debugFunctions.clearSelection,
        startEditMessage: debugFunctions.startEditMessage,
        endEditMessage: debugFunctions.endEditMessage,
        setTheme: debugFunctions.setTheme,
        setMessagesDensity: debugFunctions.setMessagesDensity,
        toggleSidebar: debugFunctions.toggleSidebar,
        addNotification: debugFunctions.addNotification
      }
    }
    return cachedActions
  }
})()

// 静态selector - 向后兼容的MessageUIStore
const selectMessageUIStore = (state: UnifiedChatStore) => {
  const stableActions = createStableUIActions(state)
  
  return {
    ...state.ui,
    ...stableActions
  }
}

export const useMessageUIStore = (selector?: (state: ChatUIState & {
  updateInputContent: (content: string) => void
  clearInput: () => void
  setInputFocus: (focused: boolean) => void
  selectMessage: (messageId: string, multiple?: boolean) => void
  clearSelection: () => void
  startEditMessage: (messageId: string, content: string) => void
  endEditMessage: () => void
  setTheme: (theme: 'light' | 'dark' | 'auto') => void
  setMessagesDensity: (density: 'compact' | 'comfortable' | 'spacious') => void
  toggleSidebar: () => void
  addNotification: (notification: any) => void
}) => any) => {
  const uiStore = useUnifiedChatStore(selectMessageUIStore)
  return selector ? selector(uiStore) : uiStore
}

// 为向后兼容添加getState方法
useMessageUIStore.getState = () => {
  const state = useUnifiedChatStore.getState()
  return selectMessageUIStore(state)
}