/**
 * Chat Error Boundary - 聊天系统专用错误边界组件
 * 
 * 🎯 设计目标：
 * 1. 处理聊天系统特有的错误类型
 * 2. 提供用户友好的错误展示
 * 3. 支持错误恢复和重试机制
 * 4. 集成错误报告和监控
 * 
 * ⚡ 核心特性：
 * - 错误类型分类处理
 * - 自动重试机制
 * - 用户操作指导
 * - 开发调试信息
 */

'use client'

import React, { Component, ErrorInfo, ReactNode } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'

// ============================================================================
// 类型定义
// ============================================================================

/**
 * 聊天错误类型
 */
export enum ChatErrorType {
  /** 连接错误 */
  CONNECTION = 'connection',
  /** 认证错误 */
  AUTHENTICATION = 'authentication',
  /** 数据同步错误 */
  DATA_SYNC = 'data_sync',
  /** Hydration错误 */
  HYDRATION = 'hydration',
  /** 组件渲染错误 */
  RENDER = 'render',
  /** 网络错误 */
  NETWORK = 'network',
  /** 未知错误 */
  UNKNOWN = 'unknown'
}

/**
 * 聊天错误信息
 */
export interface ChatError extends Error {
  type?: ChatErrorType
  code?: string
  context?: Record<string, any>
  recoverable?: boolean
  retryable?: boolean
}

/**
 * 错误边界状态
 */
interface ErrorBoundaryState {
  hasError: boolean
  error: ChatError | null
  errorInfo: ErrorInfo | null
  retryCount: number
  isRetrying: boolean
}

/**
 * 错误边界属性
 */
export interface ChatErrorBoundaryProps {
  children: ReactNode
  /** 错误回调 */
  onError?: (error: ChatError, errorInfo: ErrorInfo) => void
  /** 自定义错误展示组件 */
  fallback?: React.ComponentType<{
    error: ChatError
    errorInfo: ErrorInfo
    retry: () => void
    reset: () => void
  }>
  /** 最大重试次数 */
  maxRetries?: number
  /** 自动重试延迟（毫秒） */
  retryDelay?: number
  /** 是否启用自动重试 */
  enableAutoRetry?: boolean
  /** 是否显示详细错误信息 */
  showDetails?: boolean
}

// ============================================================================
// 错误分类函数
// ============================================================================

/**
 * 识别错误类型
 */
function classifyError(error: Error): ChatErrorType {
  const message = error.message.toLowerCase()
  const stack = error.stack?.toLowerCase() || ''

  if (message.includes('network') || message.includes('fetch')) {
    return ChatErrorType.NETWORK
  }
  
  if (message.includes('websocket') || message.includes('connection')) {
    return ChatErrorType.CONNECTION
  }
  
  if (message.includes('auth') || message.includes('unauthorized')) {
    return ChatErrorType.AUTHENTICATION
  }
  
  if (message.includes('hydration') || stack.includes('hydrate')) {
    return ChatErrorType.HYDRATION
  }
  
  if (message.includes('sync') || message.includes('data')) {
    return ChatErrorType.DATA_SYNC
  }
  
  if (stack.includes('render') || message.includes('render')) {
    return ChatErrorType.RENDER
  }

  return ChatErrorType.UNKNOWN
}

/**
 * 判断错误是否可重试
 */
function isRetryableError(error: ChatError): boolean {
  if (error.retryable !== undefined) {
    return error.retryable
  }

  const retryableTypes = [
    ChatErrorType.CONNECTION,
    ChatErrorType.NETWORK,
    ChatErrorType.DATA_SYNC,
    ChatErrorType.HYDRATION
  ]

  return retryableTypes.includes(error.type || ChatErrorType.UNKNOWN)
}

/**
 * 判断错误是否可恢复
 */
function isRecoverableError(error: ChatError): boolean {
  if (error.recoverable !== undefined) {
    return error.recoverable
  }

  const recoverableTypes = [
    ChatErrorType.CONNECTION,
    ChatErrorType.NETWORK,
    ChatErrorType.DATA_SYNC,
    ChatErrorType.HYDRATION,
    ChatErrorType.RENDER
  ]

  return recoverableTypes.includes(error.type || ChatErrorType.UNKNOWN)
}

// ============================================================================
// 错误边界组件
// ============================================================================

export class ChatErrorBoundary extends Component<
  ChatErrorBoundaryProps,
  ErrorBoundaryState
> {
  private retryTimer?: NodeJS.Timeout

  constructor(props: ChatErrorBoundaryProps) {
    super(props)
    
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
      isRetrying: false
    }
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    const chatError: ChatError = {
      ...error,
      type: classifyError(error),
      recoverable: isRecoverableError(error as ChatError),
      retryable: isRetryableError(error as ChatError)
    }

    return {
      hasError: true,
      error: chatError
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const chatError = this.state.error!
    
    this.setState({ errorInfo })
    
    // 调用错误回调
    this.props.onError?.(chatError, errorInfo)
    
    // 报告错误到监控系统
    this.reportError(chatError, errorInfo)
    
    // 自动重试
    if (this.props.enableAutoRetry && isRetryableError(chatError)) {
      this.scheduleRetry()
    }
  }

  componentWillUnmount() {
    if (this.retryTimer) {
      clearTimeout(this.retryTimer)
    }
  }

  /**
   * 报告错误到监控系统
   */
  private reportError = (error: ChatError, errorInfo: ErrorInfo) => {
    if (process.env.NODE_ENV === 'development') {
      console.group('🚨 Chat Error Boundary')
      console.error('Error:', error)
      console.error('Error Info:', errorInfo)
      console.error('Component Stack:', errorInfo.componentStack)
      console.groupEnd()
    }

    // TODO: 集成错误监控服务
    // 例如: Sentry, LogRocket, 或其他错误追踪服务
  }

  /**
   * 安排重试
   */
  private scheduleRetry = () => {
    const { maxRetries = 3, retryDelay = 2000 } = this.props
    const { retryCount } = this.state

    if (retryCount >= maxRetries) {
      return
    }

    this.setState({ isRetrying: true })

    this.retryTimer = setTimeout(() => {
      this.retry()
    }, retryDelay * Math.pow(2, retryCount)) // 指数退避
  }

  /**
   * 重试操作
   */
  private retry = () => {
    this.setState(prevState => ({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: prevState.retryCount + 1,
      isRetrying: false
    }))
  }

  /**
   * 重置错误状态
   */
  private reset = () => {
    if (this.retryTimer) {
      clearTimeout(this.retryTimer)
    }

    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
      isRetrying: false
    })
  }

  /**
   * 手动重试
   */
  private handleRetry = () => {
    if (this.state.isRetrying) return
    this.retry()
  }

  /**
   * 重新加载页面
   */
  private handleReload = () => {
    window.location.reload()
  }

  render() {
    const { children, fallback: CustomFallback, showDetails = process.env.NODE_ENV === 'development' } = this.props
    const { hasError, error, errorInfo, isRetrying } = this.state

    if (hasError && error) {
      // 使用自定义错误组件
      if (CustomFallback) {
        return (
          <CustomFallback
            error={error}
            errorInfo={errorInfo!}
            retry={this.handleRetry}
            reset={this.reset}
          />
        )
      }

      // 默认错误展示
      return (
        <div className="chat-error-boundary min-h-screen flex items-center justify-center p-4 bg-gray-50">
          <Card className="max-w-2xl w-full">
            <CardHeader>
              <CardTitle className="flex items-center space-x-2 text-red-600">
                <ErrorIcon />
                <span>聊天系统遇到错误</span>
              </CardTitle>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {/* 错误类型和描述 */}
              <Alert variant={error.type === ChatErrorType.NETWORK ? 'destructive' : 'default'}>
                <AlertTitle>
                  {this.getErrorTitle(error.type || ChatErrorType.UNKNOWN)}
                </AlertTitle>
                <AlertDescription>
                  {this.getErrorDescription(error.type || ChatErrorType.UNKNOWN)}
                </AlertDescription>
              </Alert>

              {/* 用户操作按钮 */}
              <div className="flex flex-wrap gap-3">
                {isRetryableError(error) && (
                  <Button 
                    onClick={this.handleRetry}
                    disabled={isRetrying}
                    className="flex items-center space-x-2"
                  >
                    {isRetrying ? (
                      <>
                        <LoadingIcon />
                        <span>重试中...</span>
                      </>
                    ) : (
                      <>
                        <RetryIcon />
                        <span>重试</span>
                      </>
                    )}
                  </Button>
                )}
                
                <Button 
                  variant="outline" 
                  onClick={this.handleReload}
                  className="flex items-center space-x-2"
                >
                  <ReloadIcon />
                  <span>刷新页面</span>
                </Button>
                
                <Button 
                  variant="outline" 
                  onClick={this.reset}
                  className="flex items-center space-x-2"
                >
                  <HomeIcon />
                  <span>返回首页</span>
                </Button>
              </div>

              {/* 详细错误信息（开发模式） */}
              {showDetails && (
                <details className="mt-6">
                  <summary className="cursor-pointer text-sm font-medium text-gray-600 hover:text-gray-800">
                    查看详细错误信息
                  </summary>
                  <div className="mt-3 p-4 bg-gray-100 rounded-lg">
                    <div className="space-y-2 text-sm">
                      <div>
                        <strong>错误类型:</strong> {error.type}
                      </div>
                      <div>
                        <strong>错误代码:</strong> {error.code || 'N/A'}
                      </div>
                      <div>
                        <strong>错误消息:</strong> {error.message}
                      </div>
                      {error.context && (
                        <div>
                          <strong>上下文:</strong>
                          <pre className="mt-1 text-xs bg-white p-2 rounded overflow-auto">
                            {JSON.stringify(error.context, null, 2)}
                          </pre>
                        </div>
                      )}
                      {errorInfo && (
                        <div>
                          <strong>组件堆栈:</strong>
                          <pre className="mt-1 text-xs bg-white p-2 rounded overflow-auto">
                            {errorInfo.componentStack}
                          </pre>
                        </div>
                      )}
                      {error.stack && (
                        <div>
                          <strong>错误堆栈:</strong>
                          <pre className="mt-1 text-xs bg-white p-2 rounded overflow-auto">
                            {error.stack}
                          </pre>
                        </div>
                      )}
                    </div>
                  </div>
                </details>
              )}
            </CardContent>
          </Card>
        </div>
      )
    }

    return children
  }

  /**
   * 获取错误标题
   */
  private getErrorTitle(type: ChatErrorType): string {
    switch (type) {
      case ChatErrorType.CONNECTION:
        return '连接失败'
      case ChatErrorType.AUTHENTICATION:
        return '身份验证失败'
      case ChatErrorType.DATA_SYNC:
        return '数据同步失败'
      case ChatErrorType.HYDRATION:
        return '页面初始化失败'
      case ChatErrorType.RENDER:
        return '页面渲染失败'
      case ChatErrorType.NETWORK:
        return '网络错误'
      default:
        return '系统错误'
    }
  }

  /**
   * 获取错误描述
   */
  private getErrorDescription(type: ChatErrorType): string {
    switch (type) {
      case ChatErrorType.CONNECTION:
        return '无法连接到聊天服务器，请检查网络连接后重试。'
      case ChatErrorType.AUTHENTICATION:
        return '身份验证失败，请重新登录后再试。'
      case ChatErrorType.DATA_SYNC:
        return '数据同步过程中出现问题，请刷新页面重试。'
      case ChatErrorType.HYDRATION:
        return '页面初始化过程中出现问题，请刷新页面重试。'
      case ChatErrorType.RENDER:
        return '页面渲染过程中出现问题，请刷新页面重试。'
      case ChatErrorType.NETWORK:
        return '网络请求失败，请检查网络连接后重试。'
      default:
        return '系统遇到未知错误，请联系技术支持。'
    }
  }
}

// ============================================================================
// 图标组件
// ============================================================================

const ErrorIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.732 15.5c-.77.833.192 2.5 1.732 2.5z" />
  </svg>
)

const RetryIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
  </svg>
)

const ReloadIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
  </svg>
)

const HomeIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
  </svg>
)

const LoadingIcon = () => (
  <svg className="w-4 h-4 animate-spin" fill="none" viewBox="0 0 24 24">
    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
  </svg>
)

// ============================================================================
// 高阶组件
// ============================================================================

/**
 * withChatErrorBoundary HOC
 */
export function withChatErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ChatErrorBoundaryProps, 'children'>
) {
  return function WrappedComponent(props: P) {
    return (
      <ChatErrorBoundary {...errorBoundaryProps}>
        <Component {...props} />
      </ChatErrorBoundary>
    )
  }
}

// ============================================================================
// 便捷函数
// ============================================================================

/**
 * 创建聊天错误
 */
export function createChatError(
  message: string,
  type: ChatErrorType = ChatErrorType.UNKNOWN,
  options?: {
    code?: string
    context?: Record<string, any>
    recoverable?: boolean
    retryable?: boolean
    cause?: Error
  }
): ChatError {
  const error = new Error(message) as ChatError
  error.type = type
  error.code = options?.code
  error.context = options?.context
  error.recoverable = options?.recoverable
  error.retryable = options?.retryable
  
  if (options?.cause) {
    error.cause = options.cause
    error.stack = options.cause.stack
  }

  return error
}

export default ChatErrorBoundary