/**
 * Chat Session Page - 动态聊天会话页面（App Router）
 * 
 * 🎯 设计目标：
 * 1. 支持动态sessionId路由参数
 * 2. 实现会话特定的SSR数据预加载
 * 3. SEO友好的元数据生成
 * 4. 错误处理和重定向逻辑
 * 
 * ⚡ 核心特性：
 * - 动态路由参数处理
 * - 会话数据验证和加载
 * - 适当的错误边界
 * - 性能优化的预加载
 */

import { Metadata } from 'next'
import { notFound, redirect } from 'next/navigation'
import { getTranslations } from 'next-intl/server'
import { Suspense } from 'react'

// 组件导入
import { SSRChatProvider } from '@/components/chat/ssr-chat-provider'
import { ChatHistoryLoader, ChatHistorySuspense } from '@/components/chat/chat-history-loader'
import { ProgressiveHydrationProvider } from '@/components/chat/progressive-hydration'
import { ChatInterface } from '@/components/ui/chat-interface'
import { ChatErrorBoundary } from '@/components/error/chat-error-boundary'

// 类型导入
import type { SSRChatInitialData } from '@/components/chat/ssr-chat-provider'
import type { ChatHistoryLoaderConfig } from '@/components/chat/chat-history-loader'

// ============================================================================
// 页面参数类型
// ============================================================================

interface ChatSessionPageProps {
  params: {
    locale: string
    sessionId: string
  }
  searchParams: {
    mode?: 'embed' | 'standalone'
    theme?: 'light' | 'dark' | 'auto'
    redirect_from?: string
  }
}

// ============================================================================
// 元数据生成
// ============================================================================

export async function generateMetadata({
  params,
  searchParams
}: ChatSessionPageProps): Promise<Metadata> {
  const { locale, sessionId } = params
  const t = await getTranslations({ locale, namespace: 'chat' })

  const isEmbed = searchParams.mode === 'embed'
  
  // 验证sessionId格式
  if (!isValidSessionId(sessionId)) {
    return {
      title: t('metadata.error.title'),
      description: t('metadata.error.description'),
      robots: 'noindex, nofollow'
    }
  }

  return {
    title: t('metadata.session.title', { sessionId: sessionId.slice(0, 8) }),
    description: t('metadata.session.description'),
    keywords: t('metadata.keywords'),
    robots: isEmbed ? 'noindex, nofollow' : 'index, follow',
    openGraph: {
      title: t('metadata.session.openGraph.title'),
      description: t('metadata.session.openGraph.description'),
      type: 'website',
      locale: locale,
    },
    twitter: {
      card: 'summary_large_image',
      title: t('metadata.session.twitter.title'),
      description: t('metadata.session.twitter.description'),
    },
    alternates: {
      canonical: `/chat/${sessionId}`,
      languages: {
        'en': `/en/chat/${sessionId}`,
        'ja': `/ja/chat/${sessionId}`,
        'zh': `/zh/chat/${sessionId}`,
      },
    },
  }
}

// ============================================================================
// 服务端数据获取
// ============================================================================

/**
 * 验证sessionId格式
 */
function isValidSessionId(sessionId: string): boolean {
  // 基本格式验证 - 可以根据实际需求调整
  const sessionIdPattern = /^[a-zA-Z0-9_-]{8,64}$/
  return sessionIdPattern.test(sessionId)
}

/**
 * 获取会话页面数据
 */
async function getChatSessionPageData(
  locale: string,
  sessionId: string
): Promise<{
  initialData: SSRChatInitialData
  historyConfig: ChatHistoryLoaderConfig
  sessionExists: boolean
}> {
  try {
    // 验证sessionId格式
    if (!isValidSessionId(sessionId)) {
      throw new Error(`Invalid session ID format: ${sessionId}`)
    }

    // 这里应该调用后端API验证会话是否存在
    // 使用 unified-http 或直接的数据库查询
    const sessionExists = await validateSessionExists(sessionId)
    
    if (!sessionExists) {
      throw new Error(`Session not found: ${sessionId}`)
    }

    // 获取会话历史数据
    const historyConfig: ChatHistoryLoaderConfig = {
      sessionId,
      limit: 100, // 为特定会话加载更多消息
      includeSystemMessages: true,
      cacheTTL: 300
    }

    // 预加载会话基本信息
    const sessionInfo = await getSessionInfo(sessionId)
    
    const initialData: SSRChatInitialData = {
      messages: [], // 会在 ChatHistoryLoader 中加载
      currentSession: sessionInfo,
      sessionStats: {
        totalMessages: 0,
        userMessages: 0,
        assistantMessages: 0,
        lastMessageTime: Date.now()
      },
      serverTimestamp: Date.now()
    }

    return { initialData, historyConfig, sessionExists }

  } catch (error) {
    console.error('❌ 获取聊天会话页面数据失败:', error)
    
    // 返回错误状态，让页面处理
    return {
      initialData: {
        messages: [],
        currentSession: null,
        serverTimestamp: Date.now()
      },
      historyConfig: {
        sessionId,
        limit: 0,
        includeSystemMessages: false,
        cacheTTL: 60
      },
      sessionExists: false
    }
  }
}

/**
 * 验证会话是否存在
 */
async function validateSessionExists(sessionId: string): Promise<boolean> {
  try {
    // 这里应该调用实际的API
    // const response = await fetch(`/api/chat/sessions/${sessionId}/validate`)
    // return response.ok
    
    // 模拟验证逻辑
    await new Promise(resolve => setTimeout(resolve, 100))
    
    // 简单的模拟验证 - 拒绝一些明显无效的ID
    if (sessionId.includes('invalid') || sessionId.length < 8) {
      return false
    }
    
    return true
  } catch (error) {
    console.error('验证会话存在性失败:', error)
    return false
  }
}

/**
 * 获取会话基本信息
 */
async function getSessionInfo(sessionId: string) {
  try {
    // 这里应该调用实际的API获取会话信息
    // const response = await fetch(`/api/chat/sessions/${sessionId}`)
    // return await response.json()
    
    // 模拟会话信息
    return {
      sessionId,
      userId: 'user-1',
      groupChatId: 'default-group',
      organizationId: 'default-org',
      isActive: true,
      messages: [],
      createdAt: new Date(Date.now() - 3600000), // 1小时前创建
      updatedAt: new Date()
    }
  } catch (error) {
    console.error('获取会话信息失败:', error)
    return null
  }
}

// ============================================================================
// 主页面组件
// ============================================================================

export default async function ChatSessionPage({
  params,
  searchParams
}: ChatSessionPageProps) {
  const { locale, sessionId } = params
  const { mode = 'standalone', theme = 'auto', redirect_from } = searchParams

  // 验证语言参数
  const supportedLocales = ['en', 'ja', 'zh']
  if (!supportedLocales.includes(locale)) {
    notFound()
  }

  // 获取翻译
  const t = await getTranslations({ locale, namespace: 'chat' })

  // 验证sessionId并获取数据
  const { initialData, historyConfig, sessionExists } = await getChatSessionPageData(locale, sessionId)

  // 如果会话不存在，重定向到主聊天页面
  if (!sessionExists) {
    console.log(`🔄 会话不存在，重定向: ${sessionId}`)
    redirect(`/${locale}/chat?error=session_not_found&sessionId=${sessionId}`)
  }

  // 页面设置
  const isEmbedMode = mode === 'embed'
  const pageClassNames = [
    'chat-session-page',
    `chat-session-page--${mode}`,
    `chat-session-page--theme-${theme}`,
    isEmbedMode && 'chat-session-page--embed'
  ].filter(Boolean).join(' ')

  return (
    <div className={pageClassNames}>
      <ChatErrorBoundary
        onError={(error, errorInfo) => {
          console.error('Chat session error:', error, errorInfo)
        }}
        showDetails={process.env.NODE_ENV === 'development'}
      >
        <ProgressiveHydrationProvider
          ssrData={initialData}
          config={{
            batchSize: 3,
            batchInterval: 50,
            enablePerformanceMonitoring: process.env.NODE_ENV === 'development'
          }}
        >
          <SSRChatProvider
            initialData={initialData}
            enableSSR={true}
            fallbackStrategy="skeleton"
            config={{
              autoConnect: !isEmbedMode,
              enableNotifications: true,
              maxRetries: 3
            }}
            onHydrationComplete={() => {
              console.log(`✅ 会话页面 hydration 完成: ${sessionId}`)
            }}
            onHydrationError={(error) => {
              console.error(`❌ 会话页面 hydration 失败: ${sessionId}`, error)
            }}
          >
            <ChatHistorySuspense fallback={<ChatSessionPageSkeleton />}>
              <ChatHistoryLoader
                config={historyConfig}
                onError={(error) => {
                  console.error(`❌ 会话历史加载失败: ${sessionId}`, error)
                }}
              >
                {(historyData) => (
                  <ChatSessionPageContent
                    sessionId={sessionId}
                    historyData={historyData}
                    locale={locale}
                    mode={mode}
                    theme={theme}
                    translations={t}
                    redirectFrom={redirect_from}
                  />
                )}
              </ChatHistoryLoader>
            </ChatHistorySuspense>
          </SSRChatProvider>
        </ProgressiveHydrationProvider>
      </ChatErrorBoundary>
    </div>
  )
}

// ============================================================================
// 页面内容组件
// ============================================================================

interface ChatSessionPageContentProps {
  sessionId: string;
  historyData: any; // ChatHistoryData type
  locale: string;
  mode: string;
  theme: string;
  translations: any;
  redirectFrom?: string;
}

function ChatSessionPageContent({
  sessionId,
  historyData,
  locale,
  mode,
  theme,
  translations,
  redirectFrom,
}: ChatSessionPageContentProps) {
  return (
    <main className="chat-session-page-content h-screen flex flex-col">
      {/* 页面头部（仅非嵌入模式） */}
      {mode !== 'embed' && (
        <header className="chat-session-page-header flex-shrink-0 border-b bg-white">
          <div className="container mx-auto px-4 py-3">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-xl font-semibold text-gray-900">
                  {translations('session.title')}
                </h1>
                <div className="flex items-center space-x-2 mt-1">
                  <span className="text-sm text-gray-500">
                    {translations('session.id')}: {sessionId.slice(0, 12)}...
                  </span>
                  {historyData.sessionStats && (
                    <span className="text-sm text-gray-400">
                      • {historyData.sessionStats.totalMessages} {translations('session.messages')}
                    </span>
                  )}
                </div>
              </div>
              
              {/* 操作按钮 */}
              <div className="flex items-center space-x-2">
                {redirectFrom && (
                  <span className="text-xs text-blue-600 bg-blue-50 px-2 py-1 rounded">
                    {translations('session.redirected_from')}: {redirectFrom}
                  </span>
                )}
              </div>
            </div>
          </div>
        </header>
      )}

      {/* 聊天界面 */}
      <div className="chat-session-page-main flex-1 min-h-0">
        <ChatInterface
          initialMessages={historyData.messages}
          sessionStats={historyData.sessionStats}
          mode={mode as 'embed' | 'standalone'}
          theme={theme as 'light' | 'dark' | 'auto'}
          locale={locale}
        />
      </div>
    </main>
  );
}

// ============================================================================
// 骨架屏组件
// ============================================================================

function ChatSessionPageSkeleton() {
  return (
    <div className="chat-session-page-skeleton h-screen flex flex-col animate-pulse">
      {/* 头部骨架屏 */}
      <div className="chat-session-page-header-skeleton border-b bg-white p-4">
        <div className="container mx-auto">
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="h-6 bg-gray-200 rounded w-48"></div>
              <div className="h-4 bg-gray-200 rounded w-72"></div>
            </div>
            <div className="w-20 h-8 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>

      {/* 聊天界面骨架屏 */}
      <div className="flex-1 p-4">
        <div className="max-w-4xl mx-auto h-full flex flex-col">
          {/* 消息列表骨架屏 */}
          <div className="flex-1 space-y-4 mb-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="flex space-x-3">
                <div className="w-8 h-8 bg-gray-200 rounded-full flex-shrink-0"></div>
                <div className="flex-1 space-y-2">
                  <div className="flex items-center space-x-2">
                    <div className="h-3 bg-gray-200 rounded w-16"></div>
                    <div className="h-3 bg-gray-200 rounded w-20"></div>
                  </div>
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                </div>
              </div>
            ))}
          </div>

          {/* 输入框骨架屏 */}
          <div className="border-t pt-4">
            <div className="flex space-x-2">
              <div className="flex-1 h-12 bg-gray-200 rounded"></div>
              <div className="w-12 h-12 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// ============================================================================
// 路由配置
// ============================================================================

export const runtime = 'nodejs'
export const revalidate = 0 // 禁用静态生成缓存，因为聊天是动态内容

// 静态参数生成（可选 - 用于常见的sessionId预生成）
export async function generateStaticParams() {
  // 可以返回一些常见的sessionId进行预生成
  // 但通常聊天会话都是动态的，所以这里返回空数组
  return []
}