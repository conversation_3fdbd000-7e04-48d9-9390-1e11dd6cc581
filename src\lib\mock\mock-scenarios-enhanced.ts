/**
 * 增强的Mock场景定义
 * 
 * 功能：
 * 1. 更丰富的预定义场景
 * 2. 支持复杂的业务流程
 * 3. 动态场景生成
 * 4. 智能场景推荐
 * 5. 场景模板和自定义场景
 * 
 * 设计原则：
 * - 向后兼容现有场景系统
 * - 支持复杂的多步骤工作流
 * - 提供丰富的业务场景模拟
 * - 支持动态参数和条件逻辑
 */

import { MockScenarios, ScenarioManager, type MockScenario } from './mock-scenarios'
import { MockDataFactory } from './mock-data-factory'
import type { BaseWebSocketMessage, FormField } from '@/types/websocket-event-type'

// ============================================================================
// 增强场景类型定义
// ============================================================================

/**
 * 场景步骤定义
 */
export interface ScenarioStep {
  id: string
  type: 'streaming' | 'checkpoint' | 'report' | 'error' | 'delay' | 'condition'
  delay: number
  data?: any
  condition?: (context: ScenarioContext) => boolean
  onExecute?: (context: ScenarioContext) => Promise<void>
}

/**
 * 场景上下文
 */
export interface ScenarioContext {
  sessionId: string
  userId: string
  organizationId: string
  variables: Record<string, any>
  stepHistory: string[]
  currentStep: number
  totalSteps: number
}

/**
 * 增强场景定义
 */
export interface EnhancedMockScenario extends MockScenario {
  /** 场景分类 */
  category: 'business' | 'technical' | 'workflow' | 'demo' | 'test'
  /** 场景标签 */
  tags: string[]
  /** 场景难度 */
  difficulty: 'easy' | 'medium' | 'hard'
  /** 预计执行时间（毫秒） */
  estimatedDuration: number
  /** 场景步骤 */
  steps?: ScenarioStep[]
  /** 动态数据生成器 */
  dataGenerator?: (context: ScenarioContext) => any
  /** 场景变量 */
  variables?: Record<string, any>
  /** 前置条件 */
  prerequisites?: string[]
  /** 成功指标 */
  successCriteria?: string[]
}

// ============================================================================
// 业务场景定义
// ============================================================================

/**
 * 数据分析工作流场景
 */
const DATA_ANALYSIS_WORKFLOW: EnhancedMockScenario = {
  id: 'data-analysis-workflow',
  name: '数据分析完整工作流',
  description: '模拟完整的数据分析流程：需求收集 → 数据准备 → 分析处理 → 结果展示',
  type: 'mixed',
  category: 'business',
  tags: ['数据分析', '工作流', '业务流程'],
  difficulty: 'medium',
  delayMs: 500,
  estimatedDuration: 45000,
  steps: [
    {
      id: 'welcome',
      type: 'streaming',
      delay: 0,
      data: {
        text: '🔍 欢迎使用数据分析系统！我将引导您完成完整的分析流程。首先，让我了解您的分析需求。'
      }
    },
    {
      id: 'requirements',
      type: 'checkpoint',
      delay: 2000,
      data: {
        title: '分析需求收集',
        description: '请填写以下信息，帮助我们为您定制分析方案',
        fields: [
          {
            id: 'analysis_type',
            label: '分析类型',
            type: 'select',
            required: true,
            options: [
              { label: '用户行为分析', value: 'user_behavior' },
              { label: '销售数据分析', value: 'sales_analysis' },
              { label: '市场趋势分析', value: 'market_trend' },
              { label: '风险评估分析', value: 'risk_assessment' }
            ]
          },
          {
            id: 'time_range',
            label: '分析时间范围',
            type: 'select',
            required: true,
            options: [
              { label: '最近7天', value: '7d' },
              { label: '最近30天', value: '30d' },
              { label: '最近90天', value: '90d' },
              { label: '自定义时间', value: 'custom' }
            ]
          },
          {
            id: 'detail_level',
            label: '详细程度',
            type: 'radio',
            required: true,
            options: [
              { label: '概览报告', value: 'overview' },
              { label: '详细分析', value: 'detailed' },
              { label: '深度挖掘', value: 'deep_dive' }
            ]
          },
          {
            id: 'output_format',
            label: '输出格式',
            type: 'checkbox',
            required: false,
            options: [
              { label: 'PDF报告', value: 'pdf' },
              { label: 'Excel数据', value: 'excel' },
              { label: '在线仪表板', value: 'dashboard' },
              { label: '邮件摘要', value: 'email' }
            ]
          }
        ]
      }
    },
    {
      id: 'processing',
      type: 'streaming',
      delay: 5000,
      data: {
        text: `📊 收到您的分析需求！正在准备数据分析流程...

🔄 数据收集中... (20%)
📈 数据清洗和预处理... (40%) 
🤖 应用机器学习模型... (60%)
📊 生成可视化图表... (80%)
✅ 分析完成，正在生成报告... (100%)`
      }
    },
    {
      id: 'results',
      type: 'report',
      delay: 15000,
      data: {
        title: '数据分析报告',
        content: `# 数据分析报告

## 执行摘要

本次分析已成功完成，基于您提供的需求参数，我们对相关数据进行了深入分析。

## 关键发现

### 📈 核心指标表现
- **用户活跃度**: 环比增长 +15.3%
- **转化率**: 4.2% (行业平均 3.8%)
- **用户留存率**: 73.5% (较上期提升 5.2%)
- **平均会话时长**: 8.5分钟 (+12.8%)

### 🎯 趋势洞察
1. **移动端使用增长**: 移动端用户占比达到 68%，较上期增长 8%
2. **核心功能受欢迎**: 数据分析功能使用率提升 22%
3. **用户满意度提升**: NPS评分从 7.2 提升至 8.4

### 🔍 细分分析

#### 用户行为模式
- **高价值用户群体**: 占用户总数 15%，贡献 45% 的价值
- **成长型用户**: 占比 35%，具有较高转化潜力
- **观望型用户**: 占比 50%，需要针对性激活策略

#### 地理分布特征
- **一线城市**: 用户活跃度最高，付费意愿强
- **二三线城市**: 增长最快的用户群体
- **海外市场**: 新兴增长点，需要本地化策略

## 风险识别

### ⚠️ 需要关注的问题
1. **用户流失风险**: 新用户 30 天留存率偏低
2. **季节性波动**: Q4 通常出现用户活跃度下降
3. **竞争压力**: 主要竞争对手推出新功能

### 🛡️ 风险缓解建议
- 优化新用户引导流程
- 制定季节性营销策略
- 加强产品创新和差异化

## 行动建议

### 短期优化 (1-2个月)
1. **提升用户体验**
   - 优化移动端界面响应速度
   - 简化核心功能操作流程
   - 增强个性化推荐算法

2. **激活潜在用户**
   - 针对观望型用户推出体验活动
   - 设计阶梯式激励机制
   - 加强社交化功能

### 中期规划 (3-6个月)
1. **功能升级**
   - 基于用户反馈优化核心功能
   - 推出AI辅助分析工具
   - 增强数据可视化能力

2. **市场拓展**
   - 深入二三线城市市场
   - 探索海外市场机会
   - 建立合作伙伴生态

### 长期战略 (6-12个月)
1. **平台化发展**
   - 构建开放式数据分析平台
   - 引入第三方服务和插件
   - 建立行业解决方案

2. **技术创新**
   - 投入实时数据处理能力
   - 提升机器学习算法精度
   - 探索前沿技术应用

## 结论

分析结果显示业务发展态势良好，关键指标均超出预期。建议继续保持当前发展方向，同时关注用户体验优化和市场拓展机会。

---
**报告生成时间**: ${new Date().toLocaleString()}  
**数据更新时间**: ${new Date().toLocaleString()}  
**分析模型版本**: v2.3.1`
      }
    },
    {
      id: 'follow_up',
      type: 'streaming',
      delay: 20000,
      data: {
        text: '✅ 分析报告已生成完成！您可以根据报告中的建议制定下一步行动计划。如果需要更深入的分析或有其他问题，请随时告诉我。'
      }
    }
  ],
  variables: {
    analysisType: 'user_behavior',
    timeRange: '30d',
    detailLevel: 'detailed'
  },
  prerequisites: ['用户已登录', '有足够的数据权限'],
  successCriteria: ['用户完成需求表单', '生成分析报告', '用户确认满意度']
}

/**
 * 客户服务对话场景
 */
const CUSTOMER_SERVICE_WORKFLOW: EnhancedMockScenario = {
  id: 'customer-service-workflow',
  name: '智能客服对话流程',
  description: '模拟完整的客服对话：问题识别 → 解决方案推荐 → 问题处理 → 满意度评价',
  type: 'mixed',
  category: 'business',
  tags: ['客服', '对话', '问题解决'],
  difficulty: 'easy',
  delayMs: 300,
  estimatedDuration: 25000,
  steps: [
    {
      id: 'greeting',
      type: 'streaming',
      delay: 0,
      data: {
        text: '👋 您好！我是智能客服助手，很高兴为您服务。请描述您遇到的问题，我会尽快为您解决。'
      }
    },
    {
      id: 'problem_analysis',
      type: 'streaming',
      delay: 3000,
      data: {
        text: '🔍 我正在分析您的问题... 根据您的描述，这可能是一个常见的技术问题。让我为您提供几种解决方案。'
      }
    },
    {
      id: 'solution_options',
      type: 'checkpoint',
      delay: 6000,
      data: {
        title: '解决方案选择',
        description: '请选择您希望尝试的解决方案',
        fields: [
          {
            id: 'solution_type',
            label: '解决方案',
            type: 'radio',
            required: true,
            options: [
              { label: '自助解决（推荐）', value: 'self_service' },
              { label: '技术支持协助', value: 'tech_support' },
              { label: '远程协助', value: 'remote_help' },
              { label: '预约现场服务', value: 'onsite_service' }
            ]
          },
          {
            id: 'urgency',
            label: '紧急程度',
            type: 'select',
            required: true,
            options: [
              { label: '非常紧急', value: 'critical' },
              { label: '比较紧急', value: 'high' },
              { label: '一般', value: 'medium' },
              { label: '不紧急', value: 'low' }
            ]
          },
          {
            id: 'contact_method',
            label: '联系方式',
            type: 'checkbox',
            required: false,
            options: [
              { label: '电话回访', value: 'phone' },
              { label: '邮件通知', value: 'email' },
              { label: '短信提醒', value: 'sms' },
              { label: '系统消息', value: 'system' }
            ]
          }
        ]
      }
    },
    {
      id: 'processing_solution',
      type: 'streaming',
      delay: 10000,
      data: {
        text: `⚙️ 正在为您处理解决方案...

✅ 已创建服务工单 #CS-2024-001
📞 技术专家已分配，预计 5 分钟内与您联系
📧 解决方案文档已发送至您的邮箱
🕐 预计处理时间：15-30 分钟

请保持手机畅通，我们的技术专家会尽快与您联系。`
      }
    },
    {
      id: 'satisfaction_survey',
      type: 'checkpoint',
      delay: 15000,
      data: {
        title: '服务满意度评价',
        description: '感谢您使用我们的客服服务，请为本次服务体验评分',
        fields: [
          {
            id: 'overall_satisfaction',
            label: '整体满意度',
            type: 'radio',
            required: true,
            options: [
              { label: '非常满意 ⭐⭐⭐⭐⭐', value: 5 },
              { label: '满意 ⭐⭐⭐⭐', value: 4 },
              { label: '一般 ⭐⭐⭐', value: 3 },
              { label: '不满意 ⭐⭐', value: 2 },
              { label: '非常不满意 ⭐', value: 1 }
            ]
          },
          {
            id: 'service_aspects',
            label: '服务优点（多选）',
            type: 'checkbox',
            required: false,
            options: [
              { label: '响应速度快', value: 'fast_response' },
              { label: '解决方案有效', value: 'effective_solution' },
              { label: '服务态度好', value: 'good_attitude' },
              { label: '专业性强', value: 'professional' }
            ]
          },
          {
            id: 'improvement_suggestions',
            label: '改进建议',
            type: 'textarea',
            required: false,
            placeholder: '请分享您的建议，帮助我们提供更好的服务...'
          }
        ]
      }
    },
    {
      id: 'thank_you',
      type: 'streaming',
      delay: 20000,
      data: {
        text: '🙏 感谢您的反馈！您的评价对我们非常重要。如果后续还有任何问题，请随时联系我们。祝您使用愉快！'
      }
    }
  ]
}

/**
 * 产品演示场景
 */
const PRODUCT_DEMO_SCENARIO: EnhancedMockScenario = {
  id: 'product-demo-interactive',
  name: '互动产品演示',
  description: '全面展示产品功能特性，包含实时演示和用户互动体验',
  type: 'mixed',
  category: 'demo',
  tags: ['产品演示', '功能介绍', '用户体验'],
  difficulty: 'medium',
  delayMs: 400,
  estimatedDuration: 60000,
  steps: [
    {
      id: 'welcome_demo',
      type: 'streaming',
      delay: 0,
      data: {
        text: `🎉 欢迎参加产品演示！

我将为您全面介绍我们的AI数据分析平台。这个演示大约需要5分钟，您可以随时打断我提问。

让我们开始吧！`
      }
    },
    {
      id: 'feature_overview',
      type: 'streaming',
      delay: 3000,
      data: {
        text: `📋 **产品核心功能概览**

🔍 **智能数据分析**
- 自动数据清洗和预处理
- 机器学习驱动的模式识别
- 实时数据流处理

📊 **可视化展示**
- 20+ 种图表类型
- 交互式仪表板
- 自定义报告生成

🤖 **AI辅助决策**
- 智能洞察推荐
- 预测性分析
- 异常检测和预警

现在让我为您演示第一个功能...`
      }
    },
    {
      id: 'demo_interaction',
      type: 'checkpoint',
      delay: 8000,
      data: {
        title: '演示偏好设置',
        description: '为了提供更个性化的演示体验，请告诉我您最感兴趣的功能',
        fields: [
          {
            id: 'interested_features',
            label: '感兴趣的功能（多选）',
            type: 'checkbox',
            required: true,
            options: [
              { label: '数据导入和连接', value: 'data_import' },
              { label: '自动化分析', value: 'auto_analysis' },
              { label: '可视化图表', value: 'visualization' },
              { label: 'AI智能洞察', value: 'ai_insights' },
              { label: '报告生成', value: 'reporting' },
              { label: '团队协作', value: 'collaboration' }
            ]
          },
          {
            id: 'industry',
            label: '您的行业领域',
            type: 'select',
            required: false,
            options: [
              { label: '零售电商', value: 'retail' },
              { label: '金融服务', value: 'finance' },
              { label: '制造业', value: 'manufacturing' },
              { label: '互联网科技', value: 'tech' },
              { label: '教育培训', value: 'education' },
              { label: '其他', value: 'other' }
            ]
          },
          {
            id: 'team_size',
            label: '团队规模',
            type: 'radio',
            required: false,
            options: [
              { label: '个人用户', value: 'individual' },
              { label: '小团队（2-10人）', value: 'small' },
              { label: '中型团队（11-50人）', value: 'medium' },
              { label: '大型团队（50+人）', value: 'large' }
            ]
          }
        ]
      }
    },
    {
      id: 'customized_demo',
      type: 'streaming',
      delay: 15000,
      data: {
        text: `🎯 **根据您的偏好定制演示**

基于您选择的功能，我将重点展示：

## 📊 数据可视化功能
正在加载演示数据...

\`\`\`javascript
// 示例：创建交互式图表
const chart = new DataViz({
  type: 'line',
  data: salesData,
  options: {
    responsive: true,
    animation: true
  }
})
\`\`\`

✨ **实时演示效果**：
- 数据点动态加载 ●●●●○
- 智能趋势线生成 📈
- 异常值自动标记 🔴
- 交互式筛选器激活 🎛️

## 🤖 AI洞察生成
正在分析数据模式...

**发现的关键洞察**：
1. 📈 销售额环比增长 23.5%
2. 🎯 转化率最高的渠道：移动端
3. ⚠️ 库存预警：3个产品需要补货
4. 🔮 预测：下月销售额可能增长 15%

这些洞察都是AI自动生成的，无需手动分析！`
      }
    },
    {
      id: 'interactive_features',
      type: 'streaming',
      delay: 25000,
      data: {
        text: `🎛️ **互动功能体验**

现在您可以亲自体验一些功能：

### 🔍 智能搜索
尝试问我："过去30天的销售趋势如何？"

### 📊 图表定制
您可以说："生成一个饼图显示产品分类占比"

### 📈 预测分析
询问："预测下个季度的业绩表现"

### 🎨 主题切换
支持浅色/深色模式，以及多种颜色主题

**现在就试试问我一个问题吧！** 👆`
      }
    },
    {
      id: 'pricing_info',
      type: 'streaming',
      delay: 35000,
      data: {
        text: `💰 **定价方案**

我们提供灵活的定价方案：

### 🆓 免费版
- 最多 1,000 行数据
- 基础图表类型
- 社区支持

### 💼 专业版 - ¥299/月
- 无限数据行数
- 高级AI功能
- 优先技术支持
- 团队协作功能

### 🏢 企业版 - 定制价格
- 私有化部署
- 定制开发
- 专属客户经理
- SLA服务保障

**现在注册可享受30天免费试用！** 🎁`
      }
    },
    {
      id: 'demo_feedback',
      type: 'checkpoint',
      delay: 45000,
      data: {
        title: '演示反馈收集',
        description: '感谢您观看我们的产品演示！请分享您的想法',
        fields: [
          {
            id: 'demo_rating',
            label: '演示评分',
            type: 'radio',
            required: true,
            options: [
              { label: '非常好 ⭐⭐⭐⭐⭐', value: 5 },
              { label: '很好 ⭐⭐⭐⭐', value: 4 },
              { label: '一般 ⭐⭐⭐', value: 3 },
              { label: '需要改进 ⭐⭐', value: 2 },
              { label: '不满意 ⭐', value: 1 }
            ]
          },
          {
            id: 'most_impressive',
            label: '最印象深刻的功能',
            type: 'select',
            required: false,
            options: [
              { label: 'AI智能分析', value: 'ai_analysis' },
              { label: '可视化效果', value: 'visualization' },
              { label: '操作简便性', value: 'ease_of_use' },
              { label: '定制化程度', value: 'customization' },
              { label: '性能表现', value: 'performance' }
            ]
          },
          {
            id: 'next_step',
            label: '您希望的下一步',
            type: 'radio',
            required: false,
            options: [
              { label: '立即开始免费试用', value: 'start_trial' },
              { label: '预约详细商务洽谈', value: 'schedule_meeting' },
              { label: '获取更多技术资料', value: 'get_materials' },
              { label: '暂时考虑', value: 'consider_later' }
            ]
          },
          {
            id: 'additional_questions',
            label: '其他问题或建议',
            type: 'textarea',
            required: false,
            placeholder: '请分享您的任何问题或建议...'
          }
        ]
      }
    },
    {
      id: 'demo_conclusion',
      type: 'streaming',
      delay: 55000,
      data: {
        text: `🎉 **演示结束**

感谢您的参与！希望这次演示让您对我们的产品有了全面了解。

**接下来的步骤**：
1. 📧 我们会将演示材料发送到您的邮箱
2. 🎁 免费试用账户已为您准备就绪
3. 👥 专业顾问将在24小时内与您联系

如果您现在就有任何问题，请随时告诉我！

**联系方式**：
📧 <EMAIL>  
📞 400-123-4567  
💬 在线客服：工作日 9:00-18:00

再次感谢您的时间！ 🙏`
      }
    }
  ]
}

// ============================================================================
// 技术测试场景
// ============================================================================

/**
 * 系统压力测试场景
 */
const STRESS_TEST_SCENARIO: EnhancedMockScenario = {
  id: 'system-stress-test',
  name: '系统压力测试',
  description: '模拟高并发消息处理，测试系统稳定性和性能表现',
  type: 'mixed',
  category: 'technical',
  tags: ['压力测试', '性能', '稳定性'],
  difficulty: 'hard',
  delayMs: 50,
  estimatedDuration: 30000,
  steps: [
    {
      id: 'test_start',
      type: 'streaming',
      delay: 0,
      data: {
        text: '🧪 开始系统压力测试...\n\n⚠️  此测试将产生大量消息，用于验证系统处理能力。'
      }
    },
    {
      id: 'burst_messages',
      type: 'streaming',
      delay: 1000,
      data: {
        generateBurstMessages: true,
        messageCount: 20,
        burstInterval: 100
      }
    },
    {
      id: 'test_results',
      type: 'report',
      delay: 10000,
      data: {
        title: '压力测试报告',
        content: `# 系统压力测试报告

## 测试配置
- **测试时间**: ${new Date().toLocaleString()}
- **消息数量**: 50条
- **发送频率**: 10条/秒
- **测试持续时间**: 5秒

## 性能指标
- **消息处理成功率**: 100%
- **平均响应时间**: 85ms
- **最大响应时间**: 150ms
- **内存峰值使用**: 12.3MB
- **CPU峰值使用**: 45%

## 结论
✅ 系统在高并发场景下表现稳定，所有消息均正确处理。`
      }
    }
  ]
}

// ============================================================================
// 增强场景集合
// ============================================================================

export const EnhancedMockScenarios: Record<string, EnhancedMockScenario> = {
  // 继承原有场景（转换为增强格式）
  ...Object.fromEntries(
    Object.entries(MockScenarios).map(([key, scenario]) => [
      key,
      {
        ...scenario,
        category: 'demo' as const,
        tags: [scenario.type],
        difficulty: 'easy' as const,
        estimatedDuration: scenario.delayMs * 10,
      } as EnhancedMockScenario
    ])
  ),

  // 新增增强场景
  'data-analysis-workflow': DATA_ANALYSIS_WORKFLOW,
  'customer-service-workflow': CUSTOMER_SERVICE_WORKFLOW,
  'product-demo-interactive': PRODUCT_DEMO_SCENARIO,
  'system-stress-test': STRESS_TEST_SCENARIO,

  // 更多业务场景
  'onboarding-flow': {
    id: 'onboarding-flow',
    name: '用户引导流程',
    description: '新用户完整引导体验，包含功能介绍和初始设置',
    type: 'mixed',
    category: 'workflow',
    tags: ['用户引导', '新手教程', '功能介绍'],
    difficulty: 'easy',
    delayMs: 800,
    estimatedDuration: 35000,
    steps: [
      {
        id: 'welcome',
        type: 'streaming',
        delay: 0,
        data: {
          text: '🎉 欢迎加入我们！让我为您介绍平台的主要功能，这只需要2分钟。'
        }
      },
      {
        id: 'profile_setup',
        type: 'checkpoint',
        delay: 3000,
        data: {
          title: '个人资料设置',
          description: '完善您的个人信息，获得更好的使用体验',
          fields: [
            {
              id: 'display_name',
              label: '显示名称',
              type: 'text',
              required: true,
              placeholder: '请输入您希望显示的名称'
            },
            {
              id: 'role',
              label: '职业角色',
              type: 'select',
              required: true,
              options: [
                { label: '数据分析师', value: 'analyst' },
                { label: '产品经理', value: 'pm' },
                { label: '运营专员', value: 'operations' },
                { label: '管理层', value: 'management' },
                { label: '其他', value: 'other' }
              ]
            }
          ]
        }
      }
    ]
  },

  'error-recovery': {
    id: 'error-recovery',
    name: '错误恢复测试',
    description: '模拟系统错误和恢复流程，测试错误处理能力',
    type: 'mixed',
    category: 'technical',
    tags: ['错误处理', '系统恢复', '异常测试'],
    difficulty: 'medium',
    delayMs: 200,
    estimatedDuration: 20000,
    steps: [
      {
        id: 'normal_operation',
        type: 'streaming',
        delay: 0,
        data: {
          text: '✅ 系统运行正常...'
        }
      },
      {
        id: 'simulate_error',
        type: 'error',
        delay: 2000,
        data: {
          code: 'SYSTEM_ERROR',
          message: '模拟系统异常，正在尝试恢复...'
        }
      },
      {
        id: 'recovery_process',
        type: 'streaming',
        delay: 5000,
        data: {
          text: '🔄 系统自动恢复中...\n\n✅ 恢复完成！所有功能已正常运行。'
        }
      }
    ]
  }
}

// ============================================================================
// 增强场景管理器
// ============================================================================

export class EnhancedScenarioManager extends ScenarioManager {
  /**
   * 获取所有增强场景
   */
  static getAllEnhancedScenarios(): EnhancedMockScenario[] {
    return Object.values(EnhancedMockScenarios)
  }

  /**
   * 根据分类获取场景
   */
  static getScenariosByCategory(category: EnhancedMockScenario['category']): EnhancedMockScenario[] {
    return Object.values(EnhancedMockScenarios).filter(scenario => scenario.category === category)
  }

  /**
   * 根据标签获取场景
   */
  static getScenariosByTag(tag: string): EnhancedMockScenario[] {
    return Object.values(EnhancedMockScenarios).filter(scenario => 
      scenario.tags?.includes(tag)
    )
  }

  /**
   * 根据难度获取场景
   */
  static getScenariosByDifficulty(difficulty: EnhancedMockScenario['difficulty']): EnhancedMockScenario[] {
    return Object.values(EnhancedMockScenarios).filter(scenario => scenario.difficulty === difficulty)
  }

  /**
   * 获取推荐场景
   */
  static getRecommendedScenarios(userProfile?: any): EnhancedMockScenario[] {
    const scenarios = this.getAllEnhancedScenarios()
    
    // 简单的推荐逻辑
    if (userProfile?.role === 'developer') {
      return scenarios.filter(s => s.category === 'technical' || s.tags?.includes('测试'))
    }
    
    if (userProfile?.role === 'business') {
      return scenarios.filter(s => s.category === 'business' || s.category === 'workflow')
    }
    
    // 默认推荐
    return scenarios.filter(s => s.difficulty === 'easy').slice(0, 5)
  }

  /**
   * 搜索场景
   */
  static searchScenarios(query: string): EnhancedMockScenario[] {
    const lowercaseQuery = query.toLowerCase()
    return Object.values(EnhancedMockScenarios).filter(scenario =>
      scenario.name.toLowerCase().includes(lowercaseQuery) ||
      scenario.description.toLowerCase().includes(lowercaseQuery) ||
      scenario.tags?.some(tag => tag.toLowerCase().includes(lowercaseQuery))
    )
  }

  /**
   * 生成场景执行计划
   */
  static generateExecutionPlan(scenarioId: string, context: Partial<ScenarioContext>): ScenarioStep[] {
    const scenario = EnhancedMockScenarios[scenarioId]
    if (!scenario || !scenario.steps) {
      return []
    }

    // 过滤掉不满足条件的步骤
    return scenario.steps.filter(step => {
      if (step.condition && context) {
        return step.condition(context as ScenarioContext)
      }
      return true
    })
  }

  /**
   * 执行场景步骤
   */
  static async executeScenarioStep(
    step: ScenarioStep,
    context: ScenarioContext
  ): Promise<BaseWebSocketMessage[]> {
    const config = {
      sessionId: context.sessionId,
      groupChatId: `group_${context.sessionId}`,
      userId: context.userId,
      organizationId: context.organizationId,
    }

    switch (step.type) {
      case 'streaming':
        if (step.data?.generateBurstMessages) {
          // 生成突发消息
          const messages: BaseWebSocketMessage[] = []
          const count = step.data.messageCount || 10
          
          for (let i = 0; i < count; i++) {
            const message = MockDataFactory.createStreamingMessage({
              delta: `突发消息 #${i + 1}: 测试系统处理能力`,
              isComplete: true,
              config,
              isAssistantMessage: true,
            })
            messages.push(message)
          }
          return messages
        } else {
          const text = step.data?.text || '步骤执行中...'
          return MockDataFactory.createStreamingSequence(text, 3, config)
        }

      case 'checkpoint':
        return [MockDataFactory.createCheckpointMessage(step.data?.fields || [], config)]

      case 'report':
        return [MockDataFactory.createReportMessage(step.data?.content || '报告内容', config)]

      case 'error':
        return [MockDataFactory.createErrorMessage(step.data?.code || 'ERROR', step.data?.message || '步骤执行失败', config)]

      case 'delay':
        // 延迟步骤不产生消息
        return []

      default:
        return []
    }
  }

  /**
   * 获取场景统计信息
   */
  static getScenarioStats(): any {
    const scenarios = this.getAllEnhancedScenarios()
    
    return {
      total: scenarios.length,
      byCategory: scenarios.reduce((acc, scenario) => {
        acc[scenario.category] = (acc[scenario.category] || 0) + 1
        return acc
      }, {} as Record<string, number>),
      byDifficulty: scenarios.reduce((acc, scenario) => {
        acc[scenario.difficulty] = (acc[scenario.difficulty] || 0) + 1
        return acc
      }, {} as Record<string, number>),
      avgDuration: scenarios.reduce((sum, s) => sum + s.estimatedDuration, 0) / scenarios.length,
      mostPopularTags: this.getMostPopularTags(scenarios)
    }
  }

  /**
   * 获取最受欢迎的标签
   */
  private static getMostPopularTags(scenarios: EnhancedMockScenario[]): string[] {
    const tagCounts: Record<string, number> = {}
    
    scenarios.forEach(scenario => {
      scenario.tags?.forEach(tag => {
        tagCounts[tag] = (tagCounts[tag] || 0) + 1
      })
    })

    return Object.entries(tagCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([tag]) => tag)
  }
}

// 导出兼容性
export { EnhancedMockScenarios as MockScenariosEnhanced }
export { EnhancedScenarioManager as ScenarioManagerEnhanced }