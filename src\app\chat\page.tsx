/**
 * Chat Page - 聊天主页面（App Router）
 * 
 * 🎯 设计目标：
 * 1. App Router 兼容的聊天页面
 * 2. 支持 SSR 和客户端渲染
 * 3. SEO 友好的元数据
 * 4. 国际化支持
 * 
 * ⚡ 核心特性：
 * - 服务端组件和客户端组件混合
 * - 动态路由参数处理
 * - 错误边界集成
 * - 性能优化
 */

import { Metadata } from 'next';
import { getTranslations } from 'next-intl/server';
import { notFound } from 'next/navigation';
import { Suspense } from 'react';

// 组件导入
import { ChatHistoryLoader, ChatHistorySuspense } from '@/components/chat/chat-history-loader';
import { ProgressiveHydrationProvider } from '@/components/chat/progressive-hydration';
import { SSRChatProvider } from '@/components/chat/ssr-chat-provider';
import { ChatErrorBoundary } from '@/components/error/chat-error-boundary';
import { ChatInterface } from '@/components/ui/chat-interface';

// 类型导入
import type { ChatHistoryLoaderConfig } from '@/components/chat/chat-history-loader';
import type { SSRChatInitialData } from '@/components/chat/ssr-chat-provider';

// ============================================================================
// 页面参数类型
// ============================================================================

interface ChatPageProps {
  params: {
    locale: string
  }
  searchParams: {
    sessionId?: string
    mode?: 'embed' | 'standalone'
    theme?: 'light' | 'dark' | 'auto'
  }
}

// ============================================================================
// 元数据生成
// ============================================================================

export async function generateMetadata({
  params,
  searchParams
}: ChatPageProps): Promise<Metadata> {
  const { locale } = params
  const t = await getTranslations({ locale, namespace: 'chat' })

  const isEmbed = searchParams.mode === 'embed'
  
  return {
    title: t('metadata.title'),
    description: t('metadata.description'),
    keywords: t('metadata.keywords'),
    robots: isEmbed ? 'noindex, nofollow' : 'index, follow',
    openGraph: {
      title: t('metadata.openGraph.title'),
      description: t('metadata.openGraph.description'),
      type: 'website',
      locale: locale,
    },
    twitter: {
      card: 'summary_large_image',
      title: t('metadata.twitter.title'),
      description: t('metadata.twitter.description'),
    },
    alternates: {
      canonical: `/chat`,
    },
  }
}

// ============================================================================
// 服务端数据获取
// ============================================================================

async function getChatPageData(
  locale: string,
  sessionId?: string
): Promise<{
  initialData: SSRChatInitialData
  historyConfig: ChatHistoryLoaderConfig
}> {
  try {
    // 如果有会话ID，获取会话相关数据
    if (sessionId) {
      // 这里应该调用后端API获取会话数据
      // 使用 unified-http 或直接的数据库查询
      const historyConfig: ChatHistoryLoaderConfig = {
        sessionId,
        limit: 50,
        includeSystemMessages: false,
        cacheTTL: 300
      }

      // 模拟获取初始数据
      const initialData: SSRChatInitialData = {
        messages: [], // 会在 ChatHistoryLoader 中加载
        currentSession: null, // 会在 ChatHistoryLoader 中加载
        serverTimestamp: Date.now()
      }

      return { initialData, historyConfig }
    }

    // 默认情况：新建会话
    const historyConfig: ChatHistoryLoaderConfig = {
      sessionId: 'new-session',
      limit: 0, // 新会话无历史消息
      includeSystemMessages: false,
      cacheTTL: 60
    }

    const initialData: SSRChatInitialData = {
      messages: [],
      currentSession: null,
      serverTimestamp: Date.now()
    }

    return { initialData, historyConfig }

  } catch (error) {
    console.error('❌ 获取聊天页面数据失败:', error)
    
    // 降级数据
    return {
      initialData: {
        messages: [],
        currentSession: null,
        serverTimestamp: Date.now()
      },
      historyConfig: {
        sessionId: sessionId || 'fallback-session',
        limit: 0,
        includeSystemMessages: false,
        cacheTTL: 60
      }
    }
  }
}

// ============================================================================
// 主页面组件
// ============================================================================

export default async function ChatPage({
  params,
  searchParams
}: ChatPageProps) {
  const { locale } = params
  const { sessionId, mode = 'standalone', theme = 'auto' } = searchParams

  // 验证语言参数
  const supportedLocales = ['en', 'ja', 'zh']
  if (!supportedLocales.includes(locale)) {
    notFound()
  }

  // 获取翻译
  const t = await getTranslations({ locale, namespace: 'chat' })

  // 获取页面数据
  const { initialData, historyConfig } = await getChatPageData(locale, sessionId)

  // 页面设置
  const isEmbedMode = mode === 'embed'
  const pageClassNames = [
    'chat-page',
    `chat-page--${mode}`,
    `chat-page--theme-${theme}`,
    isEmbedMode && 'chat-page--embed'
  ].filter(Boolean).join(' ')

  return (
    <div className={pageClassNames}>
      <ChatErrorBoundary>
        <ProgressiveHydrationProvider
          ssrData={initialData}
          config={{
            batchSize: 2,
            batchInterval: 50,
            enablePerformanceMonitoring: process.env.NODE_ENV === 'development'
          }}
        >
          <SSRChatProvider
            initialData={initialData}
            enableSSR={true}
            fallbackStrategy="skeleton"
            config={{
              autoConnect: !isEmbedMode,
              enableNotifications: true,
              maxRetries: 3
            }}
            onHydrationComplete={() => {
              console.log('✅ 聊天页面 hydration 完成')
            }}
            onHydrationError={(error) => {
              console.error('❌ 聊天页面 hydration 失败:', error)
            }}
          >
            <ChatHistorySuspense fallback={<ChatPageSkeleton />}>
              <ChatHistoryLoader
                config={historyConfig}
                onError={(error) => {
                  console.error('❌ 聊天历史加载失败:', error)
                }}
              >
                {(historyData) => (
                  <ChatPageContent
                    historyData={historyData}
                    locale={locale}
                    mode={mode}
                    theme={theme}
                    translations={t}
                  />
                )}
              </ChatHistoryLoader>
            </ChatHistorySuspense>
          </SSRChatProvider>
        </ProgressiveHydrationProvider>
      </ChatErrorBoundary>
    </div>
  )
}

// ============================================================================
// 页面内容组件
// ============================================================================

import type { ChatHistoryData } from '@/components/chat/chat-history-loader';

interface ChatPageContentProps {
  historyData: ChatHistoryData;
  locale: string;
  mode: string;
  theme: string;
  translations: any;
}

function ChatPageContent({
  historyData,
  locale,
  mode,
  theme,
  translations
}: ChatPageContentProps) {
  return (
    <main className="chat-page-content">
      {/* 页面头部 */}
      {mode !== 'embed' && (
        <header className="chat-page-header">
          <div className="container mx-auto px-4 py-2">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              {translations('title')}
            </h1>
            <p className="text-gray-600 dark:text-gray-300 mt-1">
              {translations('subtitle')}
            </p>
          </div>
        </header>
      )}

      {/* 聊天界面 */}
      <div className="chat-page-main flex-1">
        <Suspense fallback={<ChatInterfaceSkeleton />}>
          <ChatInterface
            initialMessages={historyData.messages}
            sessionStats={historyData.sessionStats}
            mode={mode as 'embed' | 'standalone'}
            theme={theme as 'light' | 'dark' | 'auto'}
            locale={locale}
          />
        </Suspense>
      </div>

      {/* 页面底部 */}
      {mode !== 'embed' && (
        <footer className="chat-page-footer">
          <div className="container mx-auto px-4 py-2 text-center text-sm text-gray-500">
            {translations('footer.powered_by')}
          </div>
        </footer>
      )}
    </main>
  )
}

// ============================================================================
// 骨架屏组件
// ============================================================================

function ChatPageSkeleton() {
  return (
    <div className="chat-page-skeleton animate-pulse">
      {/* 头部骨架屏 */}
      <div className="chat-page-header-skeleton">
        <div className="container mx-auto px-4 py-4">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>

      {/* 主要内容骨架屏 */}
      <div className="chat-page-main-skeleton flex-1 p-4">
        <div className="max-w-4xl mx-auto">
          {/* 消息列表骨架屏 */}
          <div className="space-y-4 mb-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex space-x-3">
                <div className="w-8 h-8 bg-gray-200 rounded-full flex-shrink-0"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                </div>
              </div>
            ))}
          </div>

          {/* 输入框骨架屏 */}
          <div className="border-t pt-4">
            <div className="flex space-x-2">
              <div className="flex-1 h-12 bg-gray-200 rounded"></div>
              <div className="w-20 h-12 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

function ChatInterfaceSkeleton() {
  return (
    <div className="chat-interface-skeleton animate-pulse h-full">
      <div className="flex flex-col h-full">
        {/* 消息区域 */}
        <div className="flex-1 p-4 space-y-4">
          {[1, 2, 3, 4].map((i) => (
            <div key={i} className="flex space-x-3">
              <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
              <div className="flex-1 space-y-1">
                <div className="h-3 bg-gray-200 rounded w-16"></div>
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-4 bg-gray-200 rounded w-5/6"></div>
              </div>
            </div>
          ))}
        </div>

        {/* 输入区域 */}
        <div className="border-t p-4">
          <div className="flex space-x-2">
            <div className="flex-1 h-10 bg-gray-200 rounded"></div>
            <div className="w-16 h-10 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    </div>
  )
}

// ============================================================================
// 路由配置
// ============================================================================

export const runtime = 'nodejs'
export const revalidate = 0 // 禁用静态生成缓存，因为聊天是动态内容