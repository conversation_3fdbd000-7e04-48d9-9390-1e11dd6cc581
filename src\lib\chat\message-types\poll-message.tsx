/**
 * 投票消息类型扩展示例
 * 
 * 🎯 演示目标：
 * 展示如何在5分钟内添加一个全新的消息类型
 * 
 * ⏱️ 开发步骤：
 * 1. 定义消息类型接口 (1分钟)
 * 2. 创建消息处理器 (2分钟)  
 * 3. 创建消息渲染器 (2分钟)
 * 4. 注册消息类型 (立即生效)
 * 
 * ✨ 这个示例展示了新架构的强大扩展能力！
 */

'use client'

import React, { useState, useCallback } from 'react'
import { cn } from '@/lib/utils'
import type { BaseWebSocketMessage as WebSocketMessage } from '@/types/websocket-event-type'
import type { MessageProcessor, MessageRenderer } from '@/lib/chat/chat-registry'
import { registerMessageProcessor, registerMessageRenderer } from '@/lib/chat/chat-registry'

// UI组件
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Vote, Users } from 'lucide-react'

// ============================================================================
// 1. 定义消息类型接口 (1分钟)
// ============================================================================

/**
 * 投票消息数据结构
 */
export interface PollMessageData {
  /** 投票问题 */
  question: string
  
  /** 投票选项 */
  options: Array<{
    id: string
    text: string
    votes: number
    percentage: number
  }>
  
  /** 投票配置 */
  config: {
    /** 是否允许多选 */
    multipleChoice: boolean
    /** 是否匿名投票 */
    anonymous: boolean
    /** 截止时间 */
    deadline?: number
    /** 最大投票数 */
    maxVotes?: number
  }
  
  /** 统计信息 */
  stats: {
    totalVotes: number
    totalParticipants: number
    isActive: boolean
    winningOption?: string
  }
  
  /** 用户投票状态 */
  userVotes?: string[]  // 用户已投票的选项ID
}

/**
 * 投票消息接口
 */
export interface PollMessage extends WebSocketMessage {
  payload: {
    type: 'poll'
    content: string  // 投票问题的简要描述
    data: PollMessageData
  }
}

// ============================================================================
// 2. 创建消息处理器 (2分钟)
// ============================================================================

/**
 * 投票消息处理器
 */
const pollMessageProcessor: MessageProcessor<PollMessage> = {
  process: async (message: WebSocketMessage) => {
    // 验证和处理投票消息
    if (message.payload.type !== 'poll') {
      throw new Error('Invalid poll message payload')
    }
    const pollData = message.payload.data as PollMessageData
    
    // 计算投票统计
    const totalVotes = pollData.options.reduce((sum, option) => sum + option.votes, 0)
    const totalParticipants = pollData.stats.totalParticipants || totalVotes
    
    // 计算各选项百分比
    const processedOptions = pollData.options.map(option => ({
      ...option,
      percentage: totalVotes > 0 ? Math.round((option.votes / totalVotes) * 100) : 0
    }))
    
    // 找出获胜选项
    const winningOption = processedOptions.reduce((winner, current) => 
      current.votes > winner.votes ? current : winner
    )
    
    // 检查投票是否仍然活跃
    const isActive = pollData.config.deadline 
      ? Date.now() < pollData.config.deadline 
      : pollData.stats.isActive
    
    const processedMessage: PollMessage = {
      ...message,
      payload: {
        type: 'poll',
        content: message.payload.content,
        data: {
          ...pollData,
          options: processedOptions,
          stats: {
            totalVotes,
            totalParticipants,
            isActive,
            winningOption: winningOption.id
          }
        }
      }
    }
    
    return processedMessage
  },
  
  validate: (payload: any) => {
    return payload?.type === 'poll' && 
           payload?.data?.question && 
           Array.isArray(payload?.data?.options) &&
           payload.data.options.length >= 2
  },
  
  description: '投票消息处理器 - 处理投票数据和统计计算'
}

// ============================================================================
// 3. 创建消息渲染器 (2分钟)
// ============================================================================

/**
 * 投票消息渲染组件
 */
const PollMessageRenderer: React.FC<{
  message: PollMessage
  onUpdate?: (messageId: string, updates: Partial<WebSocketMessage>) => void
  onAction?: (action: string, data?: any) => void
}> = ({ message, onUpdate, onAction }) => {
  const pollData = message.payload.data
  const [userVotes, setUserVotes] = useState<Set<string>>(
    new Set(pollData.userVotes || [])
  )
  const [isVoting, setIsVoting] = useState(false)
  
  // ============================================================================
  // 投票处理逻辑
  // ============================================================================
  
  const handleVote = useCallback(async (optionId: string) => {
    if (!pollData.stats.isActive || isVoting) return
    
    setIsVoting(true)
    
    try {
      let newVotes = new Set(userVotes)
      
      if (pollData.config.multipleChoice) {
        // 多选模式：切换选项
        if (newVotes.has(optionId)) {
          newVotes.delete(optionId)
        } else {
          newVotes.add(optionId)
        }
      } else {
        // 单选模式：替换选择
        newVotes = new Set([optionId])
      }
      
      // 更新本地状态
      setUserVotes(newVotes)
      
      // 计算新的投票数据
      const updatedOptions = pollData.options.map(option => {
        const wasVoted = (pollData.userVotes || []).includes(option.id)
        const isVoted = newVotes.has(option.id)
        
        let newVoteCount = option.votes
        if (!wasVoted && isVoted) {
          newVoteCount += 1  // 新增投票
        } else if (wasVoted && !isVoted) {
          newVoteCount -= 1  // 取消投票
        }
        
        return {
          ...option,
          votes: Math.max(0, newVoteCount)
        }
      })
      
      // 更新消息数据
      const updatedMessage = {
        payload: {
          ...message.payload,
          data: {
            ...pollData,
            options: updatedOptions,
            userVotes: Array.from(newVotes)
          }
        }
      }
      
      onUpdate?.(message.id, updatedMessage)
      
      // 通知父组件投票事件
      onAction?.('vote', {
        messageId: message.id,
        optionId,
        userVotes: Array.from(newVotes)
      })
      
    } catch (error) {
      console.error('投票失败:', error)
      // 恢复原始状态
      setUserVotes(new Set(pollData.userVotes || []))
    } finally {
      setIsVoting(false)
    }
  }, [pollData, userVotes, isVoting, message, onUpdate, onAction])
  
  // ============================================================================
  // 渲染逻辑
  // ============================================================================
  
  const isExpired = pollData.config.deadline && Date.now() > pollData.config.deadline
  const canVote = pollData.stats.isActive && !isExpired
  const hasVoted = userVotes.size > 0
  
  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center space-x-2 text-base">
          <Vote className="w-5 h-5 text-blue-600" />
          <span>投票</span>
          {!canVote && <Badge variant="secondary">已结束</Badge>}
        </CardTitle>
        <p className="text-sm text-gray-600 mt-1">
          {pollData.question}
        </p>
      </CardHeader>
      
      <CardContent className="space-y-3">
        {/* 投票选项 */}
        <div className="space-y-2">
          {pollData.options.map((option) => {
            const isSelected = userVotes.has(option.id)
            const isWinning = option.id === pollData.stats.winningOption
            
            return (
              <Button
                key={option.id}
                variant={isSelected ? "default" : "outline"}
                className={cn(
                  "w-full h-auto p-3 justify-between",
                  isWinning && "ring-2 ring-green-500",
                  !canVote && "cursor-not-allowed"
                )}
                onClick={() => canVote && handleVote(option.id)}
                disabled={!canVote || isVoting}
              >
                <div className="flex items-center space-x-2 min-w-0 flex-1">
                  <span className="text-sm font-medium truncate">
                    {option.text}
                  </span>
                  {isSelected && (
                    <Badge variant="secondary" className="text-xs">
                      已选
                    </Badge>
                  )}
                </div>
                
                <div className="flex items-center space-x-2 ml-3">
                  <span className="text-sm font-medium">
                    {option.percentage}%
                  </span>
                  <span className="text-xs text-gray-500">
                    ({option.votes})
                  </span>
                </div>
              </Button>
            )
          })}
        </div>
        
        {/* 投票进度条 */}
        {pollData.stats.totalVotes > 0 && (
          <div className="space-y-1">
            {pollData.options.map((option) => (
              <div key={`progress-${option.id}`} className="flex items-center space-x-2">
                <span className="text-xs text-gray-600 w-12 truncate">
                  {option.text}
                </span>
                <Progress 
                  value={option.percentage} 
                  className="flex-1 h-2"
                />
                <span className="text-xs text-gray-500 w-8 text-right">
                  {option.percentage}%
                </span>
              </div>
            ))}
          </div>
        )}
        
        {/* 统计信息 */}
        <div className="flex items-center justify-between text-xs text-gray-500 pt-2 border-t">
          <div className="flex items-center space-x-1">
            <Users className="w-3 h-3" />
            <span>{pollData.stats.totalParticipants} 人参与</span>
          </div>
          
          <div className="flex items-center space-x-2">
            <span>{pollData.stats.totalVotes} 票</span>
            {pollData.config.deadline && (
              <span>
                截止: {new Date(pollData.config.deadline).toLocaleString()}
              </span>
            )}
          </div>
        </div>
        
        {/* 投票设置标识 */}
        <div className="flex flex-wrap gap-1">
          {pollData.config.multipleChoice && (
            <Badge variant="outline" className="text-xs">多选</Badge>
          )}
          {pollData.config.anonymous && (
            <Badge variant="outline" className="text-xs">匿名</Badge>
          )}
          {hasVoted && (
            <Badge variant="secondary" className="text-xs">已投票</Badge>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// ============================================================================
// 4. 注册消息类型 (立即生效)
// ============================================================================

/**
 * 注册投票消息类型
 * 调用此函数后，投票消息类型立即可用！
 */
export function registerPollMessageType() {
  // 注册处理器
  registerMessageProcessor('poll', pollMessageProcessor)
  
  // 注册渲染器
  registerMessageRenderer('poll', {
    component: PollMessageRenderer,
    description: '投票消息渲染器 - 支持单选/多选投票，实时统计'
  })
  
  console.log('✅ 投票消息类型注册成功！')
}

// ============================================================================
// 便捷创建函数
// ============================================================================

/**
 * 创建投票消息的便捷函数
 */
export function createPollMessage(
  question: string,
  options: string[],
  config: Partial<PollMessageData['config']> = {}
): Omit<PollMessage, 'id' | 'timestamp' | 'sessionId'> {
  const pollOptions = options.map((text, index) => ({
    id: `option_${index}`,
    text,
    votes: 0,
    percentage: 0
  }))
  
  return {
    groupChatId: '', // 将由发送者填充
    organizationId: '', // 将由发送者填充
    userId: '', // 将由发送者填充
    payload: {
      type: 'poll',
      content: question,
      data: {
        question,
        options: pollOptions,
        config: {
          multipleChoice: false,
          anonymous: true,
          ...config
        },
        stats: {
          totalVotes: 0,
          totalParticipants: 0,
          isActive: true
        }
      }
    }
  }
}

// ============================================================================
// 使用示例
// ============================================================================

/*
// 在应用初始化时注册
registerPollMessageType()

// 创建投票消息
const pollMessage = createPollMessage(
  "你最喜欢的编程语言是什么？",
  ["TypeScript", "Python", "Rust", "Go", "Java"],
  {
    multipleChoice: false,
    anonymous: true,
    deadline: Date.now() + 24 * 60 * 60 * 1000 // 24小时后截止
  }
)

// 发送投票消息
const { sendMessage } = useChat()
await sendMessage("", {
  messageType: 'poll',
  metadata: pollMessage.payload
})
*/