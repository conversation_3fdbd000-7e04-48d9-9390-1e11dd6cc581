/**
 * Message Renderer - 统一的消息渲染组件
 * 
 * 🎯 设计目标：
 * 1. 基于ChatRegistry的插件化渲染系统
 * 2. 统一所有消息类型的渲染逻辑
 * 3. 支持流式消息、交互状态、错误处理
 * 4. 高性能的组件缓存和优化
 * 
 * ⚡ 核心特性：
 * - 自动消息类型识别和渲染器选择
 * - 内置交互状态管理（选择、编辑、悬停等）
 * - 流式消息实时更新支持
 * - 错误边界和降级渲染
 * - 无障碍和键盘导航支持
 */

'use client'

import React, { memo, useMemo, useCallback, useState, ElementType } from 'react';
import { cn } from '@/lib/utils';
import type { BaseWebSocketMessage as WebSocketMessage } from '@/types/websocket-event-type';
import { chatRegistry } from '@/lib/chat/chat-registry';
import { useChat } from './chat-provider';

// UI组件
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Copy, 
  Edit, 
  Trash2, 
  MoreVertical, 
  RefreshCw,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

// ============================================================================
// 类型定义
// ============================================================================

/**
 * 消息渲染器属性
 */
export interface MessageRendererProps {
  /** 消息数据 */
  message: WebSocketMessage;
  
  /** 消息在列表中的索引 */
  index?: number;
  
  /** 是否为流式消息 */
  isStreaming?: boolean;
  
  /** 流式累积的内容 */
  streamingContent?: string;
  
  /** 自定义CSS类名 */
  className?: string;
  
  /** 是否显示操作按钮 */
  showActions?: boolean;
  
  /** 是否启用交互功能 */
  enableInteraction?: boolean;
  
  /** 自定义操作按钮 */
  customActions?: MessageAction[];
  
  /** 事件回调 */
  onAction?: (action: string, data?: any) => void;
}

/**
 * 消息操作定义
 */
export interface MessageAction {
  id: string;
  label: string;
  icon?: ElementType;
  shortcut?: string;
  handler: (message: WebSocketMessage) => void | Promise<void>;
  visible?: (message: WebSocketMessage) => boolean;
  disabled?: (message: WebSocketMessage) => boolean;
}

/**
 * 消息状态
 */
export interface MessageState {
  status: 'pending' | 'sent' | 'delivered' | 'failed';
  error?: string;
  timestamp: number;
  retryCount?: number;
}

// ============================================================================
// 内置消息渲染器
// ============================================================================

/**
 * 文本消息渲染器
 */
const TextMessageRenderer: React.FC<{ 
  message: WebSocketMessage;
  isStreaming?: boolean;
  streamingContent?: string;
}> = ({ message, isStreaming, streamingContent }) => {
  // 安全地获取content属性
  const getContent = () => {
    if (isStreaming) return streamingContent || '';
    if ('content' in message.payload) return (message.payload as any).content;
    return '';
  };
  const content = getContent();
  
  return (
    <div className="prose prose-sm max-w-none">
      <div className="whitespace-pre-wrap break-words">
        {content}
        {isStreaming && <span className="animate-pulse">▊</span>}
      </div>
    </div>
  );
};

/**
 * 错误消息渲染器
 */
const ErrorMessageRenderer: React.FC<{ message: WebSocketMessage }> = ({ message }) => {
  return (
    <div className="flex items-start space-x-3 p-3 bg-red-50 border border-red-200 rounded-lg">
      <AlertCircle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-red-800">
          {'title' in message.payload ? (message.payload as any).title : '消息发送失败'}
        </p>
        {'content' in message.payload && (message.payload as any).content && (
          <p className="text-sm text-red-600 mt-1">
            {(message.payload as any).content}
          </p>
        )}
      </div>
    </div>
  );
};

/**
 * 系统消息渲染器
 */
const SystemMessageRenderer: React.FC<{ message: WebSocketMessage }> = ({ message }) => {
  return (
    <div className="flex justify-center py-2">
      <Badge variant="secondary" className="text-xs">
        {'content' in message.payload ? (message.payload as any).content : ''}
      </Badge>
    </div>
  );
};

/**
 * 默认降级渲染器
 */
const FallbackRenderer: React.FC<{ 
  message: WebSocketMessage;
  error?: string;
}> = ({ message, error }) => {
  return (
    <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
      <div className="flex items-center space-x-2 mb-2">
        <AlertCircle className="w-4 h-4 text-amber-500" />
        <span className="text-sm font-medium text-gray-700">
          未知消息类型: {message.payload?.type || 'unknown'}
        </span>
      </div>
      {error && (
        <p className="text-xs text-red-600 mb-2">渲染错误: {error}</p>
      )}
      <pre className="text-xs text-gray-600 overflow-auto">
        {JSON.stringify(message, null, 2)}
      </pre>
    </div>
  );
};

// ============================================================================
// 消息渲染器主组件
// ============================================================================

export const MessageRenderer: React.FC<MessageRendererProps> = memo(({
  message,
  index,
  isStreaming = false,
  streamingContent,
  className,
  showActions = true,
  enableInteraction = true,
  customActions = [],
  onAction,
}) => {
  const { 
    interaction, 
    updateMessage, 
    deleteMessage,
    notify 
  } = useChat();
  
  const [renderError, setRenderError] = useState<string | null>(null);
  const [isRetrying, setIsRetrying] = useState(false);
  
  // ============================================================================
  // 消息状态计算
  // ============================================================================
  
  const messageState = useMemo((): MessageState => {
    // 基于消息内容推断状态
    if (message.error) {
      return { status: 'failed', error: message.error, timestamp: message.timestamp };
    }
    
    if (isStreaming) {
      return { status: 'pending', timestamp: message.timestamp };
    }
    
    return { status: 'delivered', timestamp: message.timestamp };
  }, [message, isStreaming]);
  
  // 交互状态
  const interactionState = useMemo(() => ({
    isSelected: interaction.selectedMessages.includes(message.id),
    isEditing: interaction.editingMessage === message.id,
    isHovered: false, // TODO: 从UI store获取
  }), [interaction, message.id]);
  
  // 用户角色判断
  const isUserMessage = Boolean(message.userId);
  const isAssistantMessage = !isUserMessage && message.payload?.type !== 'system';
  const isSystemMessage = message.payload?.type === 'system';
  
  // ============================================================================
  // 渲染器选择和渲染
  // ============================================================================
  
  const renderedContent = useMemo(() => {
    try {
      setRenderError(null);
      
      const messageType = message.payload?.type || 'text';
      const renderer = chatRegistry.getRenderer(messageType);
      
      if (renderer) {
        // 使用注册的渲染器
        const Component = renderer.component;
        return (
          <Component
            message={message}
            context={{
              sessionId: message.sessionId,
              userId: message.userId,
              timestamp: message.timestamp,
              isStreaming,
            }}
            onUpdate={(messageId, updates) => {
              updateMessage(messageId, updates);
            }}
            onAction={onAction || (() => {})}
          />
        );
      } else {
        // 使用内置渲染器
        switch (messageType) {
          case 'text':
          case 'user_message':
          case 'assistant_message':
            return (
              <TextMessageRenderer 
                message={message}
                isStreaming={isStreaming}
                streamingContent={streamingContent || ''}
              />
            );
          
          case 'error':
            return <ErrorMessageRenderer message={message} />;
            
          case 'system':
            return <SystemMessageRenderer message={message} />;
            
          default:
            return <FallbackRenderer message={message} />;
        }
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '渲染错误';
      setRenderError(errorMessage);
      return <FallbackRenderer message={message} error={errorMessage} />;
    }
  }, [message, isStreaming, streamingContent, updateMessage, onAction]);
  
  // ============================================================================
  // 事件处理
  // ============================================================================
  
  const handleCopy = useCallback(async () => {
    try {
      const content = ('content' in message.payload ? String((message.payload as any).content) : '') || JSON.stringify(message, null, 2);
      await navigator.clipboard.writeText(content);
      notify.success('已复制到剪贴板');
    } catch (error) {
      notify.error('复制失败');
    }
  }, [message, notify]);
  
  const handleEdit = useCallback(() => {
    if (isUserMessage) {
      interaction.startEdit(message.id);
    }
  }, [interaction, message.id, isUserMessage]);
  
  const handleDelete = useCallback(async () => {
    if (window.confirm('确定要删除这条消息吗？')) {
      const success = deleteMessage(message.id);
      if (success) {
        notify.success('消息已删除');
      } else {
        notify.error('删除失败');
      }
    }
  }, [deleteMessage, message.id, notify]);
  
  const handleRetry = useCallback(async () => {
    if (messageState.status === 'failed') {
      setIsRetrying(true);
      try {
        // TODO: 实现重试逻辑
        await new Promise(resolve => setTimeout(resolve, 1000));
        notify.success('消息已重新发送');
      } catch (error) {
        notify.error('重试失败');
      } finally {
        setIsRetrying(false);
      }
    }
  }, [messageState.status, notify]);
  
  const handleSelect = useCallback(() => {
    if (enableInteraction) {
      interaction.selectMessage(message.id);
    }
  }, [enableInteraction, interaction, message.id]);
  
  // ============================================================================
  // 动作按钮配置
  // ============================================================================
  
  const actions = useMemo((): MessageAction[] => {
    const baseActions: MessageAction[] = [
      {
        id: 'copy',
        label: '复制',
        icon: Copy,
        shortcut: 'Ctrl+C',
        handler: handleCopy,
      },
    ];
    
    if (isUserMessage) {
      baseActions.push({
        id: 'edit',
        label: '编辑',
        icon: Edit,
        handler: handleEdit,
      });
    }
    
    if (messageState.status === 'failed') {
      baseActions.push({
        id: 'retry',
        label: '重试',
        icon: RefreshCw,
        handler: handleRetry,
        disabled: () => isRetrying,
      });
    }
    
    baseActions.push({
      id: 'delete',
      label: '删除',
      icon: Trash2,
      handler: handleDelete,
    });
    
    return [...baseActions, ...customActions];
  }, [isUserMessage, messageState.status, isRetrying, handleCopy, handleEdit, handleRetry, handleDelete, customActions]);
  
  // ============================================================================
  // 样式计算
  // ============================================================================
  
  const containerClasses = cn(
    'group relative transition-all duration-200',
    {
      // 消息角色样式
      'ml-12': isAssistantMessage,
      'mr-12': isUserMessage,
      
      // 交互状态样式
      'ring-2 ring-blue-500': interactionState.isSelected,
      'bg-blue-50': interactionState.isEditing,
      
      // 消息状态样式
      'opacity-60': messageState.status === 'pending',
      'border-red-200 bg-red-50': messageState.status === 'failed',
      
      // 流式消息样式
      'animate-pulse': isStreaming,
    },
    className
  );
  
  const messageClasses = cn(
    'relative rounded-lg px-4 py-3 shadow-sm transition-all',
    {
      // 消息角色背景
      'bg-white border border-gray-200': isAssistantMessage,
      'bg-blue-600 text-white': isUserMessage,
      'bg-gray-100': isSystemMessage,
      
      // 状态指示
      'border-red-300': messageState.status === 'failed',
      'border-amber-300': messageState.status === 'pending',
    }
  );
  
  // ============================================================================
  // 渲染
  // ============================================================================
  
  return (
    <div 
      className={containerClasses}
      onClick={handleSelect}
      role="article"
      aria-label={`消息来自 ${isUserMessage ? '用户' : '助手'}`}
      tabIndex={enableInteraction ? 0 : undefined}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          handleSelect();
        }
      }}
    >
      {/* 主消息内容 */}
      <Card className={messageClasses}>
        {/* 消息状态指示器 */}
        {messageState.status !== 'delivered' && (
          <div className="absolute -top-2 -right-2">
            {messageState.status === 'pending' && (
              <Badge variant="secondary" className="text-xs">
                {isStreaming ? '接收中...' : '发送中...'}
              </Badge>
            )}
            {messageState.status === 'failed' && (
              <Badge variant="destructive" className="text-xs">
                失败
              </Badge>
            )}
            {messageState.status === 'sent' && (
              <CheckCircle className="w-4 h-4 text-green-500" />
            )}
          </div>
        )}
        
        {/* 渲染的消息内容 */}
        <div className="message-content">
          {renderedContent}
        </div>
        
        {/* 消息元信息 */}
        <div className="flex items-center justify-between mt-2 text-xs opacity-60">
          <span>
            {new Date(messageState.timestamp).toLocaleTimeString()}
          </span>
          {messageState.retryCount && (
            <span>重试 {messageState.retryCount} 次</span>
          )}
        </div>
      </Card>
      
      {/* 操作按钮 */}
      {showActions && enableInteraction && (
        <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <div className="flex items-center space-x-1 bg-white rounded-lg shadow-sm border border-gray-200 p-1">
            {actions.slice(0, 3).map(action => {
              const Icon = action.icon || MoreVertical;
              const isDisabled = action.disabled?.(message) || false;
              
              return (
                <Button
                  key={action.id}
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0"
                  disabled={isDisabled}
                  onClick={(e) => {
                    e.stopPropagation();
                    action.handler(message);
                  }}
                  title={`${action.label} ${action.shortcut ? `(${action.shortcut})` : ''}`}
                >
                  <Icon className="w-3 h-3" />
                </Button>
              );
            })}
            
            {actions.length > 3 && (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                title="更多操作"
              >
                <MoreVertical className="w-3 h-3" />
              </Button>
            )}
          </div>
        </div>
      )}
      
      {/* 调试信息 */}
      {process.env.NODE_ENV === 'development' && renderError && (
        <div className="mt-2 p-2 bg-red-100 border border-red-300 rounded text-xs">
          <strong>渲染错误:</strong> {renderError}
        </div>
      )}
    </div>
  );
});

MessageRenderer.displayName = 'MessageRenderer';

// ============================================================================
// 预设消息渲染器注册
// ============================================================================

// 注册内置消息类型渲染器
if (typeof window !== 'undefined') {
  // 确保只在客户端注册
  chatRegistry.registerRenderer('text', {
    component: ({ message }) => <TextMessageRenderer message={message} />,
    description: '文本消息渲染器',
  });
  
  chatRegistry.registerRenderer('error', {
    component: ({ message }) => <ErrorMessageRenderer message={message} />,
    description: '错误消息渲染器',
  });
  
  chatRegistry.registerRenderer('system', {
    component: ({ message }) => <SystemMessageRenderer message={message} />,
    description: '系统消息渲染器',
  });
}

export default MessageRenderer;