/**
 * 环境安全工具 - 生产环境保护机制
 * 
 * 🛡️ 设计目标：
 * 1. 在生产环境下严格禁止任何开发/调试功能
 * 2. 统一的安全检查入口，防止绕过
 * 3. 详细的安全审计日志
 * 4. 多重验证确保环境完整性
 */

/**
 * 安全检查结果接口
 */
export interface SecurityCheckResult {
  allowed: boolean
  reason: string
  environment: string
  timestamp: string
}

/**
 * 安全审计日志接口
 */
export interface SecurityAuditLog {
  action: string
  allowed: boolean
  reason: string
  environment: string
  timestamp: string
  userAgent?: string | undefined
  additionalContext?: Record<string, any> | undefined
}

/**
 * 🛡️ 确保不在生产环境 - 核心安全检查
 * 
 * @param action 尝试执行的操作名称
 * @param context 额外上下文信息
 * @throws Error 如果在生产环境下执行被禁止的操作
 * @returns SecurityCheckResult 安全检查结果
 */
export function ensureNotProduction(
  action: string, 
  context?: Record<string, any>
): SecurityCheckResult {
  const environment = process.env.NODE_ENV || 'unknown'
  const timestamp = new Date().toISOString()
  
  // 🔥 严格的生产环境检查
  const isProduction = environment === 'production'
  
  const result: SecurityCheckResult = {
    allowed: !isProduction,
    reason: isProduction 
      ? `生产环境下禁止执行 ${action} 操作`
      : `开发环境下允许执行 ${action} 操作`,
    environment,
    timestamp
  }
  
  // 🔥 记录安全审计日志
  logSecurityAudit({
    action,
    allowed: result.allowed,
    reason: result.reason,
    environment,
    timestamp,
    userAgent: typeof window !== 'undefined' ? window.navigator?.userAgent : undefined,
    additionalContext: context
  })
  
  // 🔥 生产环境下直接抛出错误
  if (isProduction) {
    const errorMessage = `🚫 安全违规：${result.reason}`
    console.error(errorMessage, {
      action,
      environment,
      timestamp,
      context
    })
    throw new Error(errorMessage)
  }
  
  console.log(`✅ 安全检查通过：${result.reason}`, {
    action,
    environment,
    timestamp,
    context
  })
  
  return result
}

/**
 * 🛡️ 多重环境验证 - 深度安全检查
 * 
 * 检查多个环境指标确保环境完整性
 */
export function validateEnvironmentIntegrity(): {
  isValid: boolean
  issues: string[]
  environment: string
} {
  const issues: string[] = []
  const environment = process.env.NODE_ENV || 'unknown'
  
  // 检查NODE_ENV一致性
  if (!process.env.NODE_ENV) {
    issues.push('NODE_ENV 环境变量未设置')
  }
  
  // 检查生产环境下的敏感配置
  if (environment === 'production') {
    // 检查是否强制启用了Mock模式
    if (process.env.NEXT_PUBLIC_FORCE_MOCK_MODE === 'true') {
      issues.push('生产环境下不应启用 FORCE_MOCK_MODE')
    }
    
    // 检查是否有调试标志
    if (process.env.NEXT_PUBLIC_DEBUG === 'true') {
      issues.push('生产环境下不应启用 DEBUG 模式')
    }
    
    // 检查WebSocket URL是否配置
    if (!process.env.NEXT_PUBLIC_WEBSOCKET_URL || process.env.NEXT_PUBLIC_WEBSOCKET_URL === 'mock') {
      issues.push('生产环境下必须配置真实的 WEBSOCKET_URL')
    }
  }
  
  return {
    isValid: issues.length === 0,
    issues,
    environment
  }
}

/**
 * 🛡️ 检查是否为生产环境
 */
export function isProductionEnvironment(): boolean {
  return process.env.NODE_ENV === 'production'
}

/**
 * 🛡️ 检查是否为开发环境
 */
export function isDevelopmentEnvironment(): boolean {
  return process.env.NODE_ENV === 'development'
}

/**
 * 🛡️ 安全审计日志记录
 */
function logSecurityAudit(log: SecurityAuditLog): void {
  // 🔥 在生产环境下，安全日志应该发送到监控系统
  if (isProductionEnvironment()) {
    // TODO: 集成实际的监控系统 (如 DataDog, Sentry 等)
    console.error('🛡️ [SECURITY_AUDIT]', log)
  } else {
    // 开发环境下的详细日志
    console.log('🛡️ [SECURITY_AUDIT]', log)
  }
  
  // 🔥 可以在这里添加额外的审计逻辑
  // 例如：发送到安全监控服务、写入审计数据库等
}

/**
 * 🛡️ 获取环境安全状态报告
 */
export function getEnvironmentSecurityReport(): {
  environment: string
  isProduction: boolean
  integrityCheck: ReturnType<typeof validateEnvironmentIntegrity>
  securityLevel: 'HIGH' | 'MEDIUM' | 'LOW'
  recommendations: string[]
} {
  const environment = process.env.NODE_ENV || 'unknown'
  const isProduction = isProductionEnvironment()
  const integrityCheck = validateEnvironmentIntegrity()
  
  let securityLevel: 'HIGH' | 'MEDIUM' | 'LOW' = 'HIGH'
  const recommendations: string[] = []
  
  if (!integrityCheck.isValid) {
    securityLevel = integrityCheck.issues.length > 2 ? 'LOW' : 'MEDIUM'
    recommendations.push(...integrityCheck.issues.map(issue => `修复：${issue}`))
  }
  
  if (!isProduction && (environment as any) === 'unknown') {
    securityLevel = 'LOW'
    recommendations.push('设置明确的 NODE_ENV 环境变量')
  }
  
  return {
    environment,
    isProduction,
    integrityCheck,
    securityLevel,
    recommendations
  }
}

/**
 * 🛡️ 安全装饰器 - 用于保护敏感方法
 */
export function requireNonProduction(target: any, propertyName: string, descriptor: PropertyDescriptor) {
  const method = descriptor.value
  
  descriptor.value = function (...args: any[]) {
    ensureNotProduction(`${target.constructor.name}.${propertyName}`)
    return method.apply(this, args)
  }
  
  return descriptor
}