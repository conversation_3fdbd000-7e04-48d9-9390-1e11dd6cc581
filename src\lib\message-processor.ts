/**
 * 简化的消息处理器 - 去除过度工程化设计
 *
 * 功能：简单直接的WebSocket消息处理，专注核心功能
 * 依赖：BaseWebSocketMessage类型、各种MessagePayload类型判断函数
 * 性能：轻量级函数式处理，无复杂状态管理
 *
 * 从695行复杂实现简化为 ~50行，去除：
 * - 复杂的策略模式和类层次结构
 * - 不必要的批处理和队列管理
 * - 过度的配置选项和指标收集
 * - 单例模式和全局状态
 */

import type {
  CheckpointPayload,
  ClientMessage,
  ErrorPayload,
  ReportPayload,
  StreamingPayload
} from '@/types/websocket-event-type'

import {
  isCheckpointPayload,
  isErrorPayload,
  isReportPayload,
  isStreamingPayload,
} from '@/types/websocket-event-type'

// ============================================================================
// 简化的消息处理接口
// ============================================================================

export interface ProcessingContext {
  sessionId: string
  userId: string
  onMessage?: (message: ClientMessage) => void
  onError?: (error: Error, message: ClientMessage) => void
}

// ============================================================================
// 核心消息处理函数
// ============================================================================

/**
 * 处理WebSocket消息 - 简化版本
 * 根据消息类型添加相应的metadata，无复杂的类结构和配置
 */
export const processMessage = (
  message: ClientMessage,
  context?: ProcessingContext
): ClientMessage => {
  try {
    // 基础处理：添加处理时间戳
    const processed: ClientMessage = {
      ...message,
      metadata: {
        ...message.metadata,
        processingTimestamp: new Date().toISOString(),
      },
      status: 'delivered',
    }

    // 确保metadata存在
    if (!processed.metadata) {
      processed.metadata = {}
    }

    // 根据消息类型添加特定metadata
    if (isStreamingPayload(message.payload)) {
      const payload = message.payload as StreamingPayload
      processed.metadata!.streamingState = {
        isComplete: payload.isComplete,
        accumulatedText: payload.delta,
      }
      processed.status = payload.isComplete ? 'delivered' : 'sending'
    } else if (isCheckpointPayload(message.payload)) {
      const payload = message.payload as CheckpointPayload
      processed.metadata!.formFields = payload.fields
      processed.metadata!.fieldCount = payload.fields.length
    } else if (isReportPayload(message.payload)) {
      const payload = message.payload as ReportPayload
      processed.metadata!.contentLength = payload.content.length
      processed.metadata!.contentType = 'markdown'
    } else if (isErrorPayload(message.payload)) {
      const payload = message.payload as ErrorPayload
      processed.metadata!.errorCode = payload.code
      processed.metadata!.errorMessage = payload.message
      processed.status = 'failed'
    }

    // 调用回调函数
    if (context?.onMessage) {
      context.onMessage(processed)
    }

    return processed
  } catch (error) {
    const err = error instanceof Error ? error : new Error('Message processing failed')

    if (context?.onError) {
      context.onError(err, message)
    }

    // 返回带错误状态的消息
    return {
      ...message,
      metadata: {
        ...message.metadata,
        processingTimestamp: new Date().toISOString(),
        processingError: err.message,
      },
      status: 'failed',
    }
  }
}

/**
 * 批量处理消息 - 简化版本
 * 移除复杂的队列管理，直接处理数组
 */
export const processMessages = (
  messages: ClientMessage[],
  context?: ProcessingContext
): ClientMessage[] => {
  return messages.map(message => processMessage(message, context))
}

/**
 * 检查消息是否需要处理
 * 简化的消息类型检查，替代复杂的handler.canHandle逻辑
 */
export const canProcessMessage = (message: ClientMessage): boolean => {
  return (
    isStreamingPayload(message.payload) ||
    isCheckpointPayload(message.payload) ||
    isReportPayload(message.payload) ||
    isErrorPayload(message.payload)
  )
}

/**
 * 获取消息处理类型
 * 简单的类型判断，替代复杂的handler.getHandlerType逻辑
 */
export const getMessageType = (message: ClientMessage): string => {
  if (isStreamingPayload(message.payload)) return 'streaming'
  if (isCheckpointPayload(message.payload)) return 'checkpoint'
  if (isReportPayload(message.payload)) return 'report'
  if (isErrorPayload(message.payload)) return 'error'
  return 'unknown'
}

// ============================================================================
// 兼容性导出 - 保持与现有代码的兼容性
// ============================================================================

/**
 * @deprecated 使用简化的 processMessage 函数
 */
export const getGlobalMessageProcessor = () => ({
  processMessage,
  processMessageBatch: processMessages,
  reset: () => {},
  destroy: () => {},
})

/**
 * @deprecated 使用简化的 processMessage 函数
 */
export const resetGlobalMessageProcessor = () => {}

export default {
  processMessage,
  processMessages,
  canProcessMessage,
  getMessageType,
}
