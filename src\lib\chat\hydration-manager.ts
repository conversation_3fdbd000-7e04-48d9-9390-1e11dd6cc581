/**
 * Hydration Manager - 聊天系统hydration状态管理
 * 
 * 🎯 设计目标：
 * 1. 管理SSR到客户端的hydration过程
 * 2. 避免hydration不匹配问题
 * 3. 提供渐进式hydration能力
 * 4. 处理状态同步和数据迁移
 * 
 * ⚡ 核心特性：
 * - 分阶段hydration
 * - 状态同步检测
 * - 错误恢复机制
 * - 性能监控
 */

import { useEffect, useState, useRef, useCallback } from 'react'
import type { ClientMessage } from '@/types/websocket-event-type'
import type { ChatSession } from '@/stores/session-store'

// ============================================================================
// 类型定义
// ============================================================================

/**
 * Hydration 阶段
 */
export enum HydrationPhase {
  /** 初始SSR状态 */
  SSR = 'ssr',
  /** 开始hydration */
  STARTING = 'starting',
  /** 数据同步中 */
  SYNCING = 'syncing',
  /** 组件激活中 */
  ACTIVATING = 'activating',
  /** Hydration完成 */
  COMPLETED = 'completed',
  /** Hydration失败 */
  FAILED = 'failed'
}

/**
 * Hydration 状态
 */
export interface HydrationState {
  /** 当前阶段 */
  phase: HydrationPhase
  /** 开始时间 */
  startTime: number
  /** 持续时间 */
  duration: number
  /** 错误信息 */
  error: Error | null
  /** 进度百分比 */
  progress: number
  /** 详细步骤 */
  steps: HydrationStep[]
}

/**
 * Hydration 步骤
 */
export interface HydrationStep {
  /** 步骤ID */
  id: string
  /** 步骤名称 */
  name: string
  /** 步骤状态 */
  status: 'pending' | 'running' | 'completed' | 'failed'
  /** 开始时间 */
  startTime?: number
  /** 结束时间 */
  endTime?: number
  /** 错误信息 */
  error?: Error
  /** 元数据 */
  metadata?: Record<string, any>
}

/**
 * 数据同步配置
 */
export interface DataSyncConfig {
  /** 是否同步消息数据 */
  syncMessages?: boolean
  /** 是否同步会话数据 */
  syncSessions?: boolean
  /** 是否同步UI状态 */
  syncUIState?: boolean
  /** 同步超时时间（毫秒） */
  timeout?: number
  /** 重试次数 */
  retryCount?: number
  /** 是否启用增量同步 */
  enableIncremental?: boolean
}

/**
 * Hydration 配置
 */
export interface HydrationConfig {
  /** 数据同步配置 */
  dataSync?: DataSyncConfig
  /** 是否启用性能监控 */
  enablePerformanceMonitoring?: boolean
  /** 是否启用调试模式 */
  enableDebug?: boolean
  /** 自定义步骤 */
  customSteps?: Array<{
    id: string
    name: string
    handler: () => Promise<void>
  }>
  /** 错误处理策略 */
  errorStrategy?: 'abort' | 'continue' | 'retry'
}

/**
 * SSR数据结构
 */
export interface SSRData {
  /** 消息数据 */
  messages?: ClientMessage[]
  /** 会话数据 */
  session?: ChatSession | null
  /** 服务端时间戳 */
  serverTimestamp?: number
  /** 校验和 */
  checksum?: string
}

// ============================================================================
// Hydration Manager 类
// ============================================================================

export class HydrationManager {
  private state: HydrationState
  private config: HydrationConfig
  private listeners: Set<(state: HydrationState) => void> = new Set()
  private performanceMarks: Map<string, number> = new Map()

  constructor(config: HydrationConfig = {}) {
    this.config = {
      dataSync: {
        syncMessages: true,
        syncSessions: true,
        syncUIState: false,
        timeout: 5000,
        retryCount: 3,
        enableIncremental: true,
        ...config.dataSync
      },
      enablePerformanceMonitoring: config.enablePerformanceMonitoring ?? true,
      enableDebug: config.enableDebug ?? process.env.NODE_ENV === 'development',
      customSteps: config.customSteps ?? [],
      errorStrategy: config.errorStrategy ?? 'retry'
    }

    this.state = {
      phase: HydrationPhase.SSR,
      startTime: 0,
      duration: 0,
      error: null,
      progress: 0,
      steps: this.initializeSteps()
    }

    this.debug('HydrationManager initialized', this.config)
  }

  /**
   * 初始化hydration步骤
   */
  private initializeSteps(): HydrationStep[] {
    const baseSteps: HydrationStep[] = [
      {
        id: 'client-ready',
        name: '客户端环境准备',
        status: 'pending'
      },
      {
        id: 'data-sync',
        name: '数据同步',
        status: 'pending'
      },
      {
        id: 'store-hydration',
        name: 'Store状态恢复',
        status: 'pending'
      },
      {
        id: 'component-activation',
        name: '组件激活',
        status: 'pending'
      },
      {
        id: 'event-binding',
        name: '事件绑定',
        status: 'pending'
      }
    ]

    // 添加自定义步骤
    const customSteps: HydrationStep[] = this.config.customSteps?.map(step => ({
      id: step.id,
      name: step.name,
      status: 'pending' as const
    })) ?? []

    return [...baseSteps, ...customSteps]
  }

  /**
   * 开始hydration过程
   */
  async startHydration(ssrData?: SSRData): Promise<void> {
    this.debug('开始hydration过程', ssrData)
    this.mark('hydration-start')

    try {
      this.updateState({
        phase: HydrationPhase.STARTING,
        startTime: Date.now(),
        error: null
      })

      // 执行hydration步骤
      await this.executeSteps(ssrData)

      // 完成hydration
      this.updateState({
        phase: HydrationPhase.COMPLETED,
        duration: Date.now() - this.state.startTime,
        progress: 100
      })

      this.mark('hydration-complete')
      this.debug('Hydration完成', this.getPerformanceMetrics())

    } catch (error) {
      const hydrationError = error instanceof Error ? error : new Error('Hydration失败')
      
      this.updateState({
        phase: HydrationPhase.FAILED,
        duration: Date.now() - this.state.startTime,
        error: hydrationError
      })

      this.debug('Hydration失败', hydrationError)
      throw hydrationError
    }
  }

  /**
   * 执行hydration步骤
   */
  private async executeSteps(ssrData?: SSRData): Promise<void> {
    for (const step of this.state.steps) {
      try {
        await this.executeStep(step, ssrData)
      } catch (error) {
        const stepError = error instanceof Error ? error : new Error(`步骤${step.name}失败`)
        
        this.updateStepStatus(step.id, 'failed', stepError)

        if (this.config.errorStrategy === 'abort') {
          throw stepError
        } else if (this.config.errorStrategy === 'retry') {
          // TODO: 实现重试逻辑
          throw stepError
        }
        // continue策略会继续执行下一步
      }
    }
  }

  /**
   * 执行单个hydration步骤
   */
  private async executeStep(step: HydrationStep, ssrData?: SSRData): Promise<void> {
    this.updateStepStatus(step.id, 'running')
    this.mark(`step-${step.id}-start`)

    switch (step.id) {
      case 'client-ready':
        await this.checkClientReady()
        break
        
      case 'data-sync':
        await this.syncData(ssrData)
        break
        
      case 'store-hydration':
        await this.hydrateStores(ssrData)
        break
        
      case 'component-activation':
        await this.activateComponents()
        break
        
      case 'event-binding':
        await this.bindEvents()
        break
        
      default:
        // 执行自定义步骤
        const customStep = this.config.customSteps?.find(s => s.id === step.id)
        if (customStep) {
          await customStep.handler()
        }
        break
    }

    this.updateStepStatus(step.id, 'completed')
    this.mark(`step-${step.id}-complete`)
    
    // 更新整体进度
    const completedSteps = this.state.steps.filter(s => s.status === 'completed').length
    const progress = (completedSteps / this.state.steps.length) * 100
    this.updateState({ progress })
  }

  /**
   * 检查客户端环境就绪
   */
  private async checkClientReady(): Promise<void> {
    if (typeof window === 'undefined') {
      throw new Error('客户端环境未就绪')
    }

    // 等待DOM完全加载
    if (document.readyState !== 'complete') {
      await new Promise(resolve => {
        const handler = () => {
          if (document.readyState === 'complete') {
            document.removeEventListener('readystatechange', handler)
            resolve(void 0)
          }
        }
        document.addEventListener('readystatechange', handler)
      })
    }

    this.debug('客户端环境就绪')
  }

  /**
   * 数据同步
   */
  private async syncData(ssrData?: SSRData): Promise<void> {
    this.updateState({ phase: HydrationPhase.SYNCING })

    if (!ssrData || !this.config.dataSync) {
      this.debug('跳过数据同步')
      return
    }

    const { syncMessages, syncSessions, timeout } = this.config.dataSync

    const syncPromises: Promise<void>[] = []

    if (syncMessages && ssrData.messages) {
      syncPromises.push(this.syncMessages(ssrData.messages))
    }

    if (syncSessions && ssrData.session) {
      syncPromises.push(this.syncSession(ssrData.session))
    }

    // 设置超时
    const syncPromise = Promise.all(syncPromises)
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('数据同步超时')), timeout)
    })

    await Promise.race([syncPromise, timeoutPromise])
    this.debug('数据同步完成')
  }

  /**
   * 同步消息数据
   */
  private async syncMessages(messages: ClientMessage[]): Promise<void> {
    try {
      // 使用统一的聊天store，确保在客户端环境下导入
      const { useUnifiedChatStore } = await import('@/stores/unified-chat-store')
      const store = useUnifiedChatStore.getState()

      // 按会话分组并批量添加
      const messagesBySession = messages.reduce((acc, message) => {
        const sessionId = message.sessionId
        if (!acc[sessionId]) {
          acc[sessionId] = []
        }
        acc[sessionId].push(message)
        return acc
      }, {} as Record<string, ClientMessage[]>)

      Object.entries(messagesBySession).forEach(([sessionId, sessionMessages]) => {
        store.addMessages(sessionId, sessionMessages)
      })

      this.debug(`同步了${messages.length}条消息`)
    } catch (error) {
      this.debug('消息同步失败', error)
      throw error
    }
  }

  /**
   * 同步会话数据
   */
  private async syncSession(session: ChatSession): Promise<void> {
    try {
      const { useSessionStore } = await import('@/stores/session-store')
      const store = useSessionStore.getState()

      store.setCurrentSession(session.sessionId)
      this.debug('会话同步完成', session.sessionId)
    } catch (error) {
      this.debug('会话同步失败', error)
      throw error
    }
  }

  /**
   * 恢复Store状态
   */
  private async hydrateStores(ssrData?: SSRData): Promise<void> {
    // 这里可以实现更复杂的store状态恢复逻辑
    // 例如：恢复UI状态、用户偏好设置等
    this.debug('Store状态恢复完成')
  }

  /**
   * 激活组件
   */
  private async activateComponents(): Promise<void> {
    this.updateState({ phase: HydrationPhase.ACTIVATING })
    
    // 等待一个微任务周期，确保所有组件都已挂载
    await new Promise(resolve => setTimeout(resolve, 0))
    this.debug('组件激活完成')
  }

  /**
   * 绑定事件
   */
  private async bindEvents(): Promise<void> {
    // 这里可以绑定全局事件监听器
    // 例如：键盘快捷键、窗口事件等
    this.debug('事件绑定完成')
  }

  /**
   * 更新状态
   */
  private updateState(updates: Partial<HydrationState>): void {
    this.state = { ...this.state, ...updates }
    this.notifyListeners()
  }

  /**
   * 更新步骤状态
   */
  private updateStepStatus(
    stepId: string, 
    status: HydrationStep['status'], 
    error?: Error
  ): void {
    const steps = this.state.steps.map(step => {
      if (step.id === stepId) {
        const now = Date.now()
        return {
          ...step,
          status,
          startTime: step.startTime || (status === 'running' ? now : step.startTime),
          endTime: status === 'completed' || status === 'failed' ? now : undefined,
          error
        }
      }
      return step
    })

    this.updateState({ steps })
  }

  /**
   * 通知监听器
   */
  private notifyListeners(): void {
    this.listeners.forEach(listener => listener(this.state))
  }

  /**
   * 添加状态监听器
   */
  addListener(listener: (state: HydrationState) => void): () => void {
    this.listeners.add(listener)
    return () => this.listeners.delete(listener)
  }

  /**
   * 获取当前状态
   */
  getState(): HydrationState {
    return { ...this.state }
  }

  /**
   * 获取性能指标
   */
  getPerformanceMetrics(): Record<string, number> {
    const metrics: Record<string, number> = {}
    
    this.performanceMarks.forEach((time, name) => {
      metrics[name] = time
    })

    return metrics
  }

  /**
   * 性能标记
   */
  private mark(name: string): void {
    if (this.config.enablePerformanceMonitoring) {
      this.performanceMarks.set(name, performance.now())
    }
  }

  /**
   * 调试日志
   */
  private debug(message: string, data?: any): void {
    if (this.config.enableDebug) {
      console.log(`🔄 HydrationManager: ${message}`, data)
    }
  }
}

// ============================================================================
// React Hooks
// ============================================================================

/**
 * 使用hydration管理器的Hook
 */
export function useHydrationManager(config?: HydrationConfig) {
  const managerRef = useRef<HydrationManager>()
  const [state, setState] = useState<HydrationState>()

  // 初始化管理器
  if (!managerRef.current) {
    managerRef.current = new HydrationManager(config)
  }

  useEffect(() => {
    const manager = managerRef.current!
    
    // 添加状态监听器
    const unsubscribe = manager.addListener(setState)
    
    // 设置初始状态
    setState(manager.getState())

    return unsubscribe
  }, [])

  const startHydration = useCallback((ssrData?: SSRData) => {
    return managerRef.current!.startHydration(ssrData)
  }, [])

  const getPerformanceMetrics = useCallback(() => {
    return managerRef.current!.getPerformanceMetrics()
  }, [])

  return {
    state,
    startHydration,
    getPerformanceMetrics,
    manager: managerRef.current!
  }
}

/**
 * 简化的hydration状态Hook
 */
export function useHydrationState() {
  const [isHydrated, setIsHydrated] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    const manager = new HydrationManager({
      enableDebug: false,
      enablePerformanceMonitoring: false
    })

    const unsubscribe = manager.addListener((state) => {
      if (state.phase === HydrationPhase.COMPLETED) {
        setIsHydrated(true)
      } else if (state.phase === HydrationPhase.FAILED) {
        setError(state.error)
      }
    })

    // 自动开始hydration
    manager.startHydration().catch(setError)

    return unsubscribe
  }, [])

  return { isHydrated, error }
}

// ============================================================================
// 工具函数
// ============================================================================

/**
 * 创建SSR数据校验和
 */
export function createSSRChecksum(data: SSRData): string {
  const content = JSON.stringify(data, Object.keys(data).sort())
  
  // 简单的hash函数
  let hash = 0
  for (let i = 0; i < content.length; i++) {
    const char = content.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash // 转换为32位整数
  }
  
  return hash.toString(36)
}

/**
 * 验证SSR数据完整性
 */
export function validateSSRData(data: SSRData): boolean {
  if (!data.checksum) {
    return false
  }

  const expectedChecksum = createSSRChecksum({
    ...data,
    checksum: undefined
  })

  return data.checksum === expectedChecksum
}

export default HydrationManager