/**
 * 消息管理 Store
 *
 * 功能：WebSocket消息处理、streaming消息累积、消息CRUD操作
 * 依赖：BaseWebSocketMessage、各种MessagePayload类型、ConnectionStore
 * 性能：优化了streaming消息处理，减少不必要的重渲染
 *
 * 数据流：WebSocket -> MessageProcessor -> Store -> UI Components
 */

import { create } from 'zustand'
import { immer } from 'zustand/middleware/immer'
import { subscribeWithSelector } from 'zustand/middleware'
import { useMemo } from 'react'

import type {
  BaseWebSocketMessage,
  MessagePayload,
  StreamingPayload,
  CheckpointPayload,
  ReportPayload,
  ErrorPayload,
  FormField,
} from '@/types/websocket-event-type'

import {
  isStreamingPayload,
  isCheckpointPayload,
  isReportPayload,
  isErrorPayload,
} from '@/types/websocket-event-type'

import { useConnectionStore } from './connection-store'
import { useSessionStore } from './session-store'

// ============================================================================
// Message State Interface
// ============================================================================

export interface MessageState {
  // 消息处理状态
  isProcessingMessage: boolean
  lastProcessedMessageId: string | undefined

  // 批处理状态
  pendingMessages: BaseWebSocketMessage[]
  processingBatch: boolean

  // 🎯 Pure WebSocket Flow：发送中消息队列
  pendingSentMessages: Array<{
    tempId: string
    content: string
    timestamp: number
    sessionId: string
    userId: string
  }>
  isSendingMessage: boolean
}

// ============================================================================
// Message Actions Interface
// ============================================================================

export interface MessageActions {
  // 核心消息处理
  handleIncomingMessage: (message: BaseWebSocketMessage) => void
  processMessageBatch: () => void

  // 消息类型处理器
  handleStreamingMessage: (message: BaseWebSocketMessage) => void
  handleCheckpointMessage: (message: BaseWebSocketMessage) => void
  handleReportMessage: (message: BaseWebSocketMessage) => void
  handleErrorMessage: (message: BaseWebSocketMessage) => void

  // 消息CRUD操作
  addMessage: (message: BaseWebSocketMessage, sessionId?: string) => void
  updateMessage: (
    messageId: string,
    updates: Partial<BaseWebSocketMessage>,
    sessionId?: string
  ) => void
  deleteMessage: (messageId: string, sessionId?: string) => void

  // 用户消息发送
  sendUserMessage: (content: string, sessionId?: string) => Promise<void>
  retryMessage: (messageId: string, sessionId?: string) => Promise<void>

  // 表单处理
  submitForm: (formId: string, values: Record<string, any>) => Promise<void>

  // 消息查询
  getMessageById: (messageId: string, sessionId?: string) => BaseWebSocketMessage | undefined
  getMessageByTimestamp: (timestamp: string, sessionId?: string) => BaseWebSocketMessage | undefined
  getAccumulatedStreamingText: (sessionId: string, beforeTimestamp?: string) => string

  // 统计信息
  getMessageStats: () => {
    totalMessages: number
    streamingMessages: number
    checkpointMessages: number
    reportMessages: number
    errorMessages: number
  }

  // 清理
  clearMessages: (sessionId?: string) => void

  // 🎯 Pure WebSocket Flow：发送队列管理
  getPendingSentMessages: (sessionId?: string) => Array<{
    tempId: string
    content: string
    timestamp: number
    sessionId: string
    userId: string
  }>
  clearPendingSentMessages: (sessionId?: string) => void
}

export type MessageStore = MessageState & MessageActions

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * 生成消息ID - 增强版，确保高并发下的唯一性
 */
let messageIdCounter = 0
const generateMessageId = (message?: BaseWebSocketMessage): string => {
  const timestamp = message?.timestamp ? message.timestamp.toString() : new Date().toISOString()
  const highPrecisionTime = performance.now().toString().replace('.', '')
  const random = Math.random().toString(36).substring(2, 15) // 增加随机字符串长度
  const counter = (++messageIdCounter).toString(36)
  const type = message?.payload.type || 'unknown'
  return `msg-${timestamp.toString().replace(/[:.]/g, '')}-${highPrecisionTime}-${type}-${random}-${counter}`
}

/**
 * 创建标准WebSocket消息
 */
const createStandardMessage = (
  sessionInfo: {
    groupChatId: string
    sessionId: string
    userId: string
    organizationId: string
  },
  payload: MessagePayload,
  options?: {
    id?: string
    timestamp?: string
    status?: string
    metadata?: any
  }
): BaseWebSocketMessage => {
  const timestamp = options?.timestamp || new Date().toISOString()
  const id = options?.id || generateMessageId()

  // 🐛 调试日志：追踪用户消息创建时的 userId 值
  console.log('🔍 DEBUG_createStandardMessage:', {
    source: 'message-store.ts',
    userId: sessionInfo.userId,
    groupChatId: sessionInfo.groupChatId,
    sessionId: sessionInfo.sessionId,
    organizationId: sessionInfo.organizationId,
    messageType: payload.type,
    messageId: id,
    timestamp,
  })

  const now = Date.now()
  return {
    id,
    groupChatId: sessionInfo.groupChatId,
    sessionId: sessionInfo.sessionId,
    userId: sessionInfo.userId,
    organizationId: sessionInfo.organizationId,
    payload,
    timestamp: typeof timestamp === 'string' ? now : timestamp,
    status: options?.status as 'sending' | 'sent' | 'delivered' | 'failed' || 'sent',
    metadata: options?.metadata || {},
    createdAt: now,
    updatedAt: now,
  }
}

/**
 * 创建streaming消息
 */
const createStreamingMessage = (
  sessionInfo: {
    groupChatId: string
    sessionId: string
    userId: string
    organizationId: string
  },
  content: string,
  isComplete: boolean = true,
  options?: {
    id?: string
    timestamp?: string
    status?: string
  }
): BaseWebSocketMessage => {
  const payload: StreamingPayload = {
    type: 'streaming',
    delta: content,
    isComplete,
  }

  return createStandardMessage(sessionInfo, payload, {
    ...options,
    status: options?.status || (isComplete ? 'delivered' : 'sending'),
    metadata: {
      streamingState: {
        isComplete,
        accumulatedText: content,
      },
    },
  })
}

/**
 * 创建checkpoint消息
 */
const createCheckpointMessage = (
  sessionInfo: {
    groupChatId: string
    sessionId: string
    userId: string
    organizationId: string
  },
  fields: FormField[],
  options?: {
    id?: string
    timestamp?: string
  }
): BaseWebSocketMessage => {
  const payload: CheckpointPayload = {
    type: 'checkpoint',
    fields,
  }

  return createStandardMessage(sessionInfo, payload, options)
}

/**
 * 累积streaming消息文本
 */
const accumulateStreamingText = (
  messages: BaseWebSocketMessage[],
  beforeTimestamp?: string
): string => {
  let accumulated = ''

  for (const message of messages) {
    if (beforeTimestamp && message.timestamp >= parseInt(beforeTimestamp)) {
      break
    }

    if (isStreamingPayload(message.payload)) {
      accumulated += message.payload.delta

      // 遇到isComplete=true的消息，表示一个完整回合结束
      if (message.payload.isComplete) {
        break
      }
    }
  }

  return accumulated
}

/**
 * 获取最后一个未完成的streaming消息
 */
const getLastIncompleteStreamingMessage = (
  messages: BaseWebSocketMessage[]
): BaseWebSocketMessage | null => {
  if (messages.length === 0) return null

  const lastMessage = messages[messages.length - 1]
  if (isStreamingPayload(lastMessage.payload) && !lastMessage.payload.isComplete) {
    return lastMessage
  }

  return null
}

/**
 * 🎯 新增：根据streamingGroupId查找同组的流式消息
 */
const findStreamingMessageByGroupId = (
  messages: BaseWebSocketMessage[],
  groupId: string
): BaseWebSocketMessage | null => {
  // 从后往前查找，获取同组中最新的消息
  for (let i = messages.length - 1; i >= 0; i--) {
    const message = messages[i]
    if (
      isStreamingPayload(message.payload) &&
      message.metadata?.streamingGroup?.groupId === groupId
    ) {
      return message
    }
  }
  return null
}

/**
 * 🎯 新增：获取streamingGroup中已累积的文本
 */
const getAccumulatedTextForGroup = (messages: BaseWebSocketMessage[], groupId: string): string => {
  let accumulatedText = ''

  // 按顺序累积同组的所有chunk
  const groupMessages = messages
    .filter(m => isStreamingPayload(m.payload) && m.metadata?.streamingGroup?.groupId === groupId)
    .sort((a, b) => {
      const indexA = a.metadata?.streamingGroup?.chunkIndex || 0
      const indexB = b.metadata?.streamingGroup?.chunkIndex || 0
      return indexA - indexB
    })

  for (const message of groupMessages) {
    if (isStreamingPayload(message.payload)) {
      accumulatedText += message.payload.delta
    }
  }

  return accumulatedText
}

// ============================================================================
// Message Store Implementation
// ============================================================================

export const useMessageStore = create<MessageStore>()(
  subscribeWithSelector(
    immer((set, get) => ({
      // ============================================================================
      // 初始状态
      // ============================================================================
      isProcessingMessage: false,
      lastProcessedMessageId: undefined,
      pendingMessages: [],
      processingBatch: false,

      // 🎯 Pure WebSocket Flow：初始状态
      pendingSentMessages: [],
      isSendingMessage: false,

      // ============================================================================
      // 核心消息处理
      // ============================================================================

      handleIncomingMessage: (message: BaseWebSocketMessage) => {
        console.log('📨 [Pure WebSocket Flow] ===== MESSAGE RECEIVED =====')
        console.log('📨 [Pure WebSocket Flow] Incoming message:', {
          messageId: message.id,
          userId: message.userId,
          sessionId: message.sessionId,
          payloadType: message.payload?.type,
          isAssistantMessage: message.userId === 'mock-assistant-bot',
          timestamp: new Date().toISOString(),
        })

        const sessionStore = useSessionStore.getState()
        const currentSession = sessionStore.currentSession
        const currentState = get()

        console.log('📨 [Pure WebSocket Flow] Session context:', {
          hasCurrentSession: !!currentSession,
          currentSessionId: currentSession?.sessionId,
          messageSessionId: message.sessionId,
          sessionMatch: currentSession?.sessionId === message.sessionId,
          pendingMessagesCount: currentState.pendingSentMessages.length,
        })

        // 🎯 核心增强：检查是否是从发送队列回流的消息
        const isPendingMessage = currentState.pendingSentMessages.find(
          pending => pending.tempId === message.id
        )

        if (isPendingMessage) {
          console.log('✨ [Pure WebSocket Flow] 检测到发送队列回流消息:', {
            messageId: message.id,
            content: isPendingMessage.content.substring(0, 20) + '...',
          })

          // 从发送队列中移除
          set(state => {
            state.pendingSentMessages = state.pendingSentMessages.filter(
              pending => pending.tempId !== message.id
            )
          })

          console.log(
            '🧹 [Pure WebSocket Flow] 已从发送队列移除，剩余队列长度:',
            currentState.pendingSentMessages.length - 1
          )
        }

        // 🎯 详细调试日志：消息流追踪
        if (process.env.NODE_ENV === 'development') {
          console.log('🔄 MessageStore.handleIncomingMessage - Entry:', {
            messageId: message.id,
            userId: message.userId,
            type: message.payload?.type,
            sessionId: message.sessionId,
            isStreaming: message.payload?.type === 'streaming',
            isComplete:
              message.payload?.type === 'streaming'
                ? (message.payload as StreamingPayload).isComplete
                : undefined,
            timestamp: message.timestamp,
          })
        }

        if (!message.payload || !currentSession) {
          console.warn('❌ MSG_REJECTED:', {
            reason: !message.payload ? 'no-payload' : 'no-session',
            hasSession: !!currentSession,
            sessionId: currentSession?.sessionId,
          })
          return
        }

        console.log('📨 MSG_RECEIVED:', {
          type: message.payload.type,
          sessionId: currentSession.sessionId,
          messageCount: currentSession.messages.length,
        })

        // 设置处理状态
        set(state => {
          state.isProcessingMessage = true
          state.lastProcessedMessageId = message.id
        })

        try {
          // 根据payload类型处理
          switch (message.payload.type) {
            case 'streaming':
              get().handleStreamingMessage(message)
              break
            case 'checkpoint':
              get().handleCheckpointMessage(message)
              break
            case 'report':
              get().handleReportMessage(message)
              break
            case 'error':
              get().handleErrorMessage(message)
              break
            default:
              console.warn('❌ Unknown message type:', message.payload)
          }
        } catch (error) {
          console.error('❌ Error processing message:', error)
        } finally {
          // 清除处理状态
          set(state => {
            state.isProcessingMessage = false
          })
        }
      },

      processMessageBatch: () => {
        const state = get()
        if (state.processingBatch || state.pendingMessages.length === 0) {
          return
        }

        set(state => {
          state.processingBatch = true
        })

        try {
          const messages = [...state.pendingMessages]
          set(state => {
            state.pendingMessages = []
          })

          messages.forEach(message => {
            get().handleIncomingMessage(message)
          })

          console.log('📦 Processed message batch:', messages.length)
        } catch (error) {
          console.error('❌ Error processing message batch:', error)
        } finally {
          set(state => {
            state.processingBatch = false
          })
        }
      },

      // ============================================================================
      // 消息类型处理器
      // ============================================================================

      handleStreamingMessage: (message: BaseWebSocketMessage) => {
        if (!isStreamingPayload(message.payload)) return

        const streamingPayload = message.payload
        const sessionStore = useSessionStore.getState()
        const currentSession = sessionStore.currentSession

        if (!currentSession) return

        // 🎯 获取streamingGroup信息
        const streamingGroup = message.metadata?.streamingGroup
        const groupId = streamingGroup?.groupId

        console.log('🌊 STREAM_PROCESS:', {
          delta: streamingPayload.delta?.substring(0, 10) + '...',
          isComplete: streamingPayload.isComplete,
          sessionId: currentSession.sessionId,
          groupId: groupId,
          chunkIndex: streamingGroup?.chunkIndex,
          isFirstChunk: streamingGroup?.isFirstChunk,
        })

        if (groupId) {
          // 🎯 核心修复：基于streamingGroupId的实时累积处理
          const existingGroupMessage = findStreamingMessageByGroupId(
            currentSession.messages,
            groupId
          )

          if (existingGroupMessage) {
            // 🎯 更新现有streaming消息（无论是否是第一个chunk）
            console.log('🔄 UPDATE_STREAMING_GROUP')

            const messageIndex = currentSession.messages.findIndex(
              m => m.id === existingGroupMessage.id
            )
            if (messageIndex !== -1) {
              // 计算累积文本：现有累积文本 + 新的delta
              const currentAccumulated =
                currentSession.messages[messageIndex].metadata?.streamingState?.accumulatedText ||
                ''
              const newAccumulatedText = currentAccumulated + streamingPayload.delta

              // 🎯 关键修复：更新消息内容，使用累积文本作为delta显示
              const updatedMessage: BaseWebSocketMessage = {
                ...currentSession.messages[messageIndex],
                payload: {
                  type: 'streaming',
                  delta: newAccumulatedText, // 🎯 使用累积文本作为显示内容
                  isComplete: streamingPayload.isComplete,
                } as StreamingPayload,
                metadata: {
                  ...currentSession.messages[messageIndex].metadata,
                  streamingState: {
                    isComplete: streamingPayload.isComplete,
                    accumulatedText: newAccumulatedText,
                  },
                  streamingGroup: streamingGroup,
                },
                status: streamingPayload.isComplete ? 'delivered' : 'sending',
                timestamp: message.timestamp,
              }

              get().updateMessage(existingGroupMessage.id, updatedMessage, currentSession.sessionId)

              console.log('📝 GROUP_MSG_UPDATED:', {
                groupId: groupId,
                chunkIndex: streamingGroup?.chunkIndex,
                accumulated: newAccumulatedText?.substring(0, 30) + '...',
                totalLength: newAccumulatedText?.length,
                isComplete: streamingPayload.isComplete,
              })

              // 🎯 重要：不添加新消息到列表，只更新现有消息
              return
            }
          }

          // 🎯 只有在没有现有组消息时才创建新消息（第一个chunk）
          if (streamingGroup?.isFirstChunk) {
            console.log('✨ CREATE_NEW_GROUP (first chunk)')
            // 创建第一个消息，使用delta作为初始累积文本
            const initialMessage: BaseWebSocketMessage = {
              ...message,
              metadata: {
                ...message.metadata,
                streamingState: {
                  isComplete: streamingPayload.isComplete,
                  accumulatedText: streamingPayload.delta,
                },
              },
            }
            get().addMessage(initialMessage, currentSession.sessionId)
          } else {
            console.warn('⚠️ 收到非首个chunk但找不到现有组消息:', {
              groupId,
              chunkIndex: streamingGroup?.chunkIndex,
            })
          }
        } else {
          // 🎯 fallback：使用原有逻辑处理没有groupId的消息
          const lastIncompleteMessage = getLastIncompleteStreamingMessage(currentSession.messages)

          if (lastIncompleteMessage) {
            // 更新现有streaming消息
            console.log('🔄 UPDATE_EXISTING_LEGACY')

            const messageIndex = currentSession.messages.findIndex(
              m => m.id === lastIncompleteMessage.id
            )
            if (messageIndex !== -1) {
              const existingAccumulated =
                currentSession.messages[messageIndex].metadata?.streamingState?.accumulatedText ||
                ''
              const newAccumulated = existingAccumulated + streamingPayload.delta

              // 更新消息
              const updatedMessage: BaseWebSocketMessage = {
                ...currentSession.messages[messageIndex],
                payload: {
                  ...streamingPayload,
                  delta: streamingPayload.delta,
                },
                metadata: {
                  ...currentSession.messages[messageIndex].metadata,
                  streamingState: {
                    isComplete: streamingPayload.isComplete,
                    accumulatedText: newAccumulated,
                  },
                },
                status: streamingPayload.isComplete ? 'delivered' : 'sending',
                timestamp: message.timestamp,
              }

              get().updateMessage(
                lastIncompleteMessage.id,
                updatedMessage,
                currentSession.sessionId
              )

              console.log('📝 MSG_UPDATED_LEGACY:', {
                accumulated: newAccumulated?.substring(0, 20) + '...',
                isComplete: streamingPayload.isComplete,
              })
            }
          } else {
            // 创建新的streaming消息
            console.log('✨ CREATE_NEW_LEGACY')
            get().addMessage(message, currentSession.sessionId)
          }
        }
      },

      handleCheckpointMessage: (message: BaseWebSocketMessage) => {
        console.log('📋 Processing checkpoint message')
        const sessionStore = useSessionStore.getState()
        get().addMessage(message, sessionStore.currentSession?.sessionId)
      },

      handleReportMessage: (message: BaseWebSocketMessage) => {
        console.log('📊 Processing report message')
        const sessionStore = useSessionStore.getState()
        get().addMessage(message, sessionStore.currentSession?.sessionId)
      },

      handleErrorMessage: (message: BaseWebSocketMessage) => {
        console.log('❌ Processing error message')
        const sessionStore = useSessionStore.getState()
        get().addMessage(message, sessionStore.currentSession?.sessionId)
      },

      // ============================================================================
      // 消息CRUD操作
      // ============================================================================

      addMessage: (message: BaseWebSocketMessage, sessionId?: string) => {
        const sessionStore = useSessionStore.getState()
        const targetSessionId = sessionId || sessionStore.currentSession?.sessionId

        if (!targetSessionId) {
          console.warn('❌ No target session for adding message')
          return
        }

        const session = sessionStore.sessions[targetSessionId]
        if (!session) {
          console.warn('❌ Session not found:', targetSessionId)
          return
        }

        // 确保消息有唯一ID
        const processedMessage = { ...message }
        if (!processedMessage.id) {
          processedMessage.id = generateMessageId(message)
        }

        // 如果是streaming消息，确保metadata中有累积文本
        if (isStreamingPayload(message.payload)) {
          processedMessage.metadata = {
            ...message.metadata,
            streamingState: {
              isComplete: message.payload.isComplete,
              accumulatedText: message.payload.delta,
            },
          }
          processedMessage.status = message.payload.isComplete ? 'delivered' : 'sending'
        }

        // 更新会话中的消息
        useSessionStore.setState(state => {
          const targetSession = state.sessions[targetSessionId]
          if (targetSession) {
            targetSession.messages.push(processedMessage)
            targetSession.updatedAt = new Date()

            // 如果是当前会话，同步更新
            if (state.currentSession?.sessionId === targetSessionId) {
              state.currentSession = targetSession
            }
          }
        })

        console.log('✅ MSG_ADDED:', {
          type: message.payload.type,
          sessionId: targetSessionId,
          newCount: session.messages.length + 1,
        })
      },

      updateMessage: (
        messageId: string,
        updates: Partial<BaseWebSocketMessage>,
        sessionId?: string
      ) => {
        const sessionStore = useSessionStore.getState()
        const targetSessionId = sessionId || sessionStore.currentSession?.sessionId

        if (!targetSessionId) {
          console.warn('❌ No target session for updating message')
          return
        }

        useSessionStore.setState(state => {
          const session = state.sessions[targetSessionId]
          if (session) {
            const messageIndex = session.messages.findIndex(m => m.id === messageId)
            if (messageIndex !== -1) {
              const currentMessage = session.messages[messageIndex]

              // 更新基本属性
              Object.keys(updates).forEach(key => {
                if (key !== 'metadata') {
                  ;(currentMessage as any)[key] = (updates as any)[key]
                }
              })

              // 安全更新metadata
              if (updates.metadata) {
                if (!currentMessage.metadata) {
                  currentMessage.metadata = {}
                }
                Object.assign(currentMessage.metadata, updates.metadata)
              }

              session.updatedAt = new Date()

              // 如果是当前会话，同步更新
              if (state.currentSession?.sessionId === targetSessionId) {
                state.currentSession = session
              }
            }
          }
        })
      },

      deleteMessage: (messageId: string, sessionId?: string) => {
        const sessionStore = useSessionStore.getState()
        const targetSessionId = sessionId || sessionStore.currentSession?.sessionId

        if (!targetSessionId) {
          console.warn('❌ No target session for deleting message')
          return
        }

        useSessionStore.setState(state => {
          const session = state.sessions[targetSessionId]
          if (session) {
            const messageIndex = session.messages.findIndex(m => m.id === messageId)
            if (messageIndex !== -1) {
              session.messages.splice(messageIndex, 1)
              session.updatedAt = new Date()

              // 如果是当前会话，同步更新
              if (state.currentSession?.sessionId === targetSessionId) {
                state.currentSession = session
              }

              console.log('🗑️ MSG_DELETED:', { messageId, sessionId: targetSessionId })
            }
          }
        })
      },

      // ============================================================================
      // 用户消息发送
      // ============================================================================

      sendUserMessage: async (content: string, sessionId?: string) => {
        console.log('🚀 [Pure WebSocket Flow] sendUserMessage called:', {
          content: content.substring(0, 20) + '...',
          sessionId,
          timestamp: new Date().toISOString(),
        })

        const connectionStore = useConnectionStore.getState()
        const sessionStore = useSessionStore.getState()
        const currentState = get()

        const { connectionManager } = connectionStore
        const targetSession = sessionId
          ? sessionStore.sessions[sessionId]
          : sessionStore.currentSession

        if (!connectionManager || !targetSession) {
          throw new Error('Connection or session not available')
        }

        const trimmedContent = content.trim()
        if (!trimmedContent) {
          throw new Error('Message content cannot be empty')
        }

        // 🎯 防重复发送检查
        if (currentState.isSendingMessage) {
          console.warn('🚫 [Pure WebSocket Flow] 消息正在发送中，忽略重复请求')
          return
        }

        // 创建用户消息（但不立即添加到消息列表）
        const userMessage = createStreamingMessage(
          {
            groupChatId: targetSession.groupChatId,
            sessionId: targetSession.sessionId,
            userId: targetSession.userId,
            organizationId: targetSession.organizationId,
          },
          trimmedContent,
          true,
          {
            status: 'sending',
          }
        )

        // 🎯 核心改变：添加到发送中队列，而不是直接添加到消息列表
        set(state => {
          state.isSendingMessage = true
          state.pendingSentMessages.push({
            tempId: userMessage.id,
            content: trimmedContent,
            timestamp: Date.now(),
            sessionId: targetSession.sessionId,
            userId: targetSession.userId,
          })
        })

        console.log('📤 [Pure WebSocket Flow] 消息添加到发送队列:', {
          messageId: userMessage.id,
          content: trimmedContent.substring(0, 20) + '...',
          queueLength: currentState.pendingSentMessages.length + 1,
        })

        try {
          // 发送到WebSocket，等待回流后统一添加到消息列表
          await connectionManager.sendMessage(userMessage)

          console.log('✅ [Pure WebSocket Flow] 消息已发送，等待WebSocket回流')
        } catch (error) {
          console.error('❌ [Pure WebSocket Flow] 发送失败:', error)

          // 🎯 发送失败时从队列中移除
          set(state => {
            state.pendingSentMessages = state.pendingSentMessages.filter(
              msg => msg.tempId !== userMessage.id
            )
          })

          throw error
        } finally {
          // 🎯 解除发送状态锁
          set(state => {
            state.isSendingMessage = false
          })
        }
      },

      retryMessage: async (messageId: string, sessionId?: string) => {
        const sessionStore = useSessionStore.getState()
        const targetSessionId = sessionId || sessionStore.currentSession?.sessionId

        if (!targetSessionId) {
          throw new Error('Session not found')
        }

        const session = sessionStore.sessions[targetSessionId]
        if (!session) {
          throw new Error('Session not found')
        }

        const message = session.messages.find(m => m.id === messageId)
        if (!message) {
          throw new Error('Message not found')
        }

        // 只能重试失败的消息
        if (message.status !== 'failed') {
          return
        }

        try {
          // 只允许重试streaming类型消息
          if (message.payload.type === 'streaming') {
            const content = (message.payload as StreamingPayload).delta || ''
            await get().sendUserMessage(content, targetSessionId)
          }

          // 删除原失败消息
          get().deleteMessage(messageId, targetSessionId)
        } catch (error) {
          console.error('❌ Failed to retry message:', error)
          throw error
        }
      },

      // ============================================================================
      // 表单处理
      // ============================================================================

      submitForm: async (formId: string, values: Record<string, any>) => {
        const connectionStore = useConnectionStore.getState()
        const sessionStore = useSessionStore.getState()

        const { connectionManager } = connectionStore
        const currentSession = sessionStore.currentSession

        if (!connectionManager || !currentSession) {
          throw new Error('Required services not available')
        }

        try {
          // 构建表单字段
          const fields: FormField[] = Object.entries(values).map(([key, value]) => {
            let fieldType: FormField['type'] = 'text'

            if (typeof value === 'number') {
              fieldType = 'number'
            } else if (typeof value === 'boolean') {
              fieldType = 'checkbox'
            } else if (Array.isArray(value)) {
              fieldType = 'multiselect'
            } else if (value instanceof Date) {
              fieldType = 'date'
            } else if (typeof value === 'string' && value.length > 100) {
              fieldType = 'textarea'
            }

            return {
              id: key,
              label: key.charAt(0).toUpperCase() + key.slice(1),
              type: fieldType,
              required: false,
              defaultValue: value,
              ...(Array.isArray(value) && {
                options: value.map(item => ({
                  label: String(item),
                  value: item,
                })),
              }),
            }
          })

          // 创建checkpoint消息
          const checkpointMessage = createCheckpointMessage(
            {
              groupChatId: currentSession.groupChatId,
              sessionId: currentSession.sessionId,
              userId: currentSession.userId,
              organizationId: currentSession.organizationId,
            },
            fields
          )

          await connectionManager.sendMessage(checkpointMessage)
          console.log('✅ Form submitted:', formId)
        } catch (error) {
          console.error('❌ Failed to submit form:', error)
          throw error
        }
      },

      // ============================================================================
      // 消息查询
      // ============================================================================

      getMessageById: (messageId: string, sessionId?: string) => {
        const sessionStore = useSessionStore.getState()
        const targetSessionId = sessionId || sessionStore.currentSession?.sessionId

        if (!targetSessionId) return undefined

        const session = sessionStore.sessions[targetSessionId]
        return session?.messages.find(m => m.id === messageId)
      },

      getMessageByTimestamp: (timestamp: string, sessionId?: string) => {
        const sessionStore = useSessionStore.getState()
        const targetSessionId = sessionId || sessionStore.currentSession?.sessionId

        if (!targetSessionId) return undefined

        const session = sessionStore.sessions[targetSessionId]
        return session?.messages.find(m => m.timestamp === parseInt(timestamp))
      },

      getAccumulatedStreamingText: (sessionId: string, beforeTimestamp?: string) => {
        const sessionStore = useSessionStore.getState()
        const session = sessionStore.sessions[sessionId]
        if (!session) return ''

        return accumulateStreamingText(session.messages, beforeTimestamp)
      },

      // ============================================================================
      // 统计信息
      // ============================================================================

      getMessageStats: () => {
        const sessionStore = useSessionStore.getState()
        let totalMessages = 0
        let streamingMessages = 0
        let checkpointMessages = 0
        let reportMessages = 0
        let errorMessages = 0

        Object.values(sessionStore.sessions).forEach(session => {
          totalMessages += session.messages.length
          session.messages.forEach(message => {
            switch (message.payload.type) {
              case 'streaming':
                streamingMessages++
                break
              case 'checkpoint':
                checkpointMessages++
                break
              case 'report':
                reportMessages++
                break
              case 'error':
                errorMessages++
                break
            }
          })
        })

        return {
          totalMessages,
          streamingMessages,
          checkpointMessages,
          reportMessages,
          errorMessages,
        }
      },

      // ============================================================================
      // 清理
      // ============================================================================

      clearMessages: (sessionId?: string) => {
        if (sessionId) {
          // 清理指定会话的消息
          useSessionStore.setState(state => {
            const session = state.sessions[sessionId]
            if (session) {
              session.messages = []
              session.updatedAt = new Date()

              // 如果是当前会话，同步更新
              if (state.currentSession?.sessionId === sessionId) {
                state.currentSession = session
              }
            }
          })
          console.log('🗑️ Messages cleared for session:', sessionId)
        } else {
          // 清理所有消息
          useSessionStore.setState(state => {
            Object.values(state.sessions).forEach(session => {
              session.messages = []
              session.updatedAt = new Date()
            })

            if (state.currentSession) {
              state.currentSession.messages = []
            }
          })
          console.log('🗑️ All messages cleared')
        }
      },

      // ============================================================================
      // Pure WebSocket Flow：发送队列管理
      // ============================================================================

      getPendingSentMessages: (sessionId?: string) => {
        const currentState = get()
        if (sessionId) {
          return currentState.pendingSentMessages.filter(msg => msg.sessionId === sessionId)
        }
        return currentState.pendingSentMessages
      },

      clearPendingSentMessages: (sessionId?: string) => {
        set(state => {
          if (sessionId) {
            state.pendingSentMessages = state.pendingSentMessages.filter(
              msg => msg.sessionId !== sessionId
            )
            console.log('🧹 [Pure WebSocket Flow] 清理指定会话的发送队列:', sessionId)
          } else {
            state.pendingSentMessages = []
            console.log('🧹 [Pure WebSocket Flow] 清理全部发送队列')
          }
        })
      },
    }))
  )
)

// ============================================================================
// Message Hooks
// ============================================================================

export const useMessages = (sessionId?: string) => {
  const sessionStore = useSessionStore.getState()
  const targetSessionId = sessionId || sessionStore.currentSession?.sessionId

  const session = useSessionStore(state =>
    targetSessionId ? state.sessions[targetSessionId] : state.currentSession
  )

  const messages = session?.messages || []

  const addMessage = useMessageStore(state => state.addMessage)
  const updateMessage = useMessageStore(state => state.updateMessage)
  const deleteMessage = useMessageStore(state => state.deleteMessage)
  const sendUserMessage = useMessageStore(state => state.sendUserMessage)
  const getAccumulatedStreamingText = useMessageStore(state => state.getAccumulatedStreamingText)

  // 🎯 Pure WebSocket Flow：发送队列相关
  const getPendingSentMessages = useMessageStore(state => state.getPendingSentMessages)
  const clearPendingSentMessages = useMessageStore(state => state.clearPendingSentMessages)

  // 🚨 修复无限循环：使用原始数据和useMemo缓存计算结果
  const allPendingSentMessages = useMessageStore(state => state.pendingSentMessages)
  const pendingSentMessages = useMemo(() => {
    if (!targetSessionId) return []
    return allPendingSentMessages.filter(msg => msg.sessionId === targetSessionId)
  }, [allPendingSentMessages, targetSessionId])

  return {
    messages,
    addMessage,
    updateMessage,
    deleteMessage,
    sendUserMessage,
    getAccumulatedStreamingText,
    // Pure WebSocket Flow
    pendingSentMessages,
    getPendingSentMessages,
    clearPendingSentMessages,
  }
}

// 清理函数
export const cleanupMessageStore = () => {
  const { clearMessages } = useMessageStore.getState()
  clearMessages()
  console.log('🗑️ Message Store 清理完成')
}
